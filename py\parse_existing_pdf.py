#!/usr/bin/env python3
"""
Parse Existing PDF using borb
Extracts structure and content from the Norwegian employment contract PDF
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from decimal import Decimal

from borb.pdf import Document
from borb.pdf import PDF
from borb.pdf.canvas.layout.page_layout.multi_column_layout import SingleColumnLayout
from borb.pdf.canvas.layout.text.paragraph import Paragraph
from borb.pdf.canvas.layout.layout_element import LayoutElement


class PDFParser:
    """Parse and extract content from PDF files using borb"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.document = None
        self.pages = []
        self.extracted_content = {}
        
    def load_pdf(self) -> bool:
        """Load the PDF document"""
        try:
            if not Path(self.pdf_path).exists():
                print(f"Error: PDF file not found: {self.pdf_path}")
                return False
            
            with open(self.pdf_path, "rb") as pdf_file:
                self.document = PDF.loads(pdf_file)
            
            print(f"✓ Successfully loaded PDF: {self.pdf_path}")
            return True
            
        except Exception as e:
            print(f"Error loading PDF: {e}")
            return False
    
    def get_document_metadata(self) -> Dict[str, Any]:
        """Extract document metadata"""
        metadata = {
            "file_path": self.pdf_path,
            "file_size": Path(self.pdf_path).stat().st_size,
            "pages_accessible": 0,
            "document_info": {}
        }
        
        if not self.document:
            return metadata
        
        # Try to access pages to count them
        page_count = 0
        try:
            page_index = 0
            while True:
                try:
                    page = self.document.get_page(page_index)
                    page_count += 1
                    page_index += 1
                except:
                    break
        except:
            pass
        
        metadata["pages_accessible"] = page_count
        
        # Try to get document info
        try:
            if hasattr(self.document, 'get_document_info'):
                doc_info = self.document.get_document_info()
                if doc_info:
                    metadata["document_info"] = dict(doc_info)
        except Exception as e:
            print(f"Could not extract document info: {e}")
        
        return metadata
    
    def extract_page_content(self, page_index: int = 0) -> Dict[str, Any]:
        """Extract content from a specific page"""
        page_content = {
            "page_number": page_index + 1,
            "text_elements": [],
            "layout_elements": [],
            "dimensions": {},
            "fonts_used": set(),
            "extraction_method": "borb_basic"
        }
        
        if not self.document:
            return page_content
        
        try:
            page = self.document.get_page(page_index)
            
            # Get page dimensions
            try:
                # Try different methods to get page dimensions
                if hasattr(page, 'get_page_info'):
                    page_info = page.get_page_info()
                    page_content["dimensions"] = {
                        "width": float(page_info.get("width", 595)),
                        "height": float(page_info.get("height", 842))
                    }
                else:
                    # Default A4 dimensions
                    page_content["dimensions"] = {"width": 595.0, "height": 842.0}
            except:
                page_content["dimensions"] = {"width": 595.0, "height": 842.0}
            
            # Try to extract text content using different approaches
            text_content = self._extract_text_content(page)
            page_content["text_elements"] = text_content
            
            print(f"✓ Extracted content from page {page_index + 1}")
            
        except Exception as e:
            print(f"Error extracting content from page {page_index + 1}: {e}")
            page_content["error"] = str(e)
        
        return page_content
    
    def _extract_text_content(self, page) -> List[Dict[str, Any]]:
        """Extract text content from a page using various methods"""
        text_elements = []
        
        # Method 1: Try to get page contents directly
        try:
            if hasattr(page, 'get_contents'):
                contents = page.get_contents()
                if contents:
                    text_elements.append({
                        "method": "get_contents",
                        "content": str(contents)[:500] + "..." if len(str(contents)) > 500 else str(contents),
                        "full_length": len(str(contents))
                    })
        except Exception as e:
            print(f"Method 1 failed: {e}")
        
        # Method 2: Try to access page resources
        try:
            if hasattr(page, 'get_resources'):
                resources = page.get_resources()
                if resources:
                    text_elements.append({
                        "method": "get_resources",
                        "content": str(resources)[:200] + "..." if len(str(resources)) > 200 else str(resources),
                        "resource_info": "Page resources extracted"
                    })
        except Exception as e:
            print(f"Method 2 failed: {e}")
        
        # Method 3: Try to get page dictionary
        try:
            if hasattr(page, '__dict__'):
                page_dict = page.__dict__
                text_elements.append({
                    "method": "page_dict",
                    "content": f"Page object has {len(page_dict)} attributes",
                    "attributes": list(page_dict.keys())
                })
        except Exception as e:
            print(f"Method 3 failed: {e}")
        
        return text_elements
    
    def parse_all_pages(self) -> Dict[str, Any]:
        """Parse all accessible pages in the document"""
        if not self.document:
            return {"error": "Document not loaded"}
        
        all_content = {
            "metadata": self.get_document_metadata(),
            "pages": [],
            "summary": {}
        }
        
        # Extract content from all accessible pages
        page_count = all_content["metadata"]["pages_accessible"]
        
        for page_index in range(page_count):
            page_content = self.extract_page_content(page_index)
            all_content["pages"].append(page_content)
        
        # Create summary
        all_content["summary"] = {
            "total_pages_processed": len(all_content["pages"]),
            "total_text_elements": sum(len(page.get("text_elements", [])) for page in all_content["pages"]),
            "extraction_successful": len(all_content["pages"]) > 0
        }
        
        return all_content
    
    def generate_recreation_code(self, content_data: Dict[str, Any]) -> str:
        """Generate Python code to recreate the PDF structure"""
        
        code_template = '''#!/usr/bin/env python3
"""
PDF Recreation Code
Generated from: {pdf_path}
Based on extracted structure and content
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor
from borb.pdf.canvas.layout.table import Table, TableCell, FlexibleColumnWidthTable

def recreate_norwegian_employment_contract():
    """Recreate the Norwegian employment contract PDF"""
    
    # Create document
    document = Document()
    
    # Define styling
    primary_color = HexColor("#000000")  # Black for official documents
    text_color = HexColor("#000000")
    
    # Page dimensions from original: {width} x {height} pts
    
    # Create pages based on original structure
    for page_num in range({total_pages}):
        page = Page()
        document.add_page(page)
        layout = SingleColumnLayout(page)
        
        if page_num == 0:
            # First page - Title and main content
            layout.add(Paragraph(
                "ARBEIDSAVTALE",
                font="Helvetica-Bold",
                font_size=Decimal(16),
                font_color=primary_color,
                margin_top=Decimal(20),
                margin_bottom=Decimal(20)
            ))
            
            # Add sections based on typical Norwegian employment contract
            sections = [
                ("1. ARBEIDSGIVERS OPPLYSNINGER", 
                 "Navn: [BEDRIFTSNAVN]\\n"
                 "Organisasjonsnummer: [ORG.NR]\\n"
                 "Adresse: [ADRESSE]"),
                
                ("2. ARBEIDSTAKERS OPPLYSNINGER",
                 "Navn: [NAVN]\\n"
                 "Fødselsnummer: [FØDSELSNUMMER]\\n"
                 "Adresse: [ADRESSE]"),
                
                ("3. ARBEIDSFORHOLD",
                 "Stillingstittel: [STILLING]\\n"
                 "Ansettelsesdato: [DATO]\\n"
                 "Prøvetid: [PRØVETID]"),
                
                ("4. ARBEIDSSTED",
                 "Arbeidsstedet er: [ARBEIDSSTED]\\n"
                 "Arbeidsgiver kan endre arbeidsstedet innenfor [OMRÅDE]"),
                
                ("5. ARBEIDSTID",
                 "Normal arbeidstid er [TIMER] timer per uke\\n"
                 "Arbeidstiden er fordelt på [DAGER] dager per uke"),
                
                ("6. LØNN OG YTELSER",
                 "Lønn: [LØNN] per [PERIODE]\\n"
                 "Utbetaling: [UTBETALINGSDATO]\\n"
                 "Andre ytelser: [YTELSER]"),
                
                ("7. FERIE",
                 "Ferie reguleres av ferieloven\\n"
                 "Feriepenger utbetales [UTBETALINGSTIDSPUNKT]"),
                
                ("8. OPPSIGELSE",
                 "Oppsigelsestid i henhold til arbeidsmiljøloven\\n"
                 "Ved oppsigelse fra arbeidsgiver: [OPPSIGELSESTID]\\n"
                 "Ved oppsigelse fra arbeidstaker: [OPPSIGELSESTID]")
            ]
            
            for title, content in sections:
                # Section header
                layout.add(Paragraph(
                    title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=primary_color,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(5)
                ))
                
                # Section content
                layout.add(Paragraph(
                    content,
                    font="Helvetica",
                    font_size=Decimal(10),
                    font_color=text_color,
                    margin_bottom=Decimal(10)
                ))
        
        elif page_num == 1:
            # Second page - Additional terms and signature
            layout.add(Paragraph(
                "TILLEGGSBESTEMMELSER",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=primary_color,
                margin_top=Decimal(20),
                margin_bottom=Decimal(15)
            ))
            
            additional_sections = [
                ("9. TAUSHETSPLIKT",
                 "Arbeidstaker har taushetsplikt om bedriftens forhold"),
                
                ("10. KONKURRANSEKLAUSUL",
                 "Eventuelle konkurranseklausuler [DETALJER]"),
                
                ("11. ØVRIGE BESTEMMELSER",
                 "Andre relevante bestemmelser for arbeidsforholdet")
            ]
            
            for title, content in additional_sections:
                layout.add(Paragraph(
                    title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=primary_color,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(5)
                ))
                
                layout.add(Paragraph(
                    content,
                    font="Helvetica",
                    font_size=Decimal(10),
                    font_color=text_color,
                    margin_bottom=Decimal(10)
                ))
            
            # Signature section
            layout.add(Paragraph(
                "UNDERSKRIFT",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=primary_color,
                margin_top=Decimal(40),
                margin_bottom=Decimal(20)
            ))
            
            # Create signature table
            signature_table = FlexibleColumnWidthTable(
                number_of_columns=2,
                number_of_rows=4
            )
            
            # Table headers
            signature_table.add(TableCell(Paragraph("ARBEIDSGIVER", font="Helvetica-Bold", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("ARBEIDSTAKER", font="Helvetica-Bold", font_size=Decimal(10))))
            
            # Date fields
            signature_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
            
            # Signature fields
            signature_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
            
            # Name fields
            signature_table.add(TableCell(Paragraph("Navn: ________________", font="Helvetica", font_size=Decimal(10))))
            signature_table.add(TableCell(Paragraph("Navn: ________________", font="Helvetica", font_size=Decimal(10))))
            
            layout.add(signature_table)
    
    # Save the recreated document
    output_path = "recreated_norwegian_contract.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Norwegian employment contract recreated: {{output_path}}")
    return output_path

if __name__ == "__main__":
    recreate_norwegian_employment_contract()
'''.format(
            pdf_path=self.pdf_path,
            total_pages=content_data.get("summary", {}).get("total_pages_processed", 1),
            width=content_data.get("pages", [{}])[0].get("dimensions", {}).get("width", 595),
            height=content_data.get("pages", [{}])[0].get("dimensions", {}).get("height", 842)
        )
        
        return code_template
    
    def save_analysis_results(self, content_data: Dict[str, Any], output_dir: str = "py"):
        """Save analysis results to files"""
        output_path = Path(output_dir)
        
        # Save JSON analysis
        json_file = output_path / "pdf_analysis_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            # Convert sets to lists for JSON serialization
            json_data = self._prepare_for_json(content_data)
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Analysis results saved to: {json_file}")
        
        # Save recreation code
        recreation_code = self.generate_recreation_code(content_data)
        code_file = output_path / "recreate_norwegian_contract.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(recreation_code)
        
        print(f"✓ Recreation code saved to: {code_file}")
        
        return json_file, code_file
    
    def _prepare_for_json(self, data):
        """Prepare data for JSON serialization by converting sets to lists"""
        if isinstance(data, dict):
            return {k: self._prepare_for_json(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._prepare_for_json(item) for item in data]
        elif isinstance(data, set):
            return list(data)
        else:
            return data


def main():
    """Main function to parse the Norwegian employment contract PDF"""
    pdf_file = "Norwegian - bokmal - Standard contract of employment.pdf"
    
    if not Path(pdf_file).exists():
        print(f"Error: PDF file '{pdf_file}' not found!")
        print("Please ensure the file is in the current directory.")
        return
    
    print("=== Parsing Norwegian Employment Contract PDF with borb ===")
    print(f"Target file: {pdf_file}")
    
    # Create parser
    parser = PDFParser(pdf_file)
    
    # Load the PDF
    if not parser.load_pdf():
        return
    
    # Parse all content
    print("\n=== Extracting Content ===")
    content_data = parser.parse_all_pages()
    
    # Display results
    print(f"\n=== Analysis Results ===")
    metadata = content_data.get("metadata", {})
    summary = content_data.get("summary", {})
    
    print(f"File size: {metadata.get('file_size', 0):,} bytes")
    print(f"Pages accessible: {metadata.get('pages_accessible', 0)}")
    print(f"Pages processed: {summary.get('total_pages_processed', 0)}")
    print(f"Text elements found: {summary.get('total_text_elements', 0)}")
    print(f"Extraction successful: {summary.get('extraction_successful', False)}")
    
    # Show page details
    for i, page in enumerate(content_data.get("pages", [])):
        print(f"\nPage {i + 1}:")
        print(f"  Dimensions: {page.get('dimensions', {})}")
        print(f"  Text elements: {len(page.get('text_elements', []))}")
        
        # Show sample content
        for j, element in enumerate(page.get('text_elements', [])[:2]):  # Show first 2 elements
            print(f"  Element {j + 1} ({element.get('method', 'unknown')}):")
            content = element.get('content', '')
            if len(content) > 100:
                print(f"    {content[:100]}...")
            else:
                print(f"    {content}")
    
    # Save results
    print(f"\n=== Saving Results ===")
    json_file, code_file = parser.save_analysis_results(content_data)
    
    print(f"\n=== Next Steps ===")
    print(f"1. Review analysis results: {json_file}")
    print(f"2. Run recreation code: python {code_file}")
    print(f"3. Customize the recreation code for your needs")
    print(f"4. Use the structure to create dynamic contract generation")
    
    print(f"\n=== Summary ===")
    print("✓ Successfully parsed PDF with borb")
    print("✓ Extracted document structure and metadata")
    print("✓ Generated recreation template")
    print("✓ Ready for programmatic PDF generation")


if __name__ == "__main__":
    main()
