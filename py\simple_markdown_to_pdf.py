#!/usr/bin/env python3
"""
Simple Markdown to PDF Converter using borb
Converts the Norwegian employment contract markdown to PDF
"""

import re
from pathlib import Path
from decimal import Decimal
from typing import List

from borb.pdf import Document
from borb.pdf import Page
from borb.pdf import PDF
from borb.pdf import SingleColumnLayout
from borb.pdf import Paragraph
from borb.pdf.canvas.color.color import HexColor


class SimpleMarkdownToPDFConverter:
    """Simple converter for markdown to PDF using borb"""
    
    def __init__(self):
        self.document = Document()
        self.page = Page()
        self.document.add_page(self.page)
        self.layout = SingleColumnLayout(self.page)
        
        # Define colors
        self.primary_color = HexColor("#2c3e50")
        self.text_color = HexColor("#2c3e50")
    
    def clean_text(self, text: str) -> str:
        """Clean text of problematic characters and markdown formatting"""
        # Remove markdown formatting
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'`([^`]+)`', r'\1', text)        # Code
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # Links

        # Handle long URLs by breaking them
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, text)
        for url in urls:
            if len(url) > 50:
                # Break long URLs at logical points
                broken_url = url.replace('/', '/\n').replace('?', '?\n').replace('&', '&\n')
                text = text.replace(url, broken_url)
        
        # Replace problematic characters with ASCII equivalents
        text = text.replace('⁰', '0')
        text = text.replace('¹', '1')
        text = text.replace('²', '2')
        text = text.replace('³', '3')
        text = text.replace('⁴', '4')
        text = text.replace('⁵', '5')
        text = text.replace('⁶', '6')
        text = text.replace('⁷', '7')
        text = text.replace('⁸', '8')
        text = text.replace('⁹', '9')
        text = text.replace('°', ' grader')
        text = text.replace('–', '-')
        text = text.replace('—', '-')
        text = text.replace('"', '"')
        text = text.replace('"', '"')
        text = text.replace(''', "'")
        text = text.replace(''', "'")
        text = text.replace('§', 'paragraf')
        # Keep Norwegian characters as they should work with Helvetica
        # å, æ, ø, Å, Ø, é should be supported
        
        # Remove HTML comments
        text = re.sub(r'<!--.*?-->', '', text, flags=re.DOTALL)
        
        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = text.strip()
        
        return text
    
    def parse_markdown_file(self, file_path: str) -> str:
        """Read and return markdown content from file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return self.clean_text(content)
    
    def add_title(self, title: str):
        """Add main title"""
        title = self.clean_text(title)
        self.layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=Decimal(20),
            font_color=self.primary_color,
            margin_top=Decimal(20),
            margin_bottom=Decimal(20)
        ))
    
    def add_heading(self, text: str, level: int = 1):
        """Add a heading"""
        text = self.clean_text(text)
        if level == 1:
            font_size = Decimal(16)
            margin_top = Decimal(20)
        elif level == 2:
            font_size = Decimal(14)
            margin_top = Decimal(15)
        else:
            font_size = Decimal(12)
            margin_top = Decimal(10)
        
        self.layout.add(Paragraph(
            text,
            font="Helvetica-Bold",
            font_size=font_size,
            font_color=self.primary_color,
            margin_top=margin_top,
            margin_bottom=Decimal(10)
        ))
    
    def add_paragraph(self, text: str):
        """Add a regular paragraph"""
        text = self.clean_text(text)
        if not text.strip():
            return

        # Break very long lines to prevent layout issues
        max_line_length = 60  # Reduced for safety

        # Split by existing newlines first
        paragraphs = text.split('\n')
        processed_paragraphs = []

        for paragraph in paragraphs:
            if len(paragraph) > max_line_length:
                words = paragraph.split()
                lines = []
                current_line = []
                current_length = 0

                for word in words:
                    # If a single word is too long, break it
                    if len(word) > max_line_length:
                        if current_line:
                            lines.append(' '.join(current_line))
                            current_line = []
                            current_length = 0
                        # Break the long word
                        for i in range(0, len(word), max_line_length - 5):
                            lines.append(word[i:i + max_line_length - 5])
                    elif current_length + len(word) + 1 > max_line_length and current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                        current_length = len(word)
                    else:
                        current_line.append(word)
                        current_length += len(word) + 1

                if current_line:
                    lines.append(' '.join(current_line))

                processed_paragraphs.append('\n'.join(lines))
            else:
                processed_paragraphs.append(paragraph)

        text = '\n'.join(processed_paragraphs)

        self.layout.add(Paragraph(
            text,
            font="Helvetica",
            font_size=Decimal(11),
            font_color=self.text_color,
            margin_bottom=Decimal(8)
        ))
    
    def process_content(self, content: str):
        """Process markdown content and convert to PDF"""
        lines = content.split('\n')
        current_paragraph = []
        
        for line in lines:
            line = line.strip()
            
            if not line:
                # Empty line - end current paragraph
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                continue
            
            # Check for headers
            if line.startswith('#'):
                # End current paragraph first
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                
                # Count header level
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break
                
                # Extract header text
                header_text = line[level:].strip()
                if header_text:
                    self.add_heading(header_text, level)
                continue
            
            # Skip table separators and HTML comments
            if line.startswith('|---') or line.startswith('| :---') or line.startswith('<!--'):
                continue
            
            # Handle table rows (simplified - just add as paragraphs for now)
            if line.startswith('|') and line.endswith('|'):
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                
                # Extract table content
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                if cells and any(cell.strip() for cell in cells):
                    table_text = ' | '.join(cells)
                    self.add_paragraph(table_text)
                continue
            
            # Handle list items
            if line.startswith(('* ', '- ', '+ ')):
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                self.add_paragraph("• " + line[2:])
                continue
            
            # Handle numbered lists
            if re.match(r'^\d+\.\s', line):
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                self.add_paragraph(line)
                continue
            
            # Regular content line
            current_paragraph.append(line)
        
        # Add any remaining content
        if current_paragraph:
            self.add_paragraph('\n'.join(current_paragraph))
    
    def save_pdf(self, output_path: str):
        """Save the document as PDF"""
        with open(output_path, "wb") as pdf_file:
            PDF.dumps(pdf_file, self.document)


def main():
    """Main function to convert markdown to PDF"""
    # File paths
    markdown_file = "Norsk arbeidskontrakt for anleggsgartner_.md"
    output_file = "py/Arbeidskontrakt_Ringerike_Landskap.pdf"
    
    # Check if markdown file exists
    if not Path(markdown_file).exists():
        print(f"Error: Markdown file '{markdown_file}' not found!")
        return
    
    print("Starting conversion...")
    
    # Create converter
    converter = SimpleMarkdownToPDFConverter()
    
    # Read and process markdown content
    content = converter.parse_markdown_file(markdown_file)
    
    # Add title
    converter.add_title("Arbeidskontrakt - Ringerike Landskap AS")
    
    # Process content
    converter.process_content(content)
    
    # Save PDF
    converter.save_pdf(output_file)
    
    print(f"PDF successfully created: {output_file}")


if __name__ == "__main__":
    main()
