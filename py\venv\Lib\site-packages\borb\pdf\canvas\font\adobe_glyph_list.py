#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
The Adobe Glyph List (AGL) is a mapping of 4,281 glyph names to one or more Unicode characters.
Its purpose is to provide an implementation guideline for consumers of fonts (mainly software applications);
it lists a variety of standard names that are given to glyphs that correspond to certain Unicode character sequences.
The AGL is maintained by Adobe Systems.
For producers of fonts, Adobe suggests a more limited set of names, the Adobe Glyph List for New Fonts (AGLFN),
based on an earlier version of the AGL.
Names not in the AGLFN are to be constructed by standard heuristics described in Unicode and Glyph Names.
AGL and AGLFN, along with related resources, are currently maintained and available at the AGL & AGLFN open source project.
"""
import typing


class AdobeGlyphList:
    """
    The Adobe Glyph List (AGL) is a mapping of 4,281 glyph names to one or more Unicode characters.
    Its purpose is to provide an implementation guideline for consumers of fonts (mainly software applications);
    it lists a variety of standard names that are given to glyphs that correspond to certain Unicode character sequences.
    The AGL is maintained by Adobe Systems.
    For producers of fonts, Adobe suggests a more limited set of names, the Adobe Glyph List for New Fonts (AGLFN),
    based on an earlier version of the AGL.
    Names not in the AGLFN are to be constructed by standard heuristics described in Unicode and Glyph Names.
    AGL and AGLFN, along with related resources, are currently maintained and available at the AGL & AGLFN open source project.
    """

    NAME_TO_UNICODE: typing.Dict[str, int] = {
        "space": 32,
        "exclam": 33,
        "quotedbl": 34,
        "numbersign": 35,
        "dollar": 36,
        "percent": 37,
        "ampersand": 38,
        "quotesingle": 39,
        "parenleft": 40,
        "parenright": 41,
        "asterisk": 42,
        "plus": 43,
        "comma": 44,
        "hyphen": 45,
        "period": 46,
        "slash": 47,
        "zero": 48,
        "one": 49,
        "two": 50,
        "three": 51,
        "four": 52,
        "five": 53,
        "six": 54,
        "seven": 55,
        "eight": 56,
        "nine": 57,
        "colon": 58,
        "semicolon": 59,
        "less": 60,
        "equal": 61,
        "greater": 62,
        "question": 63,
        "at": 64,
        "A": 65,
        "B": 66,
        "C": 67,
        "D": 68,
        "E": 69,
        "F": 70,
        "G": 71,
        "H": 72,
        "I": 73,
        "J": 74,
        "K": 75,
        "L": 76,
        "M": 77,
        "N": 78,
        "O": 79,
        "P": 80,
        "Q": 81,
        "R": 82,
        "S": 83,
        "T": 84,
        "U": 85,
        "V": 86,
        "W": 87,
        "X": 88,
        "Y": 89,
        "Z": 90,
        "bracketleft": 91,
        "backslash": 92,
        "bracketright": 93,
        "asciicircum": 94,
        "underscore": 95,
        "grave": 96,
        "a": 97,
        "b": 98,
        "c": 99,
        "d": 100,
        "e": 101,
        "f": 102,
        "g": 103,
        "h": 104,
        "i": 105,
        "j": 106,
        "k": 107,
        "l": 108,
        "m": 109,
        "n": 110,
        "o": 111,
        "p": 112,
        "q": 113,
        "r": 114,
        "s": 115,
        "t": 116,
        "u": 117,
        "v": 118,
        "w": 119,
        "x": 120,
        "y": 121,
        "z": 122,
        "braceleft": 123,
        "bar": 124,
        "braceright": 125,
        "asciitilde": 126,
        "nbspace": 160,
        "exclamdown": 161,
        "cent": 162,
        "sterling": 163,
        "currency": 164,
        "yen": 165,
        "brokenbar": 166,
        "section": 167,
        "dieresis": 168,
        "copyright": 169,
        "ordfeminine": 170,
        "guillemetleft": 171,
        "logicalnot": 172,
        "hyphensoft": 173,
        "registered": 174,
        "macron": 175,
        "degree": 176,
        "plusminus": 177,
        "two.superior": 178,
        "three.superior": 179,
        "acute": 180,
        "mu.math": 181,
        "paragraph": 182,
        "periodcentered": 183,
        "cedilla": 184,
        "one.superior": 185,
        "ordmasculine": 186,
        "guillemetright": 187,
        "onequarter": 188,
        "onehalf": 189,
        "threequarters": 190,
        "questiondown": 191,
        "Agrave": 192,
        "Aacute": 193,
        "Acircumflex": 194,
        "Atilde": 195,
        "Adieresis": 196,
        "Aring": 197,
        "AE": 198,
        "Ccedilla": 199,
        "Egrave": 200,
        "Eacute": 201,
        "Ecircumflex": 202,
        "Edieresis": 203,
        "Igrave": 204,
        "Iacute": 205,
        "Icircumflex": 206,
        "Idieresis": 207,
        "Eth": 208,
        "Ntilde": 209,
        "Ograve": 210,
        "Oacute": 211,
        "Ocircumflex": 212,
        "Otilde": 213,
        "Odieresis": 214,
        "multiply": 215,
        "Oslash": 216,
        "Ugrave": 217,
        "Uacute": 218,
        "Ucircumflex": 219,
        "Udieresis": 220,
        "Yacute": 221,
        "Thorn": 222,
        "germandbls": 223,
        "agrave": 224,
        "aacute": 225,
        "acircumflex": 226,
        "atilde": 227,
        "adieresis": 228,
        "aring": 229,
        "ae": 230,
        "ccedilla": 231,
        "egrave": 232,
        "eacute": 233,
        "ecircumflex": 234,
        "edieresis": 235,
        "igrave": 236,
        "iacute": 237,
        "icircumflex": 238,
        "idieresis": 239,
        "eth": 240,
        "ntilde": 241,
        "ograve": 242,
        "oacute": 243,
        "ocircumflex": 244,
        "otilde": 245,
        "odieresis": 246,
        "divide": 247,
        "oslash": 248,
        "ugrave": 249,
        "uacute": 250,
        "ucircumflex": 251,
        "udieresis": 252,
        "yacute": 253,
        "thorn": 254,
        "ydieresis": 255,
        "Amacron": 256,
        "amacron": 257,
        "Abreve": 258,
        "abreve": 259,
        "Aogonek": 260,
        "aogonek": 261,
        "Cacute": 262,
        "cacute": 263,
        "Ccircumflex": 264,
        "ccircumflex": 265,
        "Cdotaccent": 266,
        "cdotaccent": 267,
        "Ccaron": 268,
        "ccaron": 269,
        "Dcaron": 270,
        "dcaron": 271,
        "Dcroat": 272,
        "dcroat": 273,
        "Emacron": 274,
        "emacron": 275,
        "Ebreve": 276,
        "ebreve": 277,
        "Edotaccent": 278,
        "edotaccent": 279,
        "Eogonek": 280,
        "eogonek": 281,
        "Ecaron": 282,
        "ecaron": 283,
        "Gcircumflex": 284,
        "gcircumflex": 285,
        "Gbreve": 286,
        "gbreve": 287,
        "Gdotaccent": 288,
        "gdotaccent": 289,
        "Gcommaaccent": 290,
        "gcommaaccent": 291,
        "Hcircumflex": 292,
        "hcircumflex": 293,
        "Hbar": 294,
        "hbar": 295,
        "Itilde": 296,
        "itilde": 297,
        "Imacron": 298,
        "imacron": 299,
        "Ibreve": 300,
        "ibreve": 301,
        "Iogonek": 302,
        "iogonek": 303,
        "Idotaccent": 304,
        "dotlessi": 305,
        "IJ": 306,
        "ij": 307,
        "Jcircumflex": 308,
        "jcircumflex": 309,
        "Kcommaaccent": 310,
        "kcommaaccent": 311,
        "kra": 312,
        "Lacute": 313,
        "lacute": 314,
        "Lcommaaccent": 315,
        "lcommaaccent": 316,
        "Lcaron": 317,
        "lcaron": 318,
        "Ldot": 319,
        "ldot": 320,
        "Lslash": 321,
        "lslash": 322,
        "Nacute": 323,
        "nacute": 324,
        "Ncommaaccent": 325,
        "ncommaaccent": 326,
        "Ncaron": 327,
        "ncaron": 328,
        "napostrophe": 329,
        "Eng": 330,
        "eng": 331,
        "Omacron": 332,
        "omacron": 333,
        "Obreve": 334,
        "obreve": 335,
        "Ohungarumlaut": 336,
        "ohungarumlaut": 337,
        "OE": 338,
        "oe": 339,
        "Racute": 340,
        "racute": 341,
        "Rcommaaccent": 342,
        "rcommaaccent": 343,
        "Rcaron": 344,
        "rcaron": 345,
        "Sacute": 346,
        "sacute": 347,
        "Scircumflex": 348,
        "scircumflex": 349,
        "Scedilla": 350,
        "scedilla": 351,
        "Scaron": 352,
        "scaron": 353,
        "Tcedilla": 354,
        "tcedilla": 355,
        "Tcaron": 356,
        "tcaron": 357,
        "Tbar": 358,
        "tbar": 359,
        "Utilde": 360,
        "utilde": 361,
        "Umacron": 362,
        "umacron": 363,
        "Ubreve": 364,
        "ubreve": 365,
        "Uring": 366,
        "uring": 367,
        "Uhungarumlaut": 368,
        "uhungarumlaut": 369,
        "Uogonek": 370,
        "uogonek": 371,
        "Wcircumflex": 372,
        "wcircumflex": 373,
        "Ycircumflex": 374,
        "ycircumflex": 375,
        "Ydieresis": 376,
        "Zacute": 377,
        "zacute": 378,
        "Zdotaccent": 379,
        "zdotaccent": 380,
        "Zcaron": 381,
        "zcaron": 382,
        "longs": 383,
        "bstroke": 384,
        "Bhook": 385,
        "Btopbar": 386,
        "btopbar": 387,
        "Tonesix": 388,
        "tonesix": 389,
        "Oopen": 390,
        "Chook": 391,
        "chook": 392,
        "Dafrican": 393,
        "Dhook": 394,
        "Dtopbar": 395,
        "dtopbar": 396,
        "deltaturned": 397,
        "Ereversed": 398,
        "Schwa": 399,
        "Eopen": 400,
        "Fhook": 401,
        "fhook": 402,
        "Ghook": 403,
        "lt:Gamma": 404,
        "hv": 405,
        "lt:Iota": 406,
        "Istroke": 407,
        "Khook": 408,
        "khook": 409,
        "lbar": 410,
        "lambdastroke": 411,
        "Mturned": 412,
        "Nhookleft": 413,
        "nlongrightleg": 414,
        "Obar": 415,
        "Ohorn": 416,
        "ohorn": 417,
        "Oi": 418,
        "oi": 419,
        "Phook": 420,
        "phook": 421,
        "yr": 422,
        "Tonetwo": 423,
        "tonetwo": 424,
        "Esh": 425,
        "eshreversedloop": 426,
        "tpalatalhook": 427,
        "Thook": 428,
        "thook": 429,
        "Tretroflexhook": 430,
        "Uhorn": 431,
        "uhorn": 432,
        "lt:Upsilon": 433,
        "Vhook": 434,
        "Yhook": 435,
        "yhook": 436,
        "Zstroke": 437,
        "zstroke": 438,
        "Ezh": 439,
        "Ezhreversed": 440,
        "ezhreversed": 441,
        "ezhtail": 442,
        "twostroke": 443,
        "Tonefive": 444,
        "tonefive": 445,
        "glottalinvertedstroke": 446,
        "wynn": 447,
        "clickdental": 448,
        "clicklateral": 449,
        "clickalveolar": 450,
        "clickretroflex": 451,
        "DZcaron": 452,
        "Dzcaron": 453,
        "dzcaron": 454,
        "LJ": 455,
        "Lj": 456,
        "lj": 457,
        "NJ": 458,
        "Nj": 459,
        "nj": 460,
        "Acaron": 461,
        "acaron": 462,
        "Icaron": 463,
        "icaron": 464,
        "Ocaron": 465,
        "ocaron": 466,
        "Ucaron": 467,
        "ucaron": 468,
        "Udieresismacron": 469,
        "udieresismacron": 470,
        "Udieresisacute": 471,
        "udieresisacute": 472,
        "Udieresiscaron": 473,
        "udieresiscaron": 474,
        "Udieresisgrave": 475,
        "udieresisgrave": 476,
        "eturned": 477,
        "Adieresismacron": 478,
        "adieresismacron": 479,
        "Adotmacron": 480,
        "adotmacron": 481,
        "AEmacron": 482,
        "aemacron": 483,
        "Gstroke": 484,
        "gstroke": 485,
        "Gcaron": 486,
        "gcaron": 487,
        "Kcaron": 488,
        "kcaron": 489,
        "Oogonek": 490,
        "oogonek": 491,
        "Oogonekmacron": 492,
        "oogonekmacron": 493,
        "Ezhcaron": 494,
        "ezhcaron": 495,
        "jcaron": 496,
        "DZ": 497,
        "Dz": 498,
        "dz": 499,
        "Gacute": 500,
        "gacute": 501,
        "Hwair": 502,
        "Wynn": 503,
        "Ngrave": 504,
        "ngrave": 505,
        "Aringacute": 506,
        "aringacute": 507,
        "AEacute": 508,
        "aeacute": 509,
        "Oslashacute": 510,
        "oslashacute": 511,
        "Agravedbl": 512,
        "agravedbl": 513,
        "Ainvertedbreve": 514,
        "ainvertedbreve": 515,
        "Egravedbl": 516,
        "egravedbl": 517,
        "Einvertedbreve": 518,
        "einvertedbreve": 519,
        "Igravedbl": 520,
        "igravedbl": 521,
        "Iinvertedbreve": 522,
        "iinvertedbreve": 523,
        "Ogravedbl": 524,
        "ogravedbl": 525,
        "Oinvertedbreve": 526,
        "oinvertedbreve": 527,
        "Rgravedbl": 528,
        "rgravedbl": 529,
        "Rinvertedbreve": 530,
        "rinvertedbreve": 531,
        "Ugravedbl": 532,
        "ugravedbl": 533,
        "Uinvertedbreve": 534,
        "uinvertedbreve": 535,
        "Scommaaccent": 536,
        "scommaaccent": 537,
        "Tcommaaccent": 538,
        "tcommaaccent": 539,
        "Yogh": 540,
        "yogh": 541,
        "Hcaron": 542,
        "hcaron": 543,
        "Nlongrightleg": 544,
        "dcurl": 545,
        "Ou": 546,
        "ou": 547,
        "Zhook": 548,
        "zhook": 549,
        "Adot": 550,
        "adot": 551,
        "Ecedilla": 552,
        "ecedilla": 553,
        "Odieresismacron": 554,
        "odieresismacron": 555,
        "Otildemacron": 556,
        "otildemacron": 557,
        "Odot": 558,
        "odot": 559,
        "Odotmacron": 560,
        "odotmacron": 561,
        "Ymacron": 562,
        "ymacron": 563,
        "lcurl": 564,
        "ncurl": 565,
        "tcurl": 566,
        "dotlessj": 567,
        "dbdigraph": 568,
        "qpdigraph": 569,
        "Astroke": 570,
        "Cstroke": 571,
        "cstroke": 572,
        "Lbar": 573,
        "Twithdiagonalstroke": 574,
        "sswashtail": 575,
        "zswashtail": 576,
        "Glottalstop": 577,
        "glottalstop": 578,
        "Bstroke": 579,
        "Ubar": 580,
        "Vturned": 581,
        "Estroke": 582,
        "estroke": 583,
        "Jstroke": 584,
        "jstroke": 585,
        "Qsmallhooktail": 586,
        "qhooktail": 587,
        "Rstroke": 588,
        "rstroke": 589,
        "Ystroke": 590,
        "ystroke": 591,
        "aturned": 592,
        "ipa:alpha": 593,
        "alphaturned": 594,
        "bhook": 595,
        "oopen": 596,
        "ccurl": 597,
        "dtail": 598,
        "dhook": 599,
        "ipa:ereversed": 600,
        "schwa": 601,
        "schwahook": 602,
        "eopen": 603,
        "eopenreversed": 604,
        "eopenreversedhook": 605,
        "eopenreversedclosed": 606,
        "dotlessjstroke": 607,
        "ghook": 608,
        "ipa:gscript": 609,
        "Gsmall": 610,
        "ipa:gamma": 611,
        "ramshorn": 612,
        "hturned": 613,
        "hhook": 614,
        "henghook": 615,
        "istroke": 616,
        "ipa:iota": 617,
        "ipa:Ismall": 618,
        "lmiddletilde": 619,
        "lbelt": 620,
        "lretroflex": 621,
        "lezh": 622,
        "mturned": 623,
        "mlonglegturned": 624,
        "mhook": 625,
        "nhookleft": 626,
        "nretroflex": 627,
        "Nsmall": 628,
        "obarred": 629,
        "OEsmall": 630,
        "omegaclosed": 631,
        "ipa:phi": 632,
        "rturned": 633,
        "rlonglegturned": 634,
        "rhookturned": 635,
        "rlongleg": 636,
        "ipa:rtail": 637,
        "rfishhook": 638,
        "rfishhookreversed": 639,
        "Rsmall": 640,
        "Rinvertedsmall": 641,
        "shook": 642,
        "esh": 643,
        "dotlessjstrokehook": 644,
        "eshsquatreversed": 645,
        "eshcurl": 646,
        "tturned": 647,
        "tretroflex": 648,
        "ubar": 649,
        "ipa:upsilon": 650,
        "vhook": 651,
        "vturned": 652,
        "wturned": 653,
        "yturned": 654,
        "Ysmall": 655,
        "zretroflex": 656,
        "zcurl": 657,
        "ezh": 658,
        "ezhcurl": 659,
        "ipa:glottalstop": 660,
        "pharyngealvoicedfricative": 661,
        "glottalstopinverted": 662,
        "Cstretched": 663,
        "clickbilabial": 664,
        "Bsmall": 665,
        "eopenclosed": 666,
        "Ghooksmall": 667,
        "Hsmall": 668,
        "jcrossedtail": 669,
        "kturned": 670,
        "Lsmall": 671,
        "qhook": 672,
        "glottalstopstroke": 673,
        "glottalstopstrokereversed": 674,
        "dzed": 675,
        "dezh": 676,
        "dzedcurl": 677,
        "ts": 678,
        "tesh": 679,
        "tccurl": 680,
        "feng": 681,
        "ls": 682,
        "lzed": 683,
        "percussivebilabial": 684,
        "percussivebidental": 685,
        "hfishhookturned": 686,
        "handtailfishhookturned": 687,
        "hsupmod": 688,
        "hhooksupmod": 689,
        "jsupmod": 690,
        "rsupmod": 691,
        "rturnedsupmod": 692,
        "rhookturnedsupmod": 693,
        "Rsupinvertedmod": 694,
        "wsupmod": 695,
        "ysupmod": 696,
        "primemod": 697,
        "primedblmod": 698,
        "commaturnedmod": 699,
        "apostrophemod": 700,
        "commareversedmod": 701,
        "ringhalfrightmod": 702,
        "ringhalfleftmod": 703,
        "glottalstopmod": 704,
        "glottalstopreversedmod": 705,
        "arrowheadleftmod": 706,
        "arrowheadrightmod": 707,
        "arrowheadupmod": 708,
        "arrowheaddownmod": 709,
        "circumflex": 710,
        "caron": 711,
        "verticallinemod": 712,
        "macronmod": 713,
        "acutemod": 714,
        "gravemod": 715,
        "verticallinelowmod": 716,
        "macronlowmod": 717,
        "gravelowmod": 718,
        "acutelowmod": 719,
        "colontriangularmod": 720,
        "colontriangularhalfmod": 721,
        "ringhalfrightcentredmod": 722,
        "ringhalfleftcentredmod": 723,
        "tackupmod": 724,
        "tackdownmod": 725,
        "plussignmod": 726,
        "minussignmod": 727,
        "breve": 728,
        "dotaccent": 729,
        "ring": 730,
        "ogonek": 731,
        "tilde": 732,
        "hungarumlaut": 733,
        "rhotichookmod": 734,
        "crossmod": 735,
        "gammasupmod": 736,
        "lsupmod": 737,
        "ssupmod": 738,
        "xsupmod": 739,
        "glottalstopsupreversedmod": 740,
        "tonebarextrahighmod": 741,
        "tonebarhighmod": 742,
        "tonebarmidmod": 743,
        "tonebarlowmod": 744,
        "tonebarextralowmod": 745,
        "yintonemod": 746,
        "yangtonemod": 747,
        "voicingmod": 748,
        "unaspiratedmod": 749,
        "apostrophedblmod": 750,
        "arrowheaddownlowmod": 751,
        "arrowheaduplowmod": 752,
        "arrowheadleftlowmod": 753,
        "arrowheadrightlowmod": 754,
        "ringlowmod": 755,
        "gravemiddlemod": 756,
        "gravedblmiddlemod": 757,
        "acutedblmiddlemod": 758,
        "tildelowmod": 759,
        "colonraisedmod": 760,
        "tonehighbeginmod": 761,
        "tonehighendmod": 762,
        "tonelowbeginmod": 763,
        "tonelowendmod": 764,
        "shelfmod": 765,
        "shelfopenmod": 766,
        "arrowleftlowmod": 767,
        "gravecmb": 768,
        "acutecmb": 769,
        "circumflexcmb": 770,
        "tildecmb": 771,
        "macroncmb": 772,
        "overlinecmb": 773,
        "brevecmb": 774,
        "dotaccentcmb": 775,
        "dieresiscmb": 776,
        "hookabovecmb": 777,
        "ringabovecmb": 778,
        "hungarumlautcmb": 779,
        "caroncmb": 780,
        "verticallineabovecmb": 781,
        "dblverticallineabovecmb": 782,
        "gravedoublecmb": 783,
        "candrabinducmb": 784,
        "invertedbrevecmb": 785,
        "commaturnedabovecmb": 786,
        "turnedabovecmb": 787,
        "reversedcommaabovecmb": 788,
        "turnedcommaabovecmb": 789,
        "gravebelowcmb": 790,
        "acutebelowcmb": 791,
        "lefttackbelowcmb": 792,
        "righttackbelowcmb": 793,
        "leftangleabovecmb": 794,
        "horncmb": 795,
        "halfleftringbelowcmb": 796,
        "uptackbelowcmb": 797,
        "downtackbelowcmb": 798,
        "plusbelowcmb": 799,
        "minusbelowcmb": 800,
        "palatalizedhookbelowcmb": 801,
        "retroflexhookbelowcmb": 802,
        "dotbelowcmb": 803,
        "dieresisbelowcmb": 804,
        "ringbelowcmb": 805,
        "commaaccentbelowcmb": 806,
        "cedillacmb": 807,
        "ogonekcmb": 808,
        "verticallinebelowcmb": 809,
        "bridgebelowcmb": 810,
        "dblarchinvertedbelowcmb": 811,
        "caronbelowcmb": 812,
        "circumflexbelowcmb": 813,
        "belowbrevecmb": 814,
        "invertedbelowbrevecmb": 815,
        "tildebelowcmb": 816,
        "macronbelowcmb": 817,
        "lowlinecmb": 818,
        "lowlinedoublecmb": 819,
        "tildeoverlaycmb": 820,
        "overlaystrokeshortcmb": 821,
        "overlaystrokelongcmb": 822,
        "solidusshortoverlaycmb": 823,
        "soliduslongoverlaycmb": 824,
        "halfrightringbelowcmb": 825,
        "invertedbridgebelowcmb": 826,
        "squarebelowcmb": 827,
        "seagullbelowcmb": 828,
        "xabovecmb": 829,
        "tildeverticalcmb": 830,
        "dbloverlinecmb": 831,
        "gravetonecmb": 832,
        "acutetonecmb": 833,
        "perispomenicmb": 834,
        "koroniscmb": 835,
        "dialytikatonoscmb": 836,
        "iotasubcmb": 837,
        "bridgeabovecmb": 838,
        "equalbelowcmb": 839,
        "dblverticallinebelowcmb": 840,
        "leftanglebelowcmb": 841,
        "nottildeabovecmb": 842,
        "homotheticabovecmb": 843,
        "almostequalabovecmb": 844,
        "arrowleftrightbelowcmb": 845,
        "arrowupbelowcmb": 846,
        "graphemejoinercmb": 847,
        "arrowheadrightabovecmb": 848,
        "halfleftringabovecmb": 849,
        "fermatacmb": 850,
        "xbelowcmb": 851,
        "arrowheadleftbelowcmb": 852,
        "arrowheadrightbelowcmb": 853,
        "arrowheadrightarrowheadupbelowcmb": 854,
        "halfrightringabovecmb": 855,
        "dotrightabovecmb": 856,
        "asteriskbelowcmb": 857,
        "doubleringbelowcmb": 858,
        "zigzagabovecmb": 859,
        "doublebelowbrevecmb": 860,
        "doublebrevecmb": 861,
        "macrondoublecmb": 862,
        "macrondoublebelowcmb": 863,
        "tildedoublecmb": 864,
        "inverteddoublebrevecmb": 865,
        "arrowrightdoublebelowcmb": 866,
        "acmb": 867,
        "ecmb": 868,
        "icmb": 869,
        "ocmb": 870,
        "ucmb": 871,
        "ccmb": 872,
        "dcmb": 873,
        "hcmb": 874,
        "mcmb": 875,
        "rcmb": 876,
        "tcmb": 877,
        "vcmb": 878,
        "xcmb": 879,
        "Heta": 880,
        "heta": 881,
        "Sampiarchaic": 882,
        "sampiarchaic": 883,
        "numeralsign": 884,
        "lownumeralsign": 885,
        "Digammapamphylian": 886,
        "digammapamphylian": 887,
        "iotasub": 890,
        "sigmalunatereversedsymbol": 891,
        "sigmalunatedottedsymbol": 892,
        "sigmalunatedottedreversedsymbol": 893,
        "gr:question": 894,
        "Yot": 895,
        "tonos": 900,
        "dieresistonos": 901,
        "Alphatonos": 902,
        "anoteleia": 903,
        "Epsilontonos": 904,
        "Etatonos": 905,
        "Iotatonos": 906,
        "Omicrontonos": 908,
        "Upsilontonos": 910,
        "Omegatonos": 911,
        "iotadieresistonos": 912,
        "Alpha": 913,
        "Beta": 914,
        "Gamma": 915,
        "Delta": 916,
        "Epsilon": 917,
        "Zeta": 918,
        "Eta": 919,
        "Theta": 920,
        "Iota": 921,
        "Kappa": 922,
        "Lambda": 923,
        "Mu": 924,
        "Nu": 925,
        "Xi": 926,
        "Omicron": 927,
        "Pi": 928,
        "Rho": 929,
        "Sigma": 931,
        "Tau": 932,
        "Upsilon": 933,
        "Phi": 934,
        "Chi": 935,
        "Psi": 936,
        "Omega": 937,
        "Iotadieresis": 938,
        "Upsilondieresis": 939,
        "alphatonos": 940,
        "epsilontonos": 941,
        "etatonos": 942,
        "iotatonos": 943,
        "upsilondieresistonos": 944,
        "alpha": 945,
        "beta": 946,
        "gamma": 947,
        "delta": 948,
        "epsilon": 949,
        "zeta": 950,
        "eta": 951,
        "theta": 952,
        "iota": 953,
        "kappa": 954,
        "lambda": 955,
        "mu": 956,
        "nu": 957,
        "xi": 958,
        "omicron": 959,
        "pi": 960,
        "rho": 961,
        "finalsigma": 962,
        "sigma": 963,
        "tau": 964,
        "upsilon": 965,
        "phi": 966,
        "chi": 967,
        "psi": 968,
        "omega": 969,
        "iotadieresis": 970,
        "upsilondieresis": 971,
        "omicrontonos": 972,
        "upsilontonos": 973,
        "omegatonos": 974,
        "Kaisymbol": 975,
        "betasymbol": 976,
        "theta.math": 977,
        "Upsilonhooksymbol": 978,
        "Upsilonacutehooksymbol": 979,
        "Upsilonadieresishooksymbol": 980,
        "phi.math": 981,
        "pi.math": 982,
        "kaisymbol": 983,
        "Koppaarchaic": 984,
        "koppaarchaic": 985,
        "Stigma": 986,
        "stigma": 987,
        "Digamma": 988,
        "digamma": 989,
        "Koppa": 990,
        "koppa": 991,
        "Sampi": 992,
        "sampi": 993,
        "Sheicoptic": 994,
        "sheicoptic": 995,
        "Feicoptic": 996,
        "feicoptic": 997,
        "Kheicoptic": 998,
        "kheicoptic": 999,
        "Horicoptic": 1000,
        "horicoptic": 1001,
        "Gangiacoptic": 1002,
        "gangiacoptic": 1003,
        "Shimacoptic": 1004,
        "shimacoptic": 1005,
        "Deicoptic": 1006,
        "deicoptic": 1007,
        "kappa.math": 1008,
        "rhosymbol": 1009,
        "sigmalunatesymbol": 1010,
        "yot": 1011,
        "Thetasymbol": 1012,
        "epsilonlunatesymbol": 1013,
        "epsilonreversedlunatesymbol": 1014,
        "Sho": 1015,
        "sho": 1016,
        "Sigmalunatesymbol": 1017,
        "San": 1018,
        "san": 1019,
        "rhostrokesymbol": 1020,
        "Sigmareversedlunatesymbol": 1021,
        "Sigmalunatesymboldotted": 1022,
        "Reverseddottedsigmalunatesymbol": 1023,
        "Iegravecyr": 1024,
        "Iocyr": 1025,
        "Djecyr": 1026,
        "Gjecyr": 1027,
        "Eukrcyr": 1028,
        "Dzecyr": 1029,
        "Iukrcyr": 1030,
        "Yukrcyr": 1031,
        "Jecyr": 1032,
        "Ljecyr": 1033,
        "Njecyr": 1034,
        "Tshecyr": 1035,
        "Kjecyr": 1036,
        "Igravecyr": 1037,
        "Ushortcyr": 1038,
        "Dzhecyr": 1039,
        "Acyr": 1040,
        "Becyr": 1041,
        "Vecyr": 1042,
        "Gecyr": 1043,
        "Decyr": 1044,
        "Iecyr": 1045,
        "Zhecyr": 1046,
        "Zecyr": 1047,
        "Icyr": 1048,
        "Ishortcyr": 1049,
        "Kacyr": 1050,
        "Elcyr": 1051,
        "Emcyr": 1052,
        "Encyr": 1053,
        "Ocyr": 1054,
        "Pecyr": 1055,
        "Ercyr": 1056,
        "Escyr": 1057,
        "Tecyr": 1058,
        "Ucyr": 1059,
        "Efcyr": 1060,
        "Hacyr": 1061,
        "Tsecyr": 1062,
        "Checyr": 1063,
        "Shacyr": 1064,
        "Shchacyr": 1065,
        "Hardcyr": 1066,
        "Ylongcyr": 1067,
        "Softcyr": 1068,
        "Ereversedcyr": 1069,
        "Yucyr": 1070,
        "Yacyr": 1071,
        "acyr": 1072,
        "becyr": 1073,
        "vecyr": 1074,
        "gecyr": 1075,
        "decyr": 1076,
        "iecyr": 1077,
        "zhecyr": 1078,
        "zecyr": 1079,
        "icyr": 1080,
        "ishortcyr": 1081,
        "kacyr": 1082,
        "elcyr": 1083,
        "emcyr": 1084,
        "encyr": 1085,
        "ocyr": 1086,
        "pecyr": 1087,
        "ercyr": 1088,
        "escyr": 1089,
        "tecyr": 1090,
        "ucyr": 1091,
        "efcyr": 1092,
        "hacyr": 1093,
        "tsecyr": 1094,
        "checyr": 1095,
        "shacyr": 1096,
        "shchacyr": 1097,
        "hardcyr": 1098,
        "ylongcyr": 1099,
        "softcyr": 1100,
        "ereversedcyr": 1101,
        "yucyr": 1102,
        "yacyr": 1103,
        "iegravecyr": 1104,
        "iocyr": 1105,
        "djecyr": 1106,
        "gjecyr": 1107,
        "eukrcyr": 1108,
        "dzecyr": 1109,
        "iukrcyr": 1110,
        "yukrcyr": 1111,
        "jecyr": 1112,
        "ljecyr": 1113,
        "njecyr": 1114,
        "tshecyr": 1115,
        "kjecyr": 1116,
        "igravecyr": 1117,
        "ushortcyr": 1118,
        "dzhecyr": 1119,
        "Omegacyr": 1120,
        "omegacyr": 1121,
        "Yatcyr": 1122,
        "yatcyr": 1123,
        "Eiotifiedcyr": 1124,
        "eiotifiedcyr": 1125,
        "Yuslittlecyr": 1126,
        "yuslittlecyr": 1127,
        "Yuslittleiotifiedcyr": 1128,
        "yuslittleiotifiedcyr": 1129,
        "Yusbigcyr": 1130,
        "yusbigcyr": 1131,
        "Yusbigiotifiedcyr": 1132,
        "yusbigiotifiedcyr": 1133,
        "Ksicyr": 1134,
        "ksicyr": 1135,
        "Psicyr": 1136,
        "psicyr": 1137,
        "Fitacyr": 1138,
        "fitacyr": 1139,
        "Izhitsacyr": 1140,
        "izhitsacyr": 1141,
        "Izhitsagravedblcyr": 1142,
        "izhitsagravedblcyr": 1143,
        "Ukcyr": 1144,
        "ukcyr": 1145,
        "Omegaroundcyr": 1146,
        "omegaroundcyr": 1147,
        "Omegatitlocyr": 1148,
        "omegatitlocyr": 1149,
        "Otcyr": 1150,
        "otcyr": 1151,
        "Koppacyr": 1152,
        "koppacyr": 1153,
        "thousandscyr": 1154,
        "titlocmbcyr": 1155,
        "palatcmbcyr": 1156,
        "dasiacmbcyr": 1157,
        "psilicmbcyr": 1158,
        "pokrytiecmbcyr": 1159,
        "hundredthousandscmbcyr": 1160,
        "millionscmbcyr": 1161,
        "Ishortsharptailcyr": 1162,
        "ishortsharptailcyr": 1163,
        "Semisoftcyr": 1164,
        "semisoftcyr": 1165,
        "Ertickcyr": 1166,
        "ertickcyr": 1167,
        "Geupcyr": 1168,
        "geupcyr": 1169,
        "Gestrokecyr": 1170,
        "gestrokecyr": 1171,
        "Gehookcyr": 1172,
        "gehookcyr": 1173,
        "Zhetailcyr": 1174,
        "zhetailcyr": 1175,
        "Zetailcyr": 1176,
        "zetailcyr": 1177,
        "Katailcyr": 1178,
        "katailcyr": 1179,
        "Kaverticalstrokecyr": 1180,
        "kaverticalstrokecyr": 1181,
        "Kastrokecyr": 1182,
        "kastrokecyr": 1183,
        "Kabashkcyr": 1184,
        "kabashkcyr": 1185,
        "Entailcyr": 1186,
        "entailcyr": 1187,
        "Engecyr": 1188,
        "engecyr": 1189,
        "Pehookcyr": 1190,
        "pehookcyr": 1191,
        "Haabkhcyr": 1192,
        "haabkhcyr": 1193,
        "Estailcyr": 1194,
        "estailcyr": 1195,
        "Tetailcyr": 1196,
        "tetailcyr": 1197,
        "Ustraightcyr": 1198,
        "ustraightcyr": 1199,
        "Ustraightstrokecyr": 1200,
        "ustraightstrokecyr": 1201,
        "Xatailcyr": 1202,
        "xatailcyr": 1203,
        "Tetsecyr": 1204,
        "tetsecyr": 1205,
        "Chetailcyr": 1206,
        "chetailcyr": 1207,
        "Chevertcyr": 1208,
        "chevertcyr": 1209,
        "Shhacyr": 1210,
        "shhacyr": 1211,
        "Cheabkhcyr": 1212,
        "cheabkhcyr": 1213,
        "Cheabkhtailcyr": 1214,
        "cheabkhtailcyr": 1215,
        "Palochkacyr": 1216,
        "Zhebrevecyr": 1217,
        "zhebrevecyr": 1218,
        "Kahookcyr": 1219,
        "kahookcyr": 1220,
        "Elsharptailcyr": 1221,
        "elsharptailcyr": 1222,
        "Enhookcyr": 1223,
        "enhookcyr": 1224,
        "Ensharptailcyr": 1225,
        "ensharptailcyr": 1226,
        "Chekhakascyr": 1227,
        "chekhakascyr": 1228,
        "Emsharptailcyr": 1229,
        "emsharptailcyr": 1230,
        "palochkacyr": 1231,
        "Abrevecyr": 1232,
        "abrevecyr": 1233,
        "Adieresiscyr": 1234,
        "adieresiscyr": 1235,
        "Aiecyr": 1236,
        "aiecyr": 1237,
        "Iebrevecyr": 1238,
        "iebrevecyr": 1239,
        "Schwacyr": 1240,
        "schwacyr": 1241,
        "Schwadieresiscyr": 1242,
        "schwadieresiscyr": 1243,
        "Zhedieresiscyr": 1244,
        "zhedieresiscyr": 1245,
        "Zedieresiscyr": 1246,
        "zedieresiscyr": 1247,
        "Dzeabkhcyr": 1248,
        "dzeabkhcyr": 1249,
        "Imacroncyr": 1250,
        "imacroncyr": 1251,
        "Idieresiscyr": 1252,
        "idieresiscyr": 1253,
        "Odieresiscyr": 1254,
        "odieresiscyr": 1255,
        "Obarcyr": 1256,
        "obarcyr": 1257,
        "Obardieresiscyr": 1258,
        "obardieresiscyr": 1259,
        "Ereverseddieresiscyr": 1260,
        "ereverseddieresiscyr": 1261,
        "Umacroncyr": 1262,
        "umacroncyr": 1263,
        "Udieresiscyr": 1264,
        "udieresiscyr": 1265,
        "Uacutedblcyr": 1266,
        "uacutedblcyr": 1267,
        "Chedieresiscyr": 1268,
        "chedieresiscyr": 1269,
        "Getailcyr": 1270,
        "getailcyr": 1271,
        "Ylongdieresiscyr": 1272,
        "ylongdieresiscyr": 1273,
        "Gehookstrokecyr": 1274,
        "gehookstrokecyr": 1275,
        "Hahookcyr": 1276,
        "hahookcyr": 1277,
        "Hastrokecyr": 1278,
        "hastrokecyr": 1279,
        "Dekomicyr": 1280,
        "dekomicyr": 1281,
        "Djekomicyr": 1282,
        "djekomicyr": 1283,
        "Zjekomicyr": 1284,
        "zjekomicyr": 1285,
        "Dzjekomicyr": 1286,
        "dzjekomicyr": 1287,
        "Ljekomicyr": 1288,
        "ljekomicyr": 1289,
        "Njekomicyr": 1290,
        "njekomicyr": 1291,
        "Sjekomicyr": 1292,
        "sjekomicyr": 1293,
        "Tjekomicyr": 1294,
        "tjekomicyr": 1295,
        "Reversedzecyr": 1296,
        "reversedzecyr": 1297,
        "Elhookcyr": 1298,
        "elhookcyr": 1299,
        "Lhacyr": 1300,
        "lhacyr": 1301,
        "Rhacyr": 1302,
        "rhacyr": 1303,
        "Yaecyr": 1304,
        "yaecyr": 1305,
        "Qacyr": 1306,
        "qacyr": 1307,
        "Wecyr": 1308,
        "wecyr": 1309,
        "Kaaleutcyr": 1310,
        "kaaleutcyr": 1311,
        "Elmiddlehookcyr": 1312,
        "elmiddlehookcyr": 1313,
        "Enmiddlehookcyr": 1314,
        "enmiddlehookcyr": 1315,
        "Petailcyr": 1316,
        "petailcyr": 1317,
        "Shhatailcyr": 1318,
        "shhatailcyr": 1319,
        "Enhookleftcyr": 1320,
        "enhookleftcyr": 1321,
        "Dzzhecyr": 1322,
        "dzzhecyr": 1323,
        "Dchecyr": 1324,
        "dchecyr": 1325,
        "Eltailcyr": 1326,
        "eltailcyr": 1327,
        "Aybarmn": 1329,
        "Benarmn": 1330,
        "Gimarmn": 1331,
        "Daarmn": 1332,
        "Echarmn": 1333,
        "Zaarmn": 1334,
        "Eharmn": 1335,
        "Etarmn": 1336,
        "Toarmn": 1337,
        "Zhearmn": 1338,
        "Iniarmn": 1339,
        "Liwnarmn": 1340,
        "Xeharmn": 1341,
        "Caarmn": 1342,
        "Kenarmn": 1343,
        "Hoarmn": 1344,
        "Jaarmn": 1345,
        "Ghadarmn": 1346,
        "Cheharmn": 1347,
        "Menarmn": 1348,
        "Yiarmn": 1349,
        "Nowarmn": 1350,
        "Shaarmn": 1351,
        "Voarmn": 1352,
        "Chaarmn": 1353,
        "Peharmn": 1354,
        "Jheharmn": 1355,
        "Raarmn": 1356,
        "Seharmn": 1357,
        "Vewarmn": 1358,
        "Tiwnarmn": 1359,
        "Reharmn": 1360,
        "Coarmn": 1361,
        "Yiwnarmn": 1362,
        "Piwrarmn": 1363,
        "Keharmn": 1364,
        "Oharmn": 1365,
        "Feharmn": 1366,
        "ringhalfleftarmn": 1369,
        "apostrophearmn": 1370,
        "emphasismarkarmn": 1371,
        "exclamarmn": 1372,
        "commaarmn": 1373,
        "questionarmn": 1374,
        "abbreviationmarkarmn": 1375,
        "turnedaybarmn": 1376,
        "aybarmn": 1377,
        "benarmn": 1378,
        "gimarmn": 1379,
        "daarmn": 1380,
        "echarmn": 1381,
        "zaarmn": 1382,
        "eharmn": 1383,
        "etarmn": 1384,
        "toarmn": 1385,
        "zhearmn": 1386,
        "iniarmn": 1387,
        "liwnarmn": 1388,
        "xeharmn": 1389,
        "caarmn": 1390,
        "kenarmn": 1391,
        "hoarmn": 1392,
        "jaarmn": 1393,
        "ghadarmn": 1394,
        "cheharmn": 1395,
        "menarmn": 1396,
        "yiarmn": 1397,
        "nowarmn": 1398,
        "shaarmn": 1399,
        "voarmn": 1400,
        "chaarmn": 1401,
        "peharmn": 1402,
        "jheharmn": 1403,
        "raarmn": 1404,
        "seharmn": 1405,
        "vewarmn": 1406,
        "tiwnarmn": 1407,
        "reharmn": 1408,
        "coarmn": 1409,
        "yiwnarmn": 1410,
        "piwrarmn": 1411,
        "keharmn": 1412,
        "oharmn": 1413,
        "feharmn": 1414,
        "ech_yiwnarmn": 1415,
        "yiwithstrokearmn": 1416,
        "periodarmn": 1417,
        "hyphenarmn": 1418,
        "rightfacingeternitysignarmn": 1421,
        "leftfacingeternitysignarmn": 1422,
        "dramsignarmn": 1423,
        "etnahta:hb": 1425,
        "segolta:hb": 1426,
        "shalshelet:hb": 1427,
        "zaqefQatan:hb": 1428,
        "zaqefGadol:hb": 1429,
        "tifcha:hb": 1430,
        "revia:hb": 1431,
        "zarqa:hb": 1432,
        "pashta:hb": 1433,
        "yetiv:hb": 1434,
        "tevir:hb": 1435,
        "azla:hb": 1436,
        "gereshMuqdam:hb": 1437,
        "SheneGerishin:hb": 1438,
        "qarneFarah:hb": 1439,
        "telishaGedolah:hb": 1440,
        "pazer:hb": 1441,
        "atnachHafukh:hb": 1442,
        "munach:hb": 1443,
        "mahpach:hb": 1444,
        "mercha:hb": 1445,
        "merchaKefulah:hb": 1446,
        "darga:hb": 1447,
        "qadma:hb": 1448,
        "telishaQetannah:hb": 1449,
        "yerachBenYomo:hb": 1450,
        "ole:hb": 1451,
        "iluy:hb": 1452,
        "dehi:hb": 1453,
        "tsinnorit:hb": 1454,
        "masoraCircle:hb": 1455,
        "sheva:hb": 1456,
        "hatafSegol:hb": 1457,
        "hatafPatah:hb": 1458,
        "hatafQamats:hb": 1459,
        "hiriq:hb": 1460,
        "tsere:hb": 1461,
        "segol:hb": 1462,
        "patah:hb": 1463,
        "qamats:hb": 1464,
        "holam:hb": 1465,
        "holamHaser:hb": 1466,
        "qubuts:hb": 1467,
        "dagesh:hb": 1468,
        "meteg:hb": 1469,
        "maqaf:hb": 1470,
        "rafe:hb": 1471,
        "paseq:hb": 1472,
        "shinDot:hb": 1473,
        "sinDot:hb": 1474,
        "sofPasuq:hb": 1475,
        "dotupper:hb": 1476,
        "dotlower:hb": 1477,
        "nunHafukha:hb": 1478,
        "qamatsQatan:hb": 1479,
        "alef:hb": 1488,
        "bet:hb": 1489,
        "gimel:hb": 1490,
        "dalet:hb": 1491,
        "he:hb": 1492,
        "vav:hb": 1493,
        "zayin:hb": 1494,
        "het:hb": 1495,
        "tet:hb": 1496,
        "yod:hb": 1497,
        "finalkaf:hb": 1498,
        "kaf:hb": 1499,
        "lamed:hb": 1500,
        "finalmem:hb": 1501,
        "mem:hb": 1502,
        "finalnun:hb": 1503,
        "nun:hb": 1504,
        "samekh:hb": 1505,
        "ayin:hb": 1506,
        "finalpe:hb": 1507,
        "pe:hb": 1508,
        "finaltsadi:hb": 1509,
        "tsadi:hb": 1510,
        "qof:hb": 1511,
        "resh:hb": 1512,
        "shin:hb": 1513,
        "tav:hb": 1514,
        "yodtriangle:hb": 1519,
        "vav_vav:hb": 1520,
        "vav_yod:hb": 1521,
        "yod_yod:hb": 1522,
        "geresh:hb": 1523,
        "gershayim:hb": 1524,
        "arnumbersign": 1536,
        "Sanah": 1537,
        "footnote": 1538,
        "Safha": 1539,
        "samvat": 1540,
        "numbermarkabove": 1541,
        "arcuberoot": 1542,
        "arfourthroot": 1543,
        "ray": 1544,
        "permille": 1545,
        "arperthousand": 1546,
        "afghani": 1547,
        "arcomma": 1548,
        "dateseparator": 1549,
        "poeticverse": 1550,
        "misra": 1551,
        "HonSAW": 1552,
        "HonAA": 1553,
        "HonRA": 1554,
        "aHonRAA": 1555,
        "takhallus": 1556,
        "tahabove": 1557,
        "alefLamYehabove": 1558,
        "zainabove": 1559,
        "fathasmall": 1560,
        "dammasmall": 1561,
        "kasrasmall": 1562,
        "arsemicolon": 1563,
        "mark": 1564,
        "tripledot": 1566,
        "arquestion": 1567,
        "kashmiriyeh": 1568,
        "hamza": 1569,
        "alefmadda": 1570,
        "alefhamza": 1571,
        "wawhamza": 1572,
        "alefhamzabelow": 1573,
        "yehhamza": 1574,
        "alef": 1575,
        "beh": 1576,
        "tehmarbuta": 1577,
        "teh": 1578,
        "theh": 1579,
        "jeem": 1580,
        "hah": 1581,
        "khah": 1582,
        "dal": 1583,
        "thal": 1584,
        "reh": 1585,
        "zain": 1586,
        "seen": 1587,
        "sheen": 1588,
        "sad": 1589,
        "dad": 1590,
        "tah": 1591,
        "zah": 1592,
        "arain": 1593,
        "ghain": 1594,
        "kehehtwodotsabove": 1595,
        "kehehthreedotsbelow": 1596,
        "yehfarsiinvertedV": 1597,
        "yehfarsitwodotsabove": 1598,
        "yehfarsithreedotsabove": 1599,
        "kashida": 1600,
        "feh": 1601,
        "qaf": 1602,
        "kaf": 1603,
        "lam": 1604,
        "meem": 1605,
        "noon": 1606,
        "heh": 1607,
        "waw": 1608,
        "alefmaksura": 1609,
        "yeh": 1610,
        "fathatan": 1611,
        "dammatan": 1612,
        "kasratan": 1613,
        "fatha": 1614,
        "damma": 1615,
        "kasra": 1616,
        "shadda": 1617,
        "sukun": 1618,
        "madda": 1619,
        "hamzaabove": 1620,
        "hamzabelow": 1621,
        "subscriptalef": 1622,
        "inverteddamma": 1623,
        "marknoonghunna": 1624,
        "zwarakay": 1625,
        "vowelVabove": 1626,
        "vowelinvertedVabove": 1627,
        "voweldotbelow": 1628,
        "dammareversed": 1629,
        "fathatwodotsdots": 1630,
        "wavyhamzabelow": 1631,
        "arzero": 1776,
        "arone": 1777,
        "artwo": 1778,
        "arthree": 1779,
        "arfour": 1780,
        "arfive": 1781,
        "arsix": 1782,
        "arseven": 1783,
        "areight": 1784,
        "arnine": 1785,
        "arpercent": 1642,
        "ardecimalseparator": 1643,
        "thousandsseparator": 1644,
        "fivepointedstar": 1645,
        "dotlessbeh": 1646,
        "dotlessqaf": 1647,
        "alefabove": 1648,
        "alefwasla": 1649,
        "alefwavyhamza": 1650,
        "alefwavyhamzabelow": 1651,
        "highhamza": 1652,
        "alefhighhamza": 1653,
        "wawhighhamza": 1654,
        "uhamza": 1655,
        "yehhighhamza": 1656,
        "tteh": 1657,
        "tteheh": 1658,
        "beeh": 1659,
        "tehring": 1660,
        "tehdownthreedotsabove": 1661,
        "peh": 1662,
        "teheh": 1663,
        "beheh": 1664,
        "hahhamza": 1665,
        "hahtwodotsvertical": 1666,
        "nyeh": 1667,
        "dyeh": 1668,
        "hahthreedotsabove": 1669,
        "tcheh": 1670,
        "tcheheh": 1671,
        "ddal": 1672,
        "dalring": 1673,
        "daldotbelow": 1674,
        "daldotbelowtahsmall": 1675,
        "dahal": 1676,
        "ddahal": 1677,
        "dul": 1678,
        "daldownthreedotsabove": 1679,
        "dalfourdotsabove": 1680,
        "rreh": 1681,
        "rehVabove": 1682,
        "rehring": 1683,
        "rehdotbelow": 1684,
        "rehVbelow": 1685,
        "rehdotbelowdotabove": 1686,
        "rehtwodotsabove": 1687,
        "jeh": 1688,
        "rehfourdotsabove": 1689,
        "seendotbelowdotabove": 1690,
        "seenthreedotsbelow": 1691,
        "seenthreedotsbelowthreedotsabove": 1692,
        "sadtwodotsbelow": 1693,
        "sadthreedotsabove": 1694,
        "tahthreedotsabove": 1695,
        "ainthreedotsabove": 1696,
        "dotlessfeh": 1697,
        "fehdotbelowright": 1698,
        "fehdotbelow": 1699,
        "veh": 1700,
        "fehthreedotsbelow": 1701,
        "peheh": 1702,
        "qafdotabove": 1703,
        "qafthreedotsabove": 1704,
        "keheh": 1705,
        "kafswash": 1706,
        "kafring": 1707,
        "kafdotabove": 1708,
        "ng": 1709,
        "kafthreedotsbelow": 1710,
        "gaf": 1711,
        "gafring": 1712,
        "ngoeh": 1713,
        "gaftwodotsbelow": 1714,
        "gueh": 1715,
        "gafthreedotsabove": 1716,
        "lamVabove": 1717,
        "lamdotabove": 1718,
        "lamthreedotsabove": 1719,
        "lamthreedotsbelow": 1720,
        "noondotbelow": 1721,
        "noonghunna": 1722,
        "rnoon": 1723,
        "noonring": 1724,
        "noonthreedotsabove": 1725,
        "hehdoachashmee": 1726,
        "tchehdotabove": 1727,
        "hehyeh": 1728,
        "hehgoal": 1729,
        "hehgoalhamza": 1730,
        "tehmarbutagoal": 1731,
        "wawring": 1732,
        "oekirghiz": 1733,
        "aroe": 1734,
        "aru": 1735,
        "aryu": 1736,
        "yukirghiz": 1737,
        "wawtwodotsabove": 1738,
        "ve": 1739,
        "yehfarsi": 1740,
        "yehtail": 1741,
        "yehVabove": 1742,
        "wawdotabove": 1743,
        "are": 1744,
        "yehthreedotsbelow": 1745,
        "yehbarree": 1746,
        "yehbarreehamza": 1747,
        "periodurdu": 1748,
        "arae": 1749,
        "sad_lam_alefmaksuraabove": 1750,
        "qaf_lam_alefmaksuraabove": 1751,
        "meemabove.init": 1752,
        "lamalefabove": 1753,
        "jeemabove": 1754,
        "threedotsaboveabove": 1755,
        "seenabove": 1756,
        "Ayahend": 1757,
        "RubElHizbstart": 1758,
        "roundedzeroabove": 1759,
        "zerosquareabove": 1760,
        "dotlesskhahabove": 1761,
        "meemabove": 1762,
        "seenlow": 1763,
        "maddaabove": 1764,
        "wawsmall": 1765,
        "yehsmall": 1766,
        "yehabove": 1767,
        "noonabove": 1768,
        "Sajdah": 1769,
        "stopbelow": 1770,
        "stopabove": 1771,
        "filledstopabove": 1772,
        "meembelow": 1773,
        "dalinvertedV": 1774,
        "rehinvertedV": 1775,
        "sheendotbelow": 1786,
        "daddotbelow": 1787,
        "ghaindotbelow": 1788,
        "ampersandSindhi": 1789,
        "menpostSindhi": 1790,
        "hehinvertedV": 1791,
        "behThreeDotsHorizontallyBelow": 1872,
        "behDotBelowThreeDotsAbove": 1873,
        "behThreeDotsUpBelow": 1874,
        "behThreeDotsUpBelowTwoDotsAbove": 1875,
        "behTwoDotsBelowDotAbove": 1876,
        "behInvertedSmallVBelow": 1877,
        "behSmallV": 1878,
        "hahTwoDotsAbove": 1879,
        "hahThreeDotsUpBelow": 1880,
        "dalTwoDotsVerticallyBelowSmallTah": 1881,
        "dalInvertedSmallVBelow": 1882,
        "rehStroke": 1883,
        "seenFourDotsAbove": 1884,
        "ainTwoDotsAbove": 1885,
        "ainThreeDotsDownAbove": 1886,
        "ainTwoDotsVerticallyAbove": 1887,
        "fehTwoDotsBelow": 1888,
        "fehThreeDotsUpBelow": 1889,
        "kehehDotAbove": 1890,
        "kehehThreeDotsAbove": 1891,
        "kehehThreeDotsUpBelow": 1892,
        "meemDotAbove": 1893,
        "meemDotBelow": 1894,
        "noonTwoDotsBelow": 1895,
        "noonSmallTah": 1896,
        "noonSmallV": 1897,
        "lamBar": 1898,
        "rehTwoDotsVerticallyAbove": 1899,
        "rehHamzaAbove": 1900,
        "seenTwoDotsVerticallyAbove": 1901,
        "hahSmallTahBelow": 1902,
        "hahSmallTahTwoDots": 1903,
        "seenSmallTahTwoDots": 1904,
        "rehSmallTahTwoDots": 1905,
        "hahSmallTahAbove": 1906,
        "alefDigitTwoAbove": 1907,
        "alefDigitThreeAbove": 1908,
        "farsiYehDigitTwoAbove": 1909,
        "farsiYehDigitThreeAbove": 1910,
        "farsiYehDigitFourBelow": 1911,
        "wawDigitTwoAbove": 1912,
        "wawDigitThreeAbove": 1913,
        "yehBarreeDigitTwoAbove": 1914,
        "yehBarreeDigitThreeAbove": 1915,
        "hahDigitFourBelow": 1916,
        "seenDigitFourAbove": 1917,
        "seenInvertedV": 1918,
        "kafTwoDotsAbove": 1919,
        "deva:candrabinduinverted": 2304,
        "deva:candrabindu": 2305,
        "deva:anusvara": 2306,
        "deva:visarga": 2307,
        "deva:ashort": 2308,
        "deva:a": 2309,
        "deva:aa": 2310,
        "deva:i": 2311,
        "deva:ii": 2312,
        "deva:u": 2313,
        "deva:uu": 2314,
        "deva:vocalicr": 2315,
        "deva:vocalicl": 2316,
        "deva:ecandra": 2317,
        "deva:eshort": 2318,
        "deva:e": 2319,
        "deva:ai": 2320,
        "deva:ocandra": 2321,
        "deva:oshort": 2322,
        "deva:o": 2323,
        "deva:au": 2324,
        "deva:ka": 2325,
        "deva:kha": 2326,
        "deva:ga": 2327,
        "deva:gha": 2328,
        "deva:nga": 2329,
        "deva:ca": 2330,
        "deva:cha": 2331,
        "deva:ja": 2332,
        "deva:jha": 2333,
        "deva:nya": 2334,
        "deva:tta": 2335,
        "deva:ttha": 2336,
        "deva:dda": 2337,
        "deva:ddha": 2338,
        "deva:nna": 2339,
        "deva:ta": 2340,
        "deva:tha": 2341,
        "deva:da": 2342,
        "deva:dha": 2343,
        "deva:na": 2344,
        "deva:nnna": 2345,
        "deva:pa": 2346,
        "deva:pha": 2347,
        "deva:ba": 2348,
        "deva:bha": 2349,
        "deva:ma": 2350,
        "deva:ya": 2351,
        "deva:ra": 2352,
        "deva:rra": 2353,
        "deva:la": 2354,
        "deva:lla": 2355,
        "deva:llla": 2356,
        "deva:va": 2357,
        "deva:sha": 2358,
        "deva:ssa": 2359,
        "deva:sa": 2360,
        "deva:ha": 2361,
        "deva:oesign": 2362,
        "deva:ooesign": 2363,
        "deva:nukta": 2364,
        "deva:avagraha": 2365,
        "deva:aasign": 2366,
        "deva:isign": 2367,
        "deva:iisign": 2368,
        "deva:usign": 2369,
        "deva:uusign": 2370,
        "deva:vocalicrsign": 2371,
        "deva:vocalicrrsign": 2372,
        "deva:esigncandra": 2373,
        "deva:esignshort": 2374,
        "deva:esign": 2375,
        "deva:aisign": 2376,
        "deva:osigncandra": 2377,
        "deva:osignshort": 2378,
        "deva:osign": 2379,
        "deva:ausign": 2380,
        "deva:virama": 2381,
        "deva:esignprishthamatra": 2382,
        "deva:awsign": 2383,
        "deva:om": 2384,
        "deva:udatta": 2385,
        "deva:anudatta": 2386,
        "deva:grave": 2387,
        "deva:acute": 2388,
        "deva:esigncandralong": 2389,
        "deva:uesign": 2390,
        "deva:uuesign": 2391,
        "deva:qa": 2392,
        "deva:khha": 2393,
        "deva:ghha": 2394,
        "deva:za": 2395,
        "deva:dddha": 2396,
        "deva:rha": 2397,
        "deva:fa": 2398,
        "deva:yya": 2399,
        "deva:vocalicrr": 2400,
        "deva:vocalicll": 2401,
        "deva:vocaliclsign": 2402,
        "deva:vocalicllsign": 2403,
        "deva:danda": 2404,
        "deva:dbldanda": 2405,
        "deva:zero": 2406,
        "deva:one": 2407,
        "deva:two": 2408,
        "deva:three": 2409,
        "deva:four": 2410,
        "deva:five": 2411,
        "deva:six": 2412,
        "deva:seven": 2413,
        "deva:eight": 2414,
        "deva:nine": 2415,
        "deva:abbreviation": 2416,
        "deva:dothigh": 2417,
        "deva:acandra": 2418,
        "deva:oe": 2419,
        "deva:ooe": 2420,
        "deva:aw": 2421,
        "deva:ue": 2422,
        "deva:uue": 2423,
        "deva:marwaridda": 2424,
        "deva:zha": 2425,
        "deva:yaheavy": 2426,
        "deva:gga": 2427,
        "deva:jja": 2428,
        "deva:glottalstop": 2429,
        "deva:ddda": 2430,
        "deva:bba": 2431,
        "beng:anji": 2432,
        "beng:candrabindu": 2433,
        "beng:anusvara": 2434,
        "beng:visarga": 2435,
        "beng:a": 2437,
        "beng:aa": 2438,
        "beng:i": 2439,
        "beng:ii": 2440,
        "beng:u": 2441,
        "beng:uu": 2442,
        "beng:vocalicr": 2443,
        "beng:vocalicl": 2444,
        "beng:e": 2447,
        "beng:ai": 2448,
        "beng:o": 2451,
        "beng:au": 2452,
        "beng:ka": 2453,
        "beng:kha": 2454,
        "beng:ga": 2455,
        "beng:gha": 2456,
        "beng:nga": 2457,
        "beng:ca": 2458,
        "beng:cha": 2459,
        "beng:ja": 2460,
        "beng:jha": 2461,
        "beng:nya": 2462,
        "beng:tta": 2463,
        "beng:ttha": 2464,
        "beng:dda": 2465,
        "beng:ddha": 2466,
        "beng:nna": 2467,
        "beng:ta": 2468,
        "beng:tha": 2469,
        "beng:da": 2470,
        "beng:dha": 2471,
        "beng:na": 2472,
        "beng:pa": 2474,
        "beng:pha": 2475,
        "beng:ba": 2476,
        "beng:bha": 2477,
        "beng:ma": 2478,
        "beng:ya": 2479,
        "beng:ra": 2480,
        "beng:la": 2482,
        "beng:sha": 2486,
        "beng:ssa": 2487,
        "beng:sa": 2488,
        "beng:ha": 2489,
        "beng:nukta": 2492,
        "beng:avagraha": 2493,
        "beng:aasign": 2494,
        "beng:isign": 2495,
        "beng:iisign": 2496,
        "beng:usign": 2497,
        "beng:uusign": 2498,
        "beng:vocalicrsign": 2499,
        "beng:vocalicrrsign": 2500,
        "beng:esign": 2503,
        "beng:aisign": 2504,
        "beng:osign": 2507,
        "beng:ausign": 2508,
        "beng:virama": 2509,
        "beng:khandata": 2510,
        "beng:aulengthmark": 2519,
        "beng:rra": 2524,
        "beng:rha": 2525,
        "beng:yya": 2527,
        "beng:vocalicrr": 2528,
        "beng:vocalicll": 2529,
        "beng:vocaliclsign": 2530,
        "beng:vocalicllsign": 2531,
        "beng:zero": 2534,
        "beng:one": 2535,
        "beng:two": 2536,
        "beng:three": 2537,
        "beng:four": 2538,
        "beng:five": 2539,
        "beng:six": 2540,
        "beng:seven": 2541,
        "beng:eight": 2542,
        "beng:nine": 2543,
        "beng:ramiddiagonal": 2544,
        "beng:ralowdiagonal": 2545,
        "beng:rupeemark": 2546,
        "beng:rupee": 2547,
        "beng:onecurrencynumerator": 2548,
        "beng:twocurrencynumerator": 2549,
        "beng:threecurrencynumerator": 2550,
        "beng:fourcurrencynumerator": 2551,
        "beng:currencyoneless": 2552,
        "beng:sixteencurrencydenominator": 2553,
        "beng:isshar": 2554,
        "beng:gandamark": 2555,
        "beng:vedicanusvara": 2556,
        "beng:abbreviationsign": 2557,
        "beng:sandhimark": 2558,
        "guru:adakbindisign": 2561,
        "guru:bindisign": 2562,
        "guru:visarga": 2563,
        "guru:a": 2565,
        "guru:aa": 2566,
        "guru:i": 2567,
        "guru:ii": 2568,
        "guru:u": 2569,
        "guru:uu": 2570,
        "guru:ee": 2575,
        "guru:ai": 2576,
        "guru:oo": 2579,
        "guru:au": 2580,
        "guru:ka": 2581,
        "guru:kha": 2582,
        "guru:ga": 2583,
        "guru:gha": 2584,
        "guru:nga": 2585,
        "guru:ca": 2586,
        "guru:cha": 2587,
        "guru:ja": 2588,
        "guru:jha": 2589,
        "guru:nya": 2590,
        "guru:tta": 2591,
        "guru:ttha": 2592,
        "guru:dda": 2593,
        "guru:ddha": 2594,
        "guru:nna": 2595,
        "guru:ta": 2596,
        "guru:tha": 2597,
        "guru:da": 2598,
        "guru:dha": 2599,
        "guru:na": 2600,
        "guru:pa": 2602,
        "guru:pha": 2603,
        "guru:ba": 2604,
        "guru:bha": 2605,
        "guru:ma": 2606,
        "guru:ya": 2607,
        "guru:ra": 2608,
        "guru:la": 2610,
        "guru:lla": 2611,
        "guru:va": 2613,
        "guru:sha": 2614,
        "guru:sa": 2616,
        "guru:ha": 2617,
        "guru:nukta": 2620,
        "guru:aasign": 2622,
        "guru:isign": 2623,
        "guru:iisign": 2624,
        "guru:usign": 2625,
        "guru:uusign": 2626,
        "guru:eesign": 2631,
        "guru:aisign": 2632,
        "guru:oosign": 2635,
        "guru:ausign": 2636,
        "guru:virama": 2637,
        "guru:udaatsign": 2641,
        "guru:khha": 2649,
        "guru:ghha": 2650,
        "guru:za": 2651,
        "guru:rra": 2652,
        "guru:fa": 2654,
        "guru:zero": 2662,
        "guru:one": 2663,
        "guru:two": 2664,
        "guru:three": 2665,
        "guru:four": 2666,
        "guru:five": 2667,
        "guru:six": 2668,
        "guru:seven": 2669,
        "guru:eight": 2670,
        "guru:nine": 2671,
        "guru:tippi": 2672,
        "guru:addak": 2673,
        "guru:iri": 2674,
        "guru:ura": 2675,
        "guru:ekonkar": 2676,
        "guru:yakashsign": 2677,
        "guru:abbreviationsign": 2678,
        "gujr:candrabindu": 2689,
        "gujr:anusvara": 2690,
        "gujr:visarga": 2691,
        "gujr:a": 2693,
        "gujr:aa": 2694,
        "gujr:i": 2695,
        "gujr:ii": 2696,
        "gujr:u": 2697,
        "gujr:uu": 2698,
        "gujr:vocalicr": 2699,
        "gujr:vocalicl": 2700,
        "gujr:ecandra": 2701,
        "gujr:e": 2703,
        "gujr:ai": 2704,
        "gujr:ocandra": 2705,
        "gujr:o": 2707,
        "gujr:au": 2708,
        "gujr:ka": 2709,
        "gujr:kha": 2710,
        "gujr:ga": 2711,
        "gujr:gha": 2712,
        "gujr:nga": 2713,
        "gujr:ca": 2714,
        "gujr:cha": 2715,
        "gujr:ja": 2716,
        "gujr:jha": 2717,
        "gujr:nya": 2718,
        "gujr:tta": 2719,
        "gujr:ttha": 2720,
        "gujr:dda": 2721,
        "gujr:ddha": 2722,
        "gujr:nna": 2723,
        "gujr:ta": 2724,
        "gujr:tha": 2725,
        "gujr:da": 2726,
        "gujr:dha": 2727,
        "gujr:na": 2728,
        "gujr:pa": 2730,
        "gujr:pha": 2731,
        "gujr:ba": 2732,
        "gujr:bha": 2733,
        "gujr:ma": 2734,
        "gujr:ya": 2735,
        "gujr:ra": 2736,
        "gujr:la": 2738,
        "gujr:lla": 2739,
        "gujr:va": 2741,
        "gujr:sha": 2742,
        "gujr:ssa": 2743,
        "gujr:sa": 2744,
        "gujr:ha": 2745,
        "gujr:nukta": 2748,
        "gujr:avagraha": 2749,
        "gujr:aasign": 2750,
        "gujr:isign": 2751,
        "gujr:iisign": 2752,
        "gujr:usign": 2753,
        "gujr:uusign": 2754,
        "gujr:vocalicrsign": 2755,
        "gujr:vocalicrrsign": 2756,
        "gujr:esigncandra": 2757,
        "gujr:esign": 2759,
        "gujr:aisign": 2760,
        "gujr:osigncandra": 2761,
        "gujr:osign": 2763,
        "gujr:ausign": 2764,
        "gujr:virama": 2765,
        "gujr:om": 2768,
        "gujr:vocalicrr": 2784,
        "gujr:vocalicll": 2785,
        "gujr:vocaliclsign": 2786,
        "gujr:vocalicllsign": 2787,
        "gujr:zero": 2790,
        "gujr:one": 2791,
        "gujr:two": 2792,
        "gujr:three": 2793,
        "gujr:four": 2794,
        "gujr:five": 2795,
        "gujr:six": 2796,
        "gujr:seven": 2797,
        "gujr:eight": 2798,
        "gujr:nine": 2799,
        "gujr:abbreviation": 2800,
        "gujr:rupee": 2801,
        "gujr:zha": 2809,
        "gujr:sukun": 2810,
        "gujr:shadda": 2811,
        "gujr:maddah": 2812,
        "gujr:threedotnuktaabove": 2813,
        "gujr:circlenuktaabove": 2814,
        "gujr:twocirclenuktaabove": 2815,
        "orya:candrabindu": 2817,
        "orya:anusvara": 2818,
        "orya:visarga": 2819,
        "orya:a": 2821,
        "orya:aa": 2822,
        "orya:i": 2823,
        "orya:ii": 2824,
        "orya:u": 2825,
        "orya:uu": 2826,
        "orya:vocalicr": 2827,
        "orya:vocalicl": 2828,
        "orya:e": 2831,
        "orya:ai": 2832,
        "orya:o": 2835,
        "orya:au": 2836,
        "orya:ka": 2837,
        "orya:kha": 2838,
        "orya:ga": 2839,
        "orya:gha": 2840,
        "orya:nga": 2841,
        "orya:ca": 2842,
        "orya:cha": 2843,
        "orya:ja": 2844,
        "orya:jha": 2845,
        "orya:nya": 2846,
        "orya:tta": 2847,
        "orya:ttha": 2848,
        "orya:dda": 2849,
        "orya:ddha": 2850,
        "orya:nna": 2851,
        "orya:ta": 2852,
        "orya:tha": 2853,
        "orya:da": 2854,
        "orya:dha": 2855,
        "orya:na": 2856,
        "orya:pa": 2858,
        "orya:pha": 2859,
        "orya:ba": 2860,
        "orya:bha": 2861,
        "orya:ma": 2862,
        "orya:ya": 2863,
        "orya:ra": 2864,
        "orya:la": 2866,
        "orya:lla": 2867,
        "orya:va": 2869,
        "orya:sha": 2870,
        "orya:ssa": 2871,
        "orya:sa": 2872,
        "orya:ha": 2873,
        "orya:nukta": 2876,
        "orya:avagraha": 2877,
        "orya:aasign": 2878,
        "orya:isign": 2879,
        "orya:iisign": 2880,
        "orya:usign": 2881,
        "orya:uusign": 2882,
        "orya:vocalicrsign": 2883,
        "orya:vocalicrrsign": 2884,
        "orya:esign": 2887,
        "orya:aisign": 2888,
        "orya:osign": 2891,
        "orya:ausign": 2892,
        "orya:virama": 2893,
        "orya:ailengthmark": 2902,
        "orya:aulengthmark": 2903,
        "orya:rra": 2908,
        "orya:rha": 2909,
        "orya:yya": 2911,
        "orya:vocalicrr": 2912,
        "orya:vocalicll": 2913,
        "orya:vocaliclsign": 2914,
        "orya:vocalicllsign": 2915,
        "orya:zero": 2918,
        "orya:one": 2919,
        "orya:two": 2920,
        "orya:three": 2921,
        "orya:four": 2922,
        "orya:five": 2923,
        "orya:six": 2924,
        "orya:seven": 2925,
        "orya:eight": 2926,
        "orya:nine": 2927,
        "orya:isshar": 2928,
        "orya:wa": 2929,
        "orya:onequarter": 2930,
        "orya:onehalf": 2931,
        "orya:threequarters": 2932,
        "orya:onesixteenth": 2933,
        "orya:oneeighth": 2934,
        "orya:threesixteenths": 2935,
        "taml:anusvara": 2946,
        "taml:visarga": 2947,
        "taml:a": 2949,
        "taml:aa": 2950,
        "taml:i": 2951,
        "taml:ii": 2952,
        "taml:u": 2953,
        "taml:uu": 2954,
        "taml:e": 2958,
        "taml:ee": 2959,
        "taml:ai": 2960,
        "taml:o": 2962,
        "taml:oo": 2963,
        "taml:au": 2964,
        "taml:ka": 2965,
        "taml:nga": 2969,
        "taml:ca": 2970,
        "taml:ja": 2972,
        "taml:nya": 2974,
        "taml:tta": 2975,
        "taml:nna": 2979,
        "taml:ta": 2980,
        "taml:na": 2984,
        "taml:nnna": 2985,
        "taml:pa": 2986,
        "taml:ma": 2990,
        "taml:ya": 2991,
        "taml:ra": 2992,
        "taml:rra": 2993,
        "taml:la": 2994,
        "taml:lla": 2995,
        "taml:llla": 2996,
        "taml:va": 2997,
        "taml:sha": 2998,
        "taml:ssa": 2999,
        "taml:sa": 3000,
        "taml:ha": 3001,
        "taml:aasign": 3006,
        "taml:isign": 3007,
        "taml:iisign": 3008,
        "taml:usign": 3009,
        "taml:uusign": 3010,
        "taml:esign": 3014,
        "taml:eesign": 3015,
        "taml:aisign": 3016,
        "taml:osign": 3018,
        "taml:oosign": 3019,
        "taml:ausign": 3020,
        "taml:virama": 3021,
        "taml:om": 3024,
        "taml:aulengthmark": 3031,
        "taml:zero": 3046,
        "taml:one": 3047,
        "taml:two": 3048,
        "taml:three": 3049,
        "taml:four": 3050,
        "taml:five": 3051,
        "taml:six": 3052,
        "taml:seven": 3053,
        "taml:eight": 3054,
        "taml:nine": 3055,
        "taml:ten": 3056,
        "taml:onehundred": 3057,
        "taml:onethousand": 3058,
        "taml:daysign": 3059,
        "taml:monthsign": 3060,
        "taml:yearsign": 3061,
        "taml:debitsign": 3062,
        "taml:creditsign": 3063,
        "taml:asabovesign": 3064,
        "taml:rupee": 3065,
        "taml:sign": 3066,
        "telu:combiningcandrabinduabovesign": 3072,
        "telu:candrabindusign": 3073,
        "telu:anusvara": 3074,
        "telu:visarga": 3075,
        "telu:combininganusvaraabovesign": 3076,
        "telu:a": 3077,
        "telu:aa": 3078,
        "telu:i": 3079,
        "telu:ii": 3080,
        "telu:u": 3081,
        "telu:uu": 3082,
        "telu:vocalicr": 3083,
        "telu:vocalicl": 3084,
        "telu:e": 3086,
        "telu:ee": 3087,
        "telu:ai": 3088,
        "telu:o": 3090,
        "telu:oo": 3091,
        "telu:au": 3092,
        "telu:ka": 3093,
        "telu:kha": 3094,
        "telu:ga": 3095,
        "telu:gha": 3096,
        "telu:nga": 3097,
        "telu:ca": 3098,
        "telu:cha": 3099,
        "telu:ja": 3100,
        "telu:jha": 3101,
        "telu:nya": 3102,
        "telu:tta": 3103,
        "telu:ttha": 3104,
        "telu:dda": 3105,
        "telu:ddha": 3106,
        "telu:nna": 3107,
        "telu:ta": 3108,
        "telu:tha": 3109,
        "telu:da": 3110,
        "telu:dha": 3111,
        "telu:na": 3112,
        "telu:pa": 3114,
        "telu:pha": 3115,
        "telu:ba": 3116,
        "telu:bha": 3117,
        "telu:ma": 3118,
        "telu:ya": 3119,
        "telu:ra": 3120,
        "telu:rra": 3121,
        "telu:la": 3122,
        "telu:lla": 3123,
        "telu:llla": 3124,
        "telu:va": 3125,
        "telu:sha": 3126,
        "telu:ssa": 3127,
        "telu:sa": 3128,
        "telu:ha": 3129,
        "telu:avagraha": 3133,
        "telu:aasign": 3134,
        "telu:isign": 3135,
        "telu:iisign": 3136,
        "telu:usign": 3137,
        "telu:uusign": 3138,
        "telu:vocalicrsign": 3139,
        "telu:vocalicrrsign": 3140,
        "telu:esign": 3142,
        "telu:eesign": 3143,
        "telu:aisign": 3144,
        "telu:osign": 3146,
        "telu:oosign": 3147,
        "telu:ausign": 3148,
        "telu:virama": 3149,
        "telu:lengthmark": 3157,
        "telu:ailengthmark": 3158,
        "telu:tsa": 3160,
        "telu:dza": 3161,
        "telu:rrra": 3162,
        "telu:vocalicrr": 3168,
        "telu:vocalicll": 3169,
        "telu:vocaliclsign": 3170,
        "telu:vocalicllsign": 3171,
        "telu:zero": 3174,
        "telu:one": 3175,
        "telu:two": 3176,
        "telu:three": 3177,
        "telu:four": 3178,
        "telu:five": 3179,
        "telu:six": 3180,
        "telu:seven": 3181,
        "telu:eight": 3182,
        "telu:nine": 3183,
        "telu:siddhamsign": 3191,
        "telu:fractionzeroforoddpowersoffour": 3192,
        "telu:fractiononeforoddpowersoffour": 3193,
        "telu:fractiontwoforoddpowersoffour": 3194,
        "telu:fractionthreeforoddpowersoffour": 3195,
        "telu:fractiononeforevenpowersoffour": 3196,
        "telu:fractiontwoforevenpowersoffour": 3197,
        "telu:fractionthreeforevenpowersoffour": 3198,
        "telu:tuumusign": 3199,
        "knda:signspacingcandrabindu": 3200,
        "knda:signcandrabindu": 3201,
        "knda:anusvara": 3202,
        "knda:visarga": 3203,
        "knda:signsiddham": 3204,
        "knda:a": 3205,
        "knda:aa": 3206,
        "knda:i": 3207,
        "knda:ii": 3208,
        "knda:u": 3209,
        "knda:uu": 3210,
        "knda:rvocal": 3211,
        "knda:lvocal": 3212,
        "knda:e": 3214,
        "knda:ee": 3215,
        "knda:ai": 3216,
        "knda:o": 3218,
        "knda:oo": 3219,
        "knda:au": 3220,
        "knda:ka": 3221,
        "knda:kha": 3222,
        "knda:ga": 3223,
        "knda:gha": 3224,
        "knda:nga": 3225,
        "knda:ca": 3226,
        "knda:cha": 3227,
        "knda:ja": 3228,
        "knda:jha": 3229,
        "knda:nya": 3230,
        "knda:tta": 3231,
        "knda:ttha": 3232,
        "knda:dda": 3233,
        "knda:ddha": 3234,
        "knda:nna": 3235,
        "knda:ta": 3236,
        "knda:tha": 3237,
        "knda:da": 3238,
        "knda:dha": 3239,
        "knda:na": 3240,
        "knda:pa": 3242,
        "knda:pha": 3243,
        "knda:ba": 3244,
        "knda:bha": 3245,
        "knda:ma": 3246,
        "knda:ya": 3247,
        "knda:ra": 3248,
        "knda:rra": 3249,
        "knda:la": 3250,
        "knda:lla": 3251,
        "knda:va": 3253,
        "knda:sha": 3254,
        "knda:ssa": 3255,
        "knda:sa": 3256,
        "knda:ha": 3257,
        "knda:nukta": 3260,
        "knda:avagraha": 3261,
        "knda:aasign": 3262,
        "knda:isign": 3263,
        "knda:iisign": 3264,
        "knda:usign": 3265,
        "knda:uusign": 3266,
        "knda:rvocalsign": 3267,
        "knda:rrvocalsign": 3268,
        "knda:esign": 3270,
        "knda:eesign": 3271,
        "knda:aisign": 3272,
        "knda:osign": 3274,
        "knda:oosign": 3275,
        "knda:ausign": 3276,
        "knda:virama": 3277,
        "knda:length": 3285,
        "knda:ailength": 3286,
        "knda:fa": 3294,
        "knda:rrvocal": 3296,
        "knda:llvocal": 3297,
        "knda:lvocalsign": 3298,
        "knda:llvocalsign": 3299,
        "knda:zero": 3302,
        "knda:one": 3303,
        "knda:two": 3304,
        "knda:three": 3305,
        "knda:four": 3306,
        "knda:five": 3307,
        "knda:six": 3308,
        "knda:seven": 3309,
        "knda:eight": 3310,
        "knda:nine": 3311,
        "knda:jihvamuliya": 3313,
        "knda:upadhmaniya": 3314,
        "mlym:combininganusvaraabovesign": 3328,
        "mlym:candrabindusign": 3329,
        "mlym:anusvarasign": 3330,
        "mlym:visargasign": 3331,
        "mlym:a": 3333,
        "mlym:aa": 3334,
        "mlym:i": 3335,
        "mlym:ii": 3336,
        "mlym:u": 3337,
        "mlym:uu": 3338,
        "mlym:rvocal": 3339,
        "mlym:lvocal": 3340,
        "mlym:e": 3342,
        "mlym:ee": 3343,
        "mlym:ai": 3344,
        "mlym:o": 3346,
        "mlym:oo": 3347,
        "mlym:au": 3348,
        "mlym:ka": 3349,
        "mlym:kha": 3350,
        "mlym:ga": 3351,
        "mlym:gha": 3352,
        "mlym:nga": 3353,
        "mlym:ca": 3354,
        "mlym:cha": 3355,
        "mlym:ja": 3356,
        "mlym:jha": 3357,
        "mlym:nya": 3358,
        "mlym:tta": 3359,
        "mlym:ttha": 3360,
        "mlym:dda": 3361,
        "mlym:ddha": 3362,
        "mlym:nna": 3363,
        "mlym:ta": 3364,
        "mlym:tha": 3365,
        "mlym:da": 3366,
        "mlym:dha": 3367,
        "mlym:na": 3368,
        "mlym:nnna": 3369,
        "mlym:pa": 3370,
        "mlym:pha": 3371,
        "mlym:ba": 3372,
        "mlym:bha": 3373,
        "mlym:ma": 3374,
        "mlym:ya": 3375,
        "mlym:ra": 3376,
        "mlym:rra": 3377,
        "mlym:la": 3378,
        "mlym:lla": 3379,
        "mlym:llla": 3380,
        "mlym:va": 3381,
        "mlym:sha": 3382,
        "mlym:ssa": 3383,
        "mlym:sa": 3384,
        "mlym:ha": 3385,
        "mlym:ttta": 3386,
        "mlym:verticalbarviramasign": 3387,
        "mlym:circularviramasign": 3388,
        "mlym:avagrahasign": 3389,
        "mlym:aasign": 3390,
        "mlym:isign": 3391,
        "mlym:iisign": 3392,
        "mlym:usign": 3393,
        "mlym:uusign": 3394,
        "mlym:rvocalsign": 3395,
        "mlym:rrvocalsign": 3396,
        "mlym:esign": 3398,
        "mlym:eesign": 3399,
        "mlym:aisign": 3400,
        "mlym:osign": 3402,
        "mlym:oosign": 3403,
        "mlym:ausign": 3404,
        "mlym:viramasign": 3405,
        "mlym:dotreph": 3406,
        "mlym:parasign": 3407,
        "mlym:mchillu": 3412,
        "mlym:ychillu": 3413,
        "mlym:lllchillu": 3414,
        "mlym:aulength": 3415,
        "mlym:oneone-hundred-and-sixtieth": 3416,
        "mlym:onefortieth": 3417,
        "mlym:threeeightieths": 3418,
        "mlym:onetwentieth": 3419,
        "mlym:onetenth": 3420,
        "mlym:threetwentieths": 3421,
        "mlym:onefifth": 3422,
        "mlym:archaicii": 3423,
        "mlym:rrvocal": 3424,
        "mlym:llvocal": 3425,
        "mlym:lvocalsign": 3426,
        "mlym:llvocalsign": 3427,
        "mlym:zero": 3430,
        "mlym:one": 3431,
        "mlym:two": 3432,
        "mlym:three": 3433,
        "mlym:four": 3434,
        "mlym:five": 3435,
        "mlym:six": 3436,
        "mlym:seven": 3437,
        "mlym:eight": 3438,
        "mlym:nine": 3439,
        "mlym:ten": 3440,
        "mlym:onehundred": 3441,
        "mlym:onethousand": 3442,
        "mlym:onequarter": 3443,
        "mlym:onehalf": 3444,
        "mlym:threequarters": 3445,
        "mlym:onesixteenth": 3446,
        "mlym:oneeighth": 3447,
        "mlym:threesixteenths": 3448,
        "mlym:date": 3449,
        "mlym:nnchillu": 3450,
        "mlym:nchillu": 3451,
        "mlym:rrchillu": 3452,
        "mlym:lchillu": 3453,
        "mlym:llchillu": 3454,
        "mlym:kchillu": 3455,
        "sinh:anusvara": 3458,
        "sinh:visarga": 3459,
        "sinh:a": 3461,
        "sinh:aa": 3462,
        "sinh:ae": 3463,
        "sinh:aae": 3464,
        "sinh:i": 3465,
        "sinh:ii": 3466,
        "sinh:u": 3467,
        "sinh:uu": 3468,
        "sinh:vocalicr": 3469,
        "sinh:vocalicrr": 3470,
        "sinh:vocalicl": 3471,
        "sinh:vocalicll": 3472,
        "sinh:e": 3473,
        "sinh:ee": 3474,
        "sinh:ai": 3475,
        "sinh:o": 3476,
        "sinh:oo": 3477,
        "sinh:au": 3478,
        "sinh:ka": 3482,
        "sinh:kha": 3483,
        "sinh:ga": 3484,
        "sinh:gha": 3485,
        "sinh:nga": 3486,
        "sinh:nnga": 3487,
        "sinh:ca": 3488,
        "sinh:cha": 3489,
        "sinh:ja": 3490,
        "sinh:jha": 3491,
        "sinh:nya": 3492,
        "sinh:jnya": 3493,
        "sinh:nyja": 3494,
        "sinh:tta": 3495,
        "sinh:ttha": 3496,
        "sinh:dda": 3497,
        "sinh:ddha": 3498,
        "sinh:nna": 3499,
        "sinh:nndda": 3500,
        "sinh:ta": 3501,
        "sinh:tha": 3502,
        "sinh:da": 3503,
        "sinh:dha": 3504,
        "sinh:na": 3505,
        "sinh:nda": 3507,
        "sinh:pa": 3508,
        "sinh:pha": 3509,
        "sinh:ba": 3510,
        "sinh:bha": 3511,
        "sinh:ma": 3512,
        "sinh:mba": 3513,
        "sinh:ya": 3514,
        "sinh:ra": 3515,
        "sinh:la": 3517,
        "sinh:va": 3520,
        "sinh:sha": 3521,
        "sinh:ssa": 3522,
        "sinh:sa": 3523,
        "sinh:ha": 3524,
        "sinh:lla": 3525,
        "sinh:fa": 3526,
        "sinh:virama": 3530,
        "sinh:aasign": 3535,
        "sinh:aesign": 3536,
        "sinh:aaesign": 3537,
        "sinh:isign": 3538,
        "sinh:iisign": 3539,
        "sinh:usign": 3540,
        "sinh:uusign": 3542,
        "sinh:vocalicrsign": 3544,
        "sinh:esign": 3545,
        "sinh:eesign": 3546,
        "sinh:aisign": 3547,
        "sinh:osign": 3548,
        "sinh:oosign": 3549,
        "sinh:ausign": 3550,
        "sinh:vocaliclsign": 3551,
        "sinh:zerolith": 3558,
        "sinh:onelith": 3559,
        "sinh:twolith": 3560,
        "sinh:threelith": 3561,
        "sinh:fourlith": 3562,
        "sinh:fivelith": 3563,
        "sinh:sixlith": 3564,
        "sinh:sevenlith": 3565,
        "sinh:eightlith": 3566,
        "sinh:ninelith": 3567,
        "sinh:vocalicrrsign": 3570,
        "sinh:vocalicllsign": 3571,
        "sinh:kunddaliya": 3572,
        "thai:kokai": 3585,
        "thai:khokhai": 3586,
        "thai:khokhuat": 3587,
        "thai:khokhwai": 3588,
        "thai:khokhon": 3589,
        "thai:khorakhang": 3590,
        "thai:ngongu": 3591,
        "thai:chochan": 3592,
        "thai:choching": 3593,
        "thai:chochang": 3594,
        "thai:soso": 3595,
        "thai:chochoe": 3596,
        "thai:yoying": 3597,
        "thai:dochada": 3598,
        "thai:topatak": 3599,
        "thai:thothan": 3600,
        "thai:thonangmontho": 3601,
        "thai:thophuthao": 3602,
        "thai:nonen": 3603,
        "thai:dodek": 3604,
        "thai:totao": 3605,
        "thai:thothung": 3606,
        "thai:thothahan": 3607,
        "thai:thothong": 3608,
        "thai:nonu": 3609,
        "thai:bobaimai": 3610,
        "thai:popla": 3611,
        "thai:phophung": 3612,
        "thai:fofa": 3613,
        "thai:phophan": 3614,
        "thai:fofan": 3615,
        "thai:phosamphao": 3616,
        "thai:moma": 3617,
        "thai:yoyak": 3618,
        "thai:rorua": 3619,
        "thai:ru": 3620,
        "thai:loling": 3621,
        "thai:lu": 3622,
        "thai:wowaen": 3623,
        "thai:sosala": 3624,
        "thai:sorusi": 3625,
        "thai:sosua": 3626,
        "thai:hohip": 3627,
        "thai:lochula": 3628,
        "thai:oang": 3629,
        "thai:honokhuk": 3630,
        "thai:paiyannoi": 3631,
        "thai:saraa": 3632,
        "thai:maihan-akat": 3633,
        "thai:saraaa": 3634,
        "thai:saraam": 3635,
        "thai:sarai": 3636,
        "thai:saraii": 3637,
        "thai:saraue": 3638,
        "thai:sarauee": 3639,
        "thai:sarau": 3640,
        "thai:sarauu": 3641,
        "thai:phinthu": 3642,
        "thai:baht": 3647,
        "thai:sarae": 3648,
        "thai:saraae": 3649,
        "thai:sarao": 3650,
        "thai:saraaimaimuan": 3651,
        "thai:saraaimaimalai": 3652,
        "thai:lakkhangyao": 3653,
        "thai:maiyamok": 3654,
        "thai:maitaikhu": 3655,
        "thai:maiek": 3656,
        "thai:maitho": 3657,
        "thai:maitri": 3658,
        "thai:maichattawa": 3659,
        "thai:thanthakhat": 3660,
        "thai:nikhahit": 3661,
        "thai:yamakkan": 3662,
        "thai:fongman": 3663,
        "thai:zero": 3664,
        "thai:one": 3665,
        "thai:two": 3666,
        "thai:three": 3667,
        "thai:four": 3668,
        "thai:five": 3669,
        "thai:six": 3670,
        "thai:seven": 3671,
        "thai:eight": 3672,
        "thai:nine": 3673,
        "thai:angkhankhu": 3674,
        "thai:khomut": 3675,
        "tibt:omsyllable": 3840,
        "tibt:gteryigmgotruncatedamark": 3841,
        "tibt:gteryigmgoumrnambcadmamark": 3842,
        "tibt:gteryigmgoumgtertshegmamark": 3843,
        "tibt:yigmgomdunmainitialmark": 3844,
        "tibt:yigmgosgabmaclosingmark": 3845,
        "tibt:caretyigmgophurshadmamark": 3846,
        "tibt:yigmgotshegshadmamark": 3847,
        "tibt:sbrulshadmark": 3848,
        "tibt:bskuryigmgomark": 3849,
        "tibt:bkashogyigmgomark": 3850,
        "tibt:intersyllabictshegmark": 3851,
        "tibt:delimitertshegbstarmark": 3852,
        "tibt:shadmark": 3853,
        "tibt:nyisshadmark": 3854,
        "tibt:tshegshadmark": 3855,
        "tibt:nyistshegshadmark": 3856,
        "tibt:rinchenspungsshadmark": 3857,
        "tibt:rgyagramshadmark": 3858,
        "tibt:caretdzudrtagsmelongcanmark": 3859,
        "tibt:gtertshegmark": 3860,
        "tibt:chadrtagslogotypesign": 3861,
        "tibt:lhagrtagslogotypesign": 3862,
        "tibt:astrologicalsgragcancharrtagssign": 3863,
        "tibt:astrologicalkhyudpasign": 3864,
        "tibt:astrologicalsdongtshugssign": 3865,
        "tibt:rdeldkargcigsign": 3866,
        "tibt:rdeldkargnyissign": 3867,
        "tibt:rdeldkargsumsign": 3868,
        "tibt:rdelnaggcigsign": 3869,
        "tibt:rdelnaggnyissign": 3870,
        "tibt:rdeldkarrdelnagsign": 3871,
        "tibt:zero": 3872,
        "tibt:one": 3873,
        "tibt:two": 3874,
        "tibt:three": 3875,
        "tibt:four": 3876,
        "tibt:five": 3877,
        "tibt:six": 3878,
        "tibt:seven": 3879,
        "tibt:eight": 3880,
        "tibt:nine": 3881,
        "tibt:halfone": 3882,
        "tibt:halftwo": 3883,
        "tibt:halfthree": 3884,
        "tibt:halffour": 3885,
        "tibt:halffive": 3886,
        "tibt:halfsix": 3887,
        "tibt:halfseven": 3888,
        "tibt:halfeight": 3889,
        "tibt:halfnine": 3890,
        "tibt:halfzero": 3891,
        "tibt:bsdusrtagsmark": 3892,
        "tibt:ngasbzungnyizlamark": 3893,
        "tibt:caretdzudrtagsbzhimigcanmark": 3894,
        "tibt:ngasbzungsgorrtagsmark": 3895,
        "tibt:chemgomark": 3896,
        "tibt:tsaphrumark": 3897,
        "tibt:gugrtagsgyonmark": 3898,
        "tibt:gugrtagsgyasmark": 3899,
        "tibt:angkhanggyonmark": 3900,
        "tibt:angkhanggyasmark": 3901,
        "tibt:yartshessign": 3902,
        "tibt:martshessign": 3903,
        "tibt:ka": 3904,
        "tibt:kha": 3905,
        "tibt:ga": 3906,
        "tibt:gha": 3907,
        "tibt:nga": 3908,
        "tibt:ca": 3909,
        "tibt:cha": 3910,
        "tibt:ja": 3911,
        "tibt:nya": 3913,
        "tibt:tta": 3914,
        "tibt:ttha": 3915,
        "tibt:dda": 3916,
        "tibt:ddha": 3917,
        "tibt:nna": 3918,
        "tibt:ta": 3919,
        "tibt:tha": 3920,
        "tibt:da": 3921,
        "tibt:dha": 3922,
        "tibt:na": 3923,
        "tibt:pa": 3924,
        "tibt:pha": 3925,
        "tibt:ba": 3926,
        "tibt:bha": 3927,
        "tibt:ma": 3928,
        "tibt:tsa": 3929,
        "tibt:tsha": 3930,
        "tibt:dza": 3931,
        "tibt:dzha": 3932,
        "tibt:wa": 3933,
        "tibt:zha": 3934,
        "tibt:za": 3935,
        "tibt:AA": 3936,
        "tibt:ya": 3937,
        "tibt:ra": 3938,
        "tibt:la": 3939,
        "tibt:sha": 3940,
        "tibt:ssa": 3941,
        "tibt:sa": 3942,
        "tibt:ha": 3943,
        "tibt:a": 3944,
        "tibt:kssa": 3945,
        "tibt:rafixed": 3946,
        "tibt:kka": 3947,
        "tibt:rra": 3948,
        "tibt:aavowelsign": 3953,
        "tibt:ivowelsign": 3954,
        "tibt:iivowelsign": 3955,
        "tibt:uvowelsign": 3956,
        "tibt:uuvowelsign": 3957,
        "tibt:rvocalicvowelsign": 3958,
        "tibt:rrvocalicvowelsign": 3959,
        "tibt:lvocalicvowelsign": 3960,
        "tibt:llvocalicvowelsign": 3961,
        "tibt:evowelsign": 3962,
        "tibt:eevowelsign": 3963,
        "tibt:ovowelsign": 3964,
        "tibt:oovowelsign": 3965,
        "tibt:rjessungarosign": 3966,
        "tibt:rnambcadsign": 3967,
        "tibt:reversedivowelsign": 3968,
        "tibt:reversediivowelsign": 3969,
        "tibt:nyizlanaadasign": 3970,
        "tibt:snaldansign": 3971,
        "tibt:halantamark": 3972,
        "tibt:palutamark": 3973,
        "tibt:lcirtagssign": 3974,
        "tibt:yangrtagssign": 3975,
        "tibt:lcetsacansign": 3976,
        "tibt:mchucansign": 3977,
        "tibt:grucanrgyingssign": 3978,
        "tibt:grumedrgyingssign": 3979,
        "tibt:invertedmchucansign": 3980,
        "tibt:lcetsacansubjoinedsign": 3981,
        "tibt:mchucansubjoinedsign": 3982,
        "tibt:invertedmchucansubjoinedsign": 3983,
        "tibt:kasubjoined": 3984,
        "tibt:khasubjoined": 3985,
        "tibt:gasubjoined": 3986,
        "tibt:ghasubjoined": 3987,
        "tibt:ngasubjoined": 3988,
        "tibt:casubjoined": 3989,
        "tibt:chasubjoined": 3990,
        "tibt:jasubjoined": 3991,
        "tibt:nyasubjoined": 3993,
        "tibt:ttasubjoined": 3994,
        "tibt:tthasubjoined": 3995,
        "tibt:ddasubjoined": 3996,
        "tibt:ddhasubjoined": 3997,
        "tibt:nnasubjoined": 3998,
        "tibt:tasubjoined": 3999,
        "tibt:thasubjoined": 4000,
        "tibt:dasubjoined": 4001,
        "tibt:dhasubjoined": 4002,
        "tibt:nasubjoined": 4003,
        "tibt:pasubjoined": 4004,
        "tibt:phasubjoined": 4005,
        "tibt:basubjoined": 4006,
        "tibt:bhasubjoined": 4007,
        "tibt:masubjoined": 4008,
        "tibt:tsasubjoined": 4009,
        "tibt:tshasubjoined": 4010,
        "tibt:dzasubjoined": 4011,
        "tibt:dzhasubjoined": 4012,
        "tibt:wasubjoined": 4013,
        "tibt:zhasubjoined": 4014,
        "tibt:zasubjoined": 4015,
        "tibt:subjoinedAA": 4016,
        "tibt:yasubjoined": 4017,
        "tibt:rasubjoined": 4018,
        "tibt:lasubjoined": 4019,
        "tibt:shasubjoined": 4020,
        "tibt:ssasubjoined": 4021,
        "tibt:sasubjoined": 4022,
        "tibt:hasubjoined": 4023,
        "tibt:asubjoined": 4024,
        "tibt:kssasubjoined": 4025,
        "tibt:wasubjoinedfixed": 4026,
        "tibt:yasubjoinedfixed": 4027,
        "tibt:rasubjoinedfixed": 4028,
        "tibt:kurukha": 4030,
        "tibt:kurukhabzhimigcan": 4031,
        "tibt:heavybeatcantillationsign": 4032,
        "tibt:lightbeatcantillationsign": 4033,
        "tibt:cangteucantillationsign": 4034,
        "tibt:sbubchalcantillationsign": 4035,
        "tibt:drilbusymbol": 4036,
        "tibt:rdorjesymbol": 4037,
        "tibt:padmagdansymbol": 4038,
        "tibt:rdorjergyagramsymbol": 4039,
        "tibt:phurpasymbol": 4040,
        "tibt:norbusymbol": 4041,
        "tibt:norbunyiskhyilsymbol": 4042,
        "tibt:norbugsumkhyilsymbol": 4043,
        "tibt:norbubzhikhyilsymbol": 4044,
        "tibt:rdelnagrdeldkarsign": 4046,
        "tibt:rdelnaggsumsign": 4047,
        "tibt:bskashoggimgorgyanmark": 4048,
        "tibt:mnyamyiggimgorgyanmark": 4049,
        "tibt:nyistshegmark": 4050,
        "tibt:brdarnyingyigmgomdunmainitialmark": 4051,
        "tibt:brdarnyingyigmgosgabmaclosingmark": 4052,
        "tibt:svastiright": 4053,
        "tibt:svastileft": 4054,
        "tibt:svastirightdot": 4055,
        "tibt:svastileftdot": 4056,
        "tibt:leadingmchanrtagsmark": 4057,
        "tibt:trailingmchanrtagsmark": 4058,
        "AnGeok": 4256,
        "BanGeok": 4257,
        "GanGeok": 4258,
        "DonGeok": 4259,
        "EnGeok": 4260,
        "VinGeok": 4261,
        "ZenGeok": 4262,
        "TanGeok": 4263,
        "InGeok": 4264,
        "KanGeok": 4265,
        "LasGeok": 4266,
        "ManGeok": 4267,
        "NarGeok": 4268,
        "OnGeok": 4269,
        "ParGeok": 4270,
        "ZharGeok": 4271,
        "RaeGeok": 4272,
        "SanGeok": 4273,
        "TarGeok": 4274,
        "UnGeok": 4275,
        "PharGeok": 4276,
        "KharGeok": 4277,
        "GhanGeok": 4278,
        "QarGeok": 4279,
        "ShinGeok": 4280,
        "ChinGeok": 4281,
        "CanGeok": 4282,
        "JilGeok": 4283,
        "CilGeok": 4284,
        "CharGeok": 4285,
        "XanGeok": 4286,
        "JhanGeok": 4287,
        "HaeGeok": 4288,
        "HeGeok": 4289,
        "HieGeok": 4290,
        "WeGeok": 4291,
        "HarGeok": 4292,
        "HoeGeok": 4293,
        "YnGeok": 4295,
        "AenGeok": 4301,
        "anGeor": 4304,
        "banGeor": 4305,
        "ganGeor": 4306,
        "donGeor": 4307,
        "enGeor": 4308,
        "vinGeor": 4309,
        "zenGeor": 4310,
        "tanGeor": 4311,
        "inGeor": 4312,
        "kanGeor": 4313,
        "lasGeor": 4314,
        "manGeor": 4315,
        "narGeor": 4316,
        "onGeor": 4317,
        "parGeor": 4318,
        "zharGeor": 4319,
        "raeGeor": 4320,
        "sanGeor": 4321,
        "tarGeor": 4322,
        "unGeor": 4323,
        "pharGeor": 4324,
        "kharGeor": 4325,
        "ghanGeor": 4326,
        "qarGeor": 4327,
        "shinGeor": 4328,
        "chinGeor": 4329,
        "canGeor": 4330,
        "jilGeor": 4331,
        "cilGeor": 4332,
        "charGeor": 4333,
        "xanGeor": 4334,
        "jhanGeor": 4335,
        "haeGeor": 4336,
        "heGeor": 4337,
        "hieGeor": 4338,
        "weGeor": 4339,
        "harGeor": 4340,
        "hoeGeor": 4341,
        "fiGeor": 4342,
        "ynGeor": 4343,
        "elifiGeor": 4344,
        "turnedganGeor": 4345,
        "ainGeor": 4346,
        "geor:paragraphseparator": 4347,
        "narmodGeor": 4348,
        "aenGeor": 4349,
        "hardsignGeor": 4350,
        "labialsignGeor": 4351,
        "ko:kiyeokchoseong": 4352,
        "ko:ssangkiyeokchoseong": 4353,
        "ko:nieunchoseong": 4354,
        "ko:tikeutchoseong": 4355,
        "ko:ssangtikeutchoseong": 4356,
        "ko:rieulchoseong": 4357,
        "ko:mieumchoseong": 4358,
        "ko:pieupchoseong": 4359,
        "ko:ssangpieupchoseong": 4360,
        "ko:sioschoseong": 4361,
        "ko:ssangsioschoseong": 4362,
        "ko:ieungchoseong": 4363,
        "ko:cieucchoseong": 4364,
        "ko:ssangcieucchoseong": 4365,
        "ko:chieuchchoseong": 4366,
        "ko:khieukhchoseong": 4367,
        "ko:thieuthchoseong": 4368,
        "ko:phieuphchoseong": 4369,
        "ko:hieuhchoseong": 4370,
        "ko:nieunkiyeokchoseong": 4371,
        "ko:ssangnieunchoseong": 4372,
        "ko:nieuntikeutchoseong": 4373,
        "ko:nieunpieupchoseong": 4374,
        "ko:tikeutkiyeokchoseong": 4375,
        "ko:rieulnieunchoseong": 4376,
        "ko:ssangrieulchoseong": 4377,
        "ko:rieulhieuhchoseong": 4378,
        "ko:kapyeounrieulchoseong": 4379,
        "ko:mieumpieupchoseong": 4380,
        "ko:kapyeounmieumchoseong": 4381,
        "ko:pieupkiyeokchoseong": 4382,
        "ko:pieupnieunchoseong": 4383,
        "ko:pieuptikeutchoseong": 4384,
        "ko:pieupsioschoseong": 4385,
        "ko:pieupsioskiyeokchoseong": 4386,
        "ko:pieupsiostikeutchoseong": 4387,
        "ko:pieupsiospieupchoseong": 4388,
        "ko:pieupssangsioschoseong": 4389,
        "ko:pieupsioscieucchoseong": 4390,
        "ko:pieupcieucchoseong": 4391,
        "ko:pieupchieuchchoseong": 4392,
        "ko:pieupthieuthchoseong": 4393,
        "ko:pieupphieuphchoseong": 4394,
        "ko:kapyeounpieupchoseong": 4395,
        "ko:kapyeounssangpieupchoseong": 4396,
        "ko:sioskiyeokchoseong": 4397,
        "ko:siosnieunchoseong": 4398,
        "ko:siostikeutchoseong": 4399,
        "ko:siosrieulchoseong": 4400,
        "ko:siosmieumchoseong": 4401,
        "ko:siospieupchoseong": 4402,
        "ko:siospieupkiyeokchoseong": 4403,
        "ko:siosssangsioschoseong": 4404,
        "ko:siosieungchoseong": 4405,
        "ko:sioscieucchoseong": 4406,
        "ko:sioschieuchchoseong": 4407,
        "ko:sioskhieukhchoseong": 4408,
        "ko:siosthieuthchoseong": 4409,
        "ko:siosphieuphchoseong": 4410,
        "ko:sioshieuhchoseong": 4411,
        "ko:chitueumsioschoseong": 4412,
        "ko:chitueumssangsioschoseong": 4413,
        "ko:ceongchieumsioschoseong": 4414,
        "ko:ceongchieumssangsioschoseong": 4415,
        "ko:pansioschoseong": 4416,
        "ko:ieungkiyeokchoseong": 4417,
        "ko:ieungtikeutchoseong": 4418,
        "ko:ieungmieumchoseong": 4419,
        "ko:ieungpieupchoseong": 4420,
        "ko:ieungsioschoseong": 4421,
        "ko:ieungpansioschoseong": 4422,
        "ko:ssangieungchoseong": 4423,
        "ko:ieungcieucchoseong": 4424,
        "ko:ieungchieuchchoseong": 4425,
        "ko:ieungthieuthchoseong": 4426,
        "ko:ieungphieuphchoseong": 4427,
        "ko:yesieungchoseong": 4428,
        "ko:cieucieungchoseong": 4429,
        "ko:chitueumcieucchoseong": 4430,
        "ko:chitueumssangcieucchoseong": 4431,
        "ko:ceongchieumcieucchoseong": 4432,
        "ko:ceongchieumssangcieucchoseong": 4433,
        "ko:chieuchkhieukhchoseong": 4434,
        "ko:chieuchhieuhchoseong": 4435,
        "ko:chitueumchieuchchoseong": 4436,
        "ko:ceongchieumchieuchchoseong": 4437,
        "ko:phieuphpieupchoseong": 4438,
        "ko:kapyeounphieuphchoseong": 4439,
        "ko:ssanghieuhchoseong": 4440,
        "ko:yeorinhieuhchoseong": 4441,
        "ko:kiyeoktikeutchoseong": 4442,
        "ko:nieunsioschoseong": 4443,
        "ko:nieuncieucchoseong": 4444,
        "ko:nieunhieuhchoseong": 4445,
        "ko:tikeutrieulchoseong": 4446,
        "ko:fillerchoseong": 4447,
        "ko:fillerjungseong": 4448,
        "ko:ajungseong": 4449,
        "ko:aejungseong": 4450,
        "ko:yajungseong": 4451,
        "ko:yaejungseong": 4452,
        "ko:eojungseong": 4453,
        "ko:ejungseong": 4454,
        "ko:yeojungseong": 4455,
        "ko:yejungseong": 4456,
        "ko:ojungseong": 4457,
        "ko:wajungseong": 4458,
        "ko:waejungseong": 4459,
        "ko:oejungseong": 4460,
        "ko:yojungseong": 4461,
        "ko:ujungseong": 4462,
        "ko:weojungseong": 4463,
        "ko:wejungseong": 4464,
        "ko:wijungseong": 4465,
        "ko:yujungseong": 4466,
        "ko:eujungseong": 4467,
        "ko:yijungseong": 4468,
        "ko:ijungseong": 4469,
        "ko:aojungseong": 4470,
        "ko:aujungseong": 4471,
        "ko:yaojungseong": 4472,
        "ko:yayojungseong": 4473,
        "ko:eoojungseong": 4474,
        "ko:eoujungseong": 4475,
        "ko:eo_eujungseong": 4476,
        "ko:yeoojungseong": 4477,
        "ko:yeoujungseong": 4478,
        "ko:o_eojungseong": 4479,
        "ko:o_ejungseong": 4480,
        "ko:oyejungseong": 4481,
        "ko:oojungseong": 4482,
        "ko:oujungseong": 4483,
        "ko:yoyajungseong": 4484,
        "ko:yoyaejungseong": 4485,
        "ko:yoyeojungseong": 4486,
        "ko:yoojungseong": 4487,
        "ko:yoijungseong": 4488,
        "ko:uajungseong": 4489,
        "ko:uaejungseong": 4490,
        "ko:ueo_eujungseong": 4491,
        "ko:uyejungseong": 4492,
        "ko:uujungseong": 4493,
        "ko:yuajungseong": 4494,
        "ko:yueojungseong": 4495,
        "ko:yuejungseong": 4496,
        "ko:yuyeojungseong": 4497,
        "ko:yuyejungseong": 4498,
        "ko:yuujungseong": 4499,
        "ko:yuijungseong": 4500,
        "ko:euujungseong": 4501,
        "ko:eueujungseong": 4502,
        "ko:yiujungseong": 4503,
        "ko:iajungseong": 4504,
        "ko:iyajungseong": 4505,
        "ko:iojungseong": 4506,
        "ko:iujungseong": 4507,
        "ko:ieujungseong": 4508,
        "ko:iaraeajungseong": 4509,
        "ko:araeajungseong": 4510,
        "ko:araeaeojungseong": 4511,
        "ko:araeaujungseong": 4512,
        "ko:araeaijungseong": 4513,
        "ko:ssangaraeajungseong": 4514,
        "ko:aeujungseong": 4515,
        "ko:yaujungseong": 4516,
        "ko:yeoyajungseong": 4517,
        "ko:oyajungseong": 4518,
        "ko:oyaejungseong": 4519,
        "ko:kiyeokjongseong": 4520,
        "ko:ssangkiyeokjongseong": 4521,
        "ko:kiyeoksiosjongseong": 4522,
        "ko:nieunjongseong": 4523,
        "ko:nieuncieucjongseong": 4524,
        "ko:nieunhieuhjongseong": 4525,
        "ko:tikeutjongseong": 4526,
        "ko:rieuljongseong": 4527,
        "ko:rieulkiyeokjongseong": 4528,
        "ko:rieulmieumjongseong": 4529,
        "ko:rieulpieupjongseong": 4530,
        "ko:rieulsiosjongseong": 4531,
        "ko:rieulthieuthjongseong": 4532,
        "ko:rieulphieuphjongseong": 4533,
        "ko:rieulhieuhjongseong": 4534,
        "ko:mieumjongseong": 4535,
        "ko:pieupjongseong": 4536,
        "ko:pieupsiosjongseong": 4537,
        "ko:siosjongseong": 4538,
        "ko:ssangsiosjongseong": 4539,
        "ko:ieungjongseong": 4540,
        "ko:cieucjongseong": 4541,
        "ko:chieuchjongseong": 4542,
        "ko:khieukhjongseong": 4543,
        "ko:thieuthjongseong": 4544,
        "ko:phieuphjongseong": 4545,
        "ko:hieuhjongseong": 4546,
        "ko:kiyeokrieuljongseong": 4547,
        "ko:kiyeoksioskiyeokjongseong": 4548,
        "ko:nieunkiyeokjongseong": 4549,
        "ko:nieuntikeutjongseong": 4550,
        "ko:nieunsiosjongseong": 4551,
        "ko:nieunpansiosjongseong": 4552,
        "ko:nieunthieuthjongseong": 4553,
        "ko:tikeutkiyeokjongseong": 4554,
        "ko:tikeutrieuljongseong": 4555,
        "ko:rieulkiyeoksiosjongseong": 4556,
        "ko:rieulnieunjongseong": 4557,
        "ko:rieultikeutjongseong": 4558,
        "ko:rieultikeuthieuhjongseong": 4559,
        "ko:ssangrieuljongseong": 4560,
        "ko:rieulmieumkiyeokjongseong": 4561,
        "ko:rieulmieumsiosjongseong": 4562,
        "ko:rieulpieupsiosjongseong": 4563,
        "ko:rieulpieuphieuhjongseong": 4564,
        "ko:rieulkapyeounpieupjongseong": 4565,
        "ko:rieulssangsiosjongseong": 4566,
        "ko:rieulpansiosjongseong": 4567,
        "ko:rieulkhieukhjongseong": 4568,
        "ko:rieulyeorinhieuhjongseong": 4569,
        "ko:mieumkiyeokjongseong": 4570,
        "ko:mieumrieuljongseong": 4571,
        "ko:mieumpieupjongseong": 4572,
        "ko:mieumsiosjongseong": 4573,
        "ko:mieumssangsiosjongseong": 4574,
        "ko:mieumpansiosjongseong": 4575,
        "ko:mieumchieuchjongseong": 4576,
        "ko:mieumhieuhjongseong": 4577,
        "ko:kapyeounmieumjongseong": 4578,
        "ko:pieuprieuljongseong": 4579,
        "ko:pieupphieuphjongseong": 4580,
        "ko:pieuphieuhjongseong": 4581,
        "ko:kapyeounpieupjongseong": 4582,
        "ko:sioskiyeokjongseong": 4583,
        "ko:siostikeutjongseong": 4584,
        "ko:siosrieuljongseong": 4585,
        "ko:siospieupjongseong": 4586,
        "ko:pansiosjongseong": 4587,
        "ko:ieungkiyeokjongseong": 4588,
        "ko:ieungssangkiyeokjongseong": 4589,
        "ko:ssangieungjongseong": 4590,
        "ko:ieungkhieukhjongseong": 4591,
        "ko:yesieungjongseong": 4592,
        "ko:yesieungsiosjongseong": 4593,
        "ko:yesieungpansiosjongseong": 4594,
        "ko:phieuphpieupjongseong": 4595,
        "ko:kapyeounphieuphjongseong": 4596,
        "ko:hieuhnieunjongseong": 4597,
        "ko:hieuhrieuljongseong": 4598,
        "ko:hieuhmieumjongseong": 4599,
        "ko:hieuhpieupjongseong": 4600,
        "ko:yeorinhieuhjongseong": 4601,
        "ko:kiyeoknieunjongseong": 4602,
        "ko:kiyeokpieupjongseong": 4603,
        "ko:kiyeokchieuchjongseong": 4604,
        "ko:kiyeokkhieukhjongseong": 4605,
        "ko:kiyeokhieuhjongseong": 4606,
        "ko:ssangnieunjongseong": 4607,
        "ethi:ha": 4608,
        "ethi:hu": 4609,
        "ethi:hi": 4610,
        "ethi:haa": 4611,
        "ethi:hee": 4612,
        "ethi:he": 4613,
        "ethi:ho": 4614,
        "ethi:hoa": 4615,
        "ethi:la": 4616,
        "ethi:lu": 4617,
        "ethi:li": 4618,
        "ethi:laa": 4619,
        "ethi:lee": 4620,
        "ethi:le": 4621,
        "ethi:lo": 4622,
        "ethi:lwa": 4623,
        "ethi:hha": 4624,
        "ethi:hhu": 4625,
        "ethi:hhi": 4626,
        "ethi:hhaa": 4627,
        "ethi:hhee": 4628,
        "ethi:hhe": 4629,
        "ethi:hho": 4630,
        "ethi:hhwa": 4631,
        "ethi:ma": 4632,
        "ethi:mu": 4633,
        "ethi:mi": 4634,
        "ethi:maa": 4635,
        "ethi:mee": 4636,
        "ethi:me": 4637,
        "ethi:mo": 4638,
        "ethi:mwa": 4639,
        "ethi:sza": 4640,
        "ethi:szu": 4641,
        "ethi:szi": 4642,
        "ethi:szaa": 4643,
        "ethi:szee": 4644,
        "ethi:sze": 4645,
        "ethi:szo": 4646,
        "ethi:szwa": 4647,
        "ethi:ra": 4648,
        "ethi:ru": 4649,
        "ethi:ri": 4650,
        "ethi:raa": 4651,
        "ethi:ree": 4652,
        "ethi:re": 4653,
        "ethi:ro": 4654,
        "ethi:rwa": 4655,
        "ethi:sa": 4656,
        "ethi:su": 4657,
        "ethi:si": 4658,
        "ethi:saa": 4659,
        "ethi:see": 4660,
        "ethi:se": 4661,
        "ethi:so": 4662,
        "ethi:swa": 4663,
        "ethi:sha": 4664,
        "ethi:shu": 4665,
        "ethi:shi": 4666,
        "ethi:shaa": 4667,
        "ethi:shee": 4668,
        "ethi:she": 4669,
        "ethi:sho": 4670,
        "ethi:shwa": 4671,
        "ethi:qa": 4672,
        "ethi:qu": 4673,
        "ethi:qi": 4674,
        "ethi:qaa": 4675,
        "ethi:qee": 4676,
        "ethi:qe": 4677,
        "ethi:qo": 4678,
        "ethi:qoa": 4679,
        "ethi:qwa": 4680,
        "ethi:qwi": 4682,
        "ethi:qwaa": 4683,
        "ethi:qwee": 4684,
        "ethi:qwe": 4685,
        "ethi:qha": 4688,
        "ethi:qhu": 4689,
        "ethi:qhi": 4690,
        "ethi:qhaa": 4691,
        "ethi:qhee": 4692,
        "ethi:qhe": 4693,
        "ethi:qho": 4694,
        "ethi:qhwa": 4696,
        "ethi:qhwi": 4698,
        "ethi:qhwaa": 4699,
        "ethi:qhwee": 4700,
        "ethi:qhwe": 4701,
        "ethi:ba": 4704,
        "ethi:bu": 4705,
        "ethi:bi": 4706,
        "ethi:baa": 4707,
        "ethi:bee": 4708,
        "ethi:be": 4709,
        "ethi:bo": 4710,
        "ethi:bwa": 4711,
        "ethi:va": 4712,
        "ethi:vu": 4713,
        "ethi:vi": 4714,
        "ethi:vaa": 4715,
        "ethi:vee": 4716,
        "ethi:ve": 4717,
        "ethi:vo": 4718,
        "ethi:vwa": 4719,
        "ethi:ta": 4720,
        "ethi:tu": 4721,
        "ethi:ti": 4722,
        "ethi:taa": 4723,
        "ethi:tee": 4724,
        "ethi:te": 4725,
        "ethi:to": 4726,
        "ethi:twa": 4727,
        "ethi:ca": 4728,
        "ethi:cu": 4729,
        "ethi:ci": 4730,
        "ethi:caa": 4731,
        "ethi:cee": 4732,
        "ethi:ce": 4733,
        "ethi:co": 4734,
        "ethi:cwa": 4735,
        "ethi:xa": 4736,
        "ethi:xu": 4737,
        "ethi:xi": 4738,
        "ethi:xaa": 4739,
        "ethi:xee": 4740,
        "ethi:xe": 4741,
        "ethi:xo": 4742,
        "ethi:xoa": 4743,
        "ethi:xwa": 4744,
        "ethi:xwi": 4746,
        "ethi:xwaa": 4747,
        "ethi:xwee": 4748,
        "ethi:xwe": 4749,
        "ethi:na": 4752,
        "ethi:nu": 4753,
        "ethi:ni": 4754,
        "ethi:naa": 4755,
        "ethi:nee": 4756,
        "ethi:ne": 4757,
        "ethi:no": 4758,
        "ethi:nwa": 4759,
        "ethi:nya": 4760,
        "ethi:nyu": 4761,
        "ethi:nyi": 4762,
        "ethi:nyaa": 4763,
        "ethi:nyee": 4764,
        "ethi:nye": 4765,
        "ethi:nyo": 4766,
        "ethi:nywa": 4767,
        "ethi:aglottal": 4768,
        "ethi:uglottal": 4769,
        "ethi:iglottal": 4770,
        "ethi:aaglottal": 4771,
        "ethi:eeglottal": 4772,
        "ethi:eglottal": 4773,
        "ethi:oglottal": 4774,
        "ethi:waglottal": 4775,
        "ethi:ka": 4776,
        "ethi:ku": 4777,
        "ethi:ki": 4778,
        "ethi:kaa": 4779,
        "ethi:kee": 4780,
        "ethi:ke": 4781,
        "ethi:ko": 4782,
        "ethi:koa": 4783,
        "ethi:kwa": 4784,
        "ethi:kwi": 4786,
        "ethi:kwaa": 4787,
        "ethi:kwee": 4788,
        "ethi:kwe": 4789,
        "ethi:kxa": 4792,
        "ethi:kxu": 4793,
        "ethi:kxi": 4794,
        "ethi:kxaa": 4795,
        "ethi:kxee": 4796,
        "ethi:kxe": 4797,
        "ethi:kxo": 4798,
        "ethi:kxwa": 4800,
        "ethi:kxwi": 4802,
        "ethi:kxwaa": 4803,
        "ethi:kxwee": 4804,
        "ethi:kxwe": 4805,
        "ethi:wa": 4808,
        "ethi:wu": 4809,
        "ethi:wi": 4810,
        "ethi:waa": 4811,
        "ethi:wee": 4812,
        "ethi:we": 4813,
        "ethi:wo": 4814,
        "ethi:woa": 4815,
        "ethi:pharyngeala": 4816,
        "ethi:pharyngealu": 4817,
        "ethi:pharyngeali": 4818,
        "ethi:pharyngealaa": 4819,
        "ethi:pharyngealee": 4820,
        "ethi:pharyngeale": 4821,
        "ethi:pharyngealo": 4822,
        "ethi:za": 4824,
        "ethi:zu": 4825,
        "ethi:zi": 4826,
        "ethi:zaa": 4827,
        "ethi:zee": 4828,
        "ethi:ze": 4829,
        "ethi:zo": 4830,
        "ethi:zwa": 4831,
        "ethi:zha": 4832,
        "ethi:zhu": 4833,
        "ethi:zhi": 4834,
        "ethi:zhaa": 4835,
        "ethi:zhee": 4836,
        "ethi:zhe": 4837,
        "ethi:zho": 4838,
        "ethi:zhwa": 4839,
        "ethi:ya": 4840,
        "ethi:yu": 4841,
        "ethi:yi": 4842,
        "ethi:yaa": 4843,
        "ethi:yee": 4844,
        "ethi:ye": 4845,
        "ethi:yo": 4846,
        "ethi:yoa": 4847,
        "ethi:da": 4848,
        "ethi:du": 4849,
        "ethi:di": 4850,
        "ethi:daa": 4851,
        "ethi:dee": 4852,
        "ethi:de": 4853,
        "ethi:do": 4854,
        "ethi:dwa": 4855,
        "ethi:dda": 4856,
        "ethi:ddu": 4857,
        "ethi:ddi": 4858,
        "ethi:ddaa": 4859,
        "ethi:ddee": 4860,
        "ethi:dde": 4861,
        "ethi:ddo": 4862,
        "ethi:ddwa": 4863,
        "ethi:ja": 4864,
        "ethi:ju": 4865,
        "ethi:ji": 4866,
        "ethi:jaa": 4867,
        "ethi:jee": 4868,
        "ethi:je": 4869,
        "ethi:jo": 4870,
        "ethi:jwa": 4871,
        "ethi:ga": 4872,
        "ethi:gu": 4873,
        "ethi:gi": 4874,
        "ethi:gaa": 4875,
        "ethi:gee": 4876,
        "ethi:ge": 4877,
        "ethi:go": 4878,
        "ethi:goa": 4879,
        "ethi:gwa": 4880,
        "ethi:gwi": 4882,
        "ethi:gwaa": 4883,
        "ethi:gwee": 4884,
        "ethi:gwe": 4885,
        "ethi:gga": 4888,
        "ethi:ggu": 4889,
        "ethi:ggi": 4890,
        "ethi:ggaa": 4891,
        "ethi:ggee": 4892,
        "ethi:gge": 4893,
        "ethi:ggo": 4894,
        "ethi:ggwaa": 4895,
        "ethi:tha": 4896,
        "ethi:thu": 4897,
        "ethi:thi": 4898,
        "ethi:thaa": 4899,
        "ethi:thee": 4900,
        "ethi:the": 4901,
        "ethi:tho": 4902,
        "ethi:thwa": 4903,
        "ethi:cha": 4904,
        "ethi:chu": 4905,
        "ethi:chi": 4906,
        "ethi:chaa": 4907,
        "ethi:chee": 4908,
        "ethi:che": 4909,
        "ethi:cho": 4910,
        "ethi:chwa": 4911,
        "ethi:pha": 4912,
        "ethi:phu": 4913,
        "ethi:phi": 4914,
        "ethi:phaa": 4915,
        "ethi:phee": 4916,
        "ethi:phe": 4917,
        "ethi:pho": 4918,
        "ethi:phwa": 4919,
        "ethi:tsa": 4920,
        "ethi:tsu": 4921,
        "ethi:tsi": 4922,
        "ethi:tsaa": 4923,
        "ethi:tsee": 4924,
        "ethi:tse": 4925,
        "ethi:tso": 4926,
        "ethi:tswa": 4927,
        "ethi:tza": 4928,
        "ethi:tzu": 4929,
        "ethi:tzi": 4930,
        "ethi:tzaa": 4931,
        "ethi:tzee": 4932,
        "ethi:tze": 4933,
        "ethi:tzo": 4934,
        "ethi:tzoa": 4935,
        "ethi:fa": 4936,
        "ethi:fu": 4937,
        "ethi:fi": 4938,
        "ethi:faa": 4939,
        "ethi:fee": 4940,
        "ethi:fe": 4941,
        "ethi:fo": 4942,
        "ethi:fwa": 4943,
        "ethi:pa": 4944,
        "ethi:pu": 4945,
        "ethi:pi": 4946,
        "ethi:paa": 4947,
        "ethi:pee": 4948,
        "ethi:pe": 4949,
        "ethi:po": 4950,
        "ethi:pwa": 4951,
        "ethi:rya": 4952,
        "ethi:mya": 4953,
        "ethi:fya": 4954,
        "ethi:geminationandvowellengthmarkcmb": 4957,
        "ethi:vowellengthmarkcmb": 4958,
        "ethi:geminationmarkcmb": 4959,
        "ethi:sectionmark": 4960,
        "ethi:wordspace": 4961,
        "ethi:fullstop": 4962,
        "ethi:comma": 4963,
        "ethi:semicolon": 4964,
        "ethi:colon": 4965,
        "ethi:prefacecolon": 4966,
        "ethi:questionmark": 4967,
        "ethi:paragraphseparator": 4968,
        "ethi:one": 4969,
        "ethi:two": 4970,
        "ethi:three": 4971,
        "ethi:four": 4972,
        "ethi:five": 4973,
        "ethi:six": 4974,
        "ethi:seven": 4975,
        "ethi:eight": 4976,
        "ethi:nine": 4977,
        "ethi:ten": 4978,
        "ethi:twenty": 4979,
        "ethi:thirty": 4980,
        "ethi:forty": 4981,
        "ethi:fifty": 4982,
        "ethi:sixty": 4983,
        "ethi:seventy": 4984,
        "ethi:eighty": 4985,
        "ethi:ninety": 4986,
        "ethi:hundred": 4987,
        "ethi:tenthousand": 4988,
        "cher:a": 5024,
        "cher:e": 5025,
        "cher:i": 5026,
        "cher:o": 5027,
        "cher:u": 5028,
        "cher:v": 5029,
        "cher:ga": 5030,
        "cher:ka": 5031,
        "cher:ge": 5032,
        "cher:gi": 5033,
        "cher:go": 5034,
        "cher:gu": 5035,
        "cher:gv": 5036,
        "cher:ha": 5037,
        "cher:he": 5038,
        "cher:hi": 5039,
        "cher:ho": 5040,
        "cher:hu": 5041,
        "cher:hv": 5042,
        "cher:la": 5043,
        "cher:le": 5044,
        "cher:li": 5045,
        "cher:lo": 5046,
        "cher:lu": 5047,
        "cher:lv": 5048,
        "cher:ma": 5049,
        "cher:me": 5050,
        "cher:mi": 5051,
        "cher:mo": 5052,
        "cher:mu": 5053,
        "cher:na": 5054,
        "cher:hna": 5055,
        "cher:nah": 5056,
        "cher:ne": 5057,
        "cher:ni": 5058,
        "cher:no": 5059,
        "cher:nu": 5060,
        "cher:nv": 5061,
        "cher:qua": 5062,
        "cher:que": 5063,
        "cher:qui": 5064,
        "cher:quo": 5065,
        "cher:quu": 5066,
        "cher:quv": 5067,
        "cher:sa": 5068,
        "cher:s": 5069,
        "cher:se": 5070,
        "cher:si": 5071,
        "cher:so": 5072,
        "cher:su": 5073,
        "cher:sv": 5074,
        "cher:da": 5075,
        "cher:ta": 5076,
        "cher:de": 5077,
        "cher:te": 5078,
        "cher:di": 5079,
        "cher:ti": 5080,
        "cher:do": 5081,
        "cher:du": 5082,
        "cher:dv": 5083,
        "cher:dla": 5084,
        "cher:tla": 5085,
        "cher:tle": 5086,
        "cher:tli": 5087,
        "cher:tlo": 5088,
        "cher:tlu": 5089,
        "cher:tlv": 5090,
        "cher:tsa": 5091,
        "cher:tse": 5092,
        "cher:tsi": 5093,
        "cher:tso": 5094,
        "cher:tsu": 5095,
        "cher:tsv": 5096,
        "cher:wa": 5097,
        "cher:we": 5098,
        "cher:wi": 5099,
        "cher:wo": 5100,
        "cher:wu": 5101,
        "cher:wv": 5102,
        "cher:ya": 5103,
        "cher:ye": 5104,
        "cher:yi": 5105,
        "cher:yo": 5106,
        "cher:yu": 5107,
        "cher:yv": 5108,
        "cher:mv": 5109,
        "cher:yesmall": 5112,
        "cher:yismall": 5113,
        "cher:yosmall": 5114,
        "cher:yusmall": 5115,
        "cher:yvsmall": 5116,
        "cher:mvsmall": 5117,
        "runr:fehu_feoh_fe_f": 5792,
        "runr:v": 5793,
        "runr:uruz_ur_u": 5794,
        "runr:yr": 5795,
        "runr:y": 5796,
        "runr:w": 5797,
        "runr:thurisaz_thurs_thorn": 5798,
        "runr:eth": 5799,
        "runr:ansuz_a": 5800,
        "runr:os_o": 5801,
        "runr:ac_a": 5802,
        "runr:aesc": 5803,
        "runr:long_branch_oss_o": 5804,
        "runr:short_twig_oss_o": 5805,
        "runr:o": 5806,
        "runr:oe": 5807,
        "runr:on": 5808,
        "runr:raido_rad_reid_r": 5809,
        "runr:kauna": 5810,
        "runr:cen": 5811,
        "runr:kaun_k": 5812,
        "runr:g": 5813,
        "runr:eng": 5814,
        "runr:gebo_gyfu_g": 5815,
        "runr:gar": 5816,
        "runr:wunjo_wynn_w": 5817,
        "runr:haglaz_h": 5818,
        "runr:haegl_h": 5819,
        "runr:long_branch_hagall_h": 5820,
        "runr:short_twig_hagall_h": 5821,
        "runr:naudiz_nyd_naud_n": 5822,
        "runr:short_twig_naud_n": 5823,
        "runr:dotted_n": 5824,
        "runr:isaz_is_iss_i": 5825,
        "runr:e": 5826,
        "runr:jeran_j": 5827,
        "runr:ger": 5828,
        "runr:long_branch_ar_ae": 5829,
        "runr:short_twig_ar_a": 5830,
        "runr:iwaz_eoh": 5831,
        "runr:pertho_peorth_p": 5832,
        "runr:algiz_eolhx": 5833,
        "runr:sowilo_s": 5834,
        "runr:sigel_long_branch_sol_s": 5835,
        "runr:short_twig_sol_s": 5836,
        "runr:c": 5837,
        "runr:z": 5838,
        "runr:tiwaz_tir_tyr_t": 5839,
        "runr:short_twig_tyr_t": 5840,
        "runr:d": 5841,
        "runr:berkanan_beorc_bjarkan_b": 5842,
        "runr:short_twig_bjarkan_b": 5843,
        "runr:dotted_p": 5844,
        "runr:open_p": 5845,
        "runr:ehwaz_eh_e": 5846,
        "runr:mannaz_man_m": 5847,
        "runr:long_branch_madr_m": 5848,
        "runr:short_twig_madr_m": 5849,
        "runr:laukaz_lagu_logr_l": 5850,
        "runr:dotted_l": 5851,
        "runr:ingwaz": 5852,
        "runr:ing": 5853,
        "runr:dagaz_daeg_d": 5854,
        "runr:othalan_ethel_o": 5855,
        "runr:ear": 5856,
        "runr:ior": 5857,
        "runr:cweorth": 5858,
        "runr:calc": 5859,
        "runr:cealc": 5860,
        "runr:stan": 5861,
        "runr:long_branch_yr": 5862,
        "runr:short_twig_yr": 5863,
        "runr:icelandic_yr": 5864,
        "runr:q": 5865,
        "runr:x": 5866,
        "runr:single_punctuation": 5867,
        "runr:multiple_punctuation": 5868,
        "runr:cross_punctuation": 5869,
        "runr:arlaug_symbol": 5870,
        "runr:tvimadur_symbol": 5871,
        "runr:belgthor_symbol": 5872,
        "runr:k": 5873,
        "runr:sh": 5874,
        "runr:oo": 5875,
        "runr:franks_casket_os": 5876,
        "runr:franks_casket_is": 5877,
        "runr:franks_casket_eh": 5878,
        "runr:franks_casket_ac": 5879,
        "runr:franks_casket_aesc": 5880,
        "mong:birga": 6144,
        "mong:ellipsis": 6145,
        "mong:comma": 6146,
        "mong:period": 6147,
        "mong:colon": 6148,
        "mong:fourdots": 6149,
        "mong:softhyphentodo": 6150,
        "mong:syllableboundarymarkersibe": 6151,
        "mong:commamanchu": 6152,
        "mong:periodmanchu": 6153,
        "mong:nirugu": 6154,
        "mong:freevariationselectorone": 6155,
        "mong:freevariationselectortwo": 6156,
        "mong:freevariationselectorthree": 6157,
        "mong:vowelseparator": 6158,
        "mong:zero": 6160,
        "mong:one": 6161,
        "mong:two": 6162,
        "mong:three": 6163,
        "mong:four": 6164,
        "mong:five": 6165,
        "mong:six": 6166,
        "mong:seven": 6167,
        "mong:eight": 6168,
        "mong:nine": 6169,
        "mong:a": 6176,
        "mong:e": 6177,
        "mong:i": 6178,
        "mong:o": 6179,
        "mong:u": 6180,
        "mong:oe": 6181,
        "mong:ue": 6182,
        "mong:ee": 6183,
        "mong:na": 6184,
        "mong:ang": 6185,
        "mong:ba": 6186,
        "mong:pa": 6187,
        "mong:qa": 6188,
        "mong:ga": 6189,
        "mong:ma": 6190,
        "mong:la": 6191,
        "mong:sa": 6192,
        "mong:sha": 6193,
        "mong:ta": 6194,
        "mong:da": 6195,
        "mong:cha": 6196,
        "mong:ja": 6197,
        "mong:ya": 6198,
        "mong:ra": 6199,
        "mong:wa": 6200,
        "mong:fa": 6201,
        "mong:ka": 6202,
        "mong:kha": 6203,
        "mong:tsa": 6204,
        "mong:za": 6205,
        "mong:haa": 6206,
        "mong:zra": 6207,
        "mong:lha": 6208,
        "mong:zhi": 6209,
        "mong:chi": 6210,
        "mong:longvowelsigntodo": 6211,
        "mong:etodo": 6212,
        "mong:itodo": 6213,
        "mong:otodo": 6214,
        "mong:utodo": 6215,
        "mong:oetodo": 6216,
        "mong:uetodo": 6217,
        "mong:angtodo": 6218,
        "mong:batodo": 6219,
        "mong:patodo": 6220,
        "mong:qatodo": 6221,
        "mong:gatodo": 6222,
        "mong:matodo": 6223,
        "mong:tatodo": 6224,
        "mong:datodo": 6225,
        "mong:chatodo": 6226,
        "mong:jatodo": 6227,
        "mong:tsatodo": 6228,
        "mong:yatodo": 6229,
        "mong:watodo": 6230,
        "mong:katodo": 6231,
        "mong:gaatodo": 6232,
        "mong:haatodo": 6233,
        "mong:jiatodo": 6234,
        "mong:niatodo": 6235,
        "mong:dzatodo": 6236,
        "mong:esibe": 6237,
        "mong:isibe": 6238,
        "mong:iysibe": 6239,
        "mong:uesibe": 6240,
        "mong:usibe": 6241,
        "mong:angsibe": 6242,
        "mong:kasibe": 6243,
        "mong:gasibe": 6244,
        "mong:hasibe": 6245,
        "mong:pasibe": 6246,
        "mong:shasibe": 6247,
        "mong:tasibe": 6248,
        "mong:dasibe": 6249,
        "mong:jasibe": 6250,
        "mong:fasibe": 6251,
        "mong:gaasibe": 6252,
        "mong:haasibe": 6253,
        "mong:tsasibe": 6254,
        "mong:zasibe": 6255,
        "mong:raasibe": 6256,
        "mong:chasibe": 6257,
        "mong:zhasibe": 6258,
        "mong:imanchu": 6259,
        "mong:kamanchu": 6260,
        "mong:ramanchu": 6261,
        "mong:famanchu": 6262,
        "mong:zhamanchu": 6263,
        "mong:chawithtwodots": 6264,
        "mong:anusvaraonealigali": 6272,
        "mong:visargaonealigali": 6273,
        "mong:damarualigali": 6274,
        "mong:ubadamaaligali": 6275,
        "mong:ubadamaaligaliinverted": 6276,
        "mong:baludaaligali": 6277,
        "mong:baludaaligalithree": 6278,
        "mong:aaligali": 6279,
        "mong:ialigali": 6280,
        "mong:kaaligali": 6281,
        "mong:ngaaligali": 6282,
        "mong:caaligali": 6283,
        "mong:ttaaligali": 6284,
        "mong:tthaaligali": 6285,
        "mong:ddaaligali": 6286,
        "mong:nnaaligali": 6287,
        "mong:taaligali": 6288,
        "mong:daaligali": 6289,
        "mong:paaligali": 6290,
        "mong:phaaligali": 6291,
        "mong:ssaaligali": 6292,
        "mong:zhaaligali": 6293,
        "mong:zaaligali": 6294,
        "mong:ahaligali": 6295,
        "mong:tatodoaligali": 6296,
        "mong:zhatodoaligali": 6297,
        "mong:ghamanchualigali": 6298,
        "mong:ngamanchualigali": 6299,
        "mong:camanchualigali": 6300,
        "mong:jhamanchualigali": 6301,
        "mong:ttamanchualigali": 6302,
        "mong:ddhamanchualigali": 6303,
        "mong:tamanchualigali": 6304,
        "mong:dhamanchualigali": 6305,
        "mong:ssamanchualigali": 6306,
        "mong:cyamanchualigali": 6307,
        "mong:zhamanchualigali": 6308,
        "mong:zamanchualigali": 6309,
        "mong:ualigalihalf": 6310,
        "mong:yaaligalihalf": 6311,
        "mong:bhamanchualigali": 6312,
        "mong:dagalgaaligali": 6313,
        "mong:lhamanchualigali": 6314,
        "AnGeor": 7312,
        "BanGeor": 7313,
        "GanGeor": 7314,
        "DonGeor": 7315,
        "EnGeor": 7316,
        "VinGeor": 7317,
        "ZenGeor": 7318,
        "TanGeor": 7319,
        "InGeor": 7320,
        "KanGeor": 7321,
        "LasGeor": 7322,
        "ManGeor": 7323,
        "NarGeor": 7324,
        "OnGeor": 7325,
        "ParGeor": 7326,
        "ZharGeor": 7327,
        "RaeGeor": 7328,
        "SanGeor": 7329,
        "TarGeor": 7330,
        "UnGeor": 7331,
        "PharGeor": 7332,
        "KharGeor": 7333,
        "GhanGeor": 7334,
        "QarGeor": 7335,
        "ShinGeor": 7336,
        "ChinGeor": 7337,
        "CanGeor": 7338,
        "JilGeor": 7339,
        "CilGeor": 7340,
        "CharGeor": 7341,
        "XanGeor": 7342,
        "JhanGeor": 7343,
        "HaeGeor": 7344,
        "HeGeor": 7345,
        "HieGeor": 7346,
        "WeGeor": 7347,
        "HarGeor": 7348,
        "HoeGeor": 7349,
        "FiGeor": 7350,
        "YnGeor": 7351,
        "ElifiGeor": 7352,
        "TurnedganGeor": 7353,
        "AinGeor": 7354,
        "AenGeor": 7357,
        "HardsignGeor": 7358,
        "LabialsignGeor": 7359,
        "ve:karshanatone": 7376,
        "ve:sharatone": 7377,
        "ve:prenkhatone": 7378,
        "ve:nihshvasasign": 7379,
        "ve:yajurmidlinesvaritasign": 7380,
        "ve:yajuraggravatedindependentsvaritatone": 7381,
        "ve:yajurindependentsvaritatone": 7382,
        "ve:yajurkathakaindependentsvaritatone": 7383,
        "ve:belowtonecandra": 7384,
        "ve:yajurkathakaindependentsvaritaschroedertone": 7385,
        "ve:svaritatonedbl": 7386,
        "ve:svaritatonetpl": 7387,
        "ve:kathakaanudattatone": 7388,
        "ve:dotbelowtone": 7389,
        "ve:twodotsbelowtone": 7390,
        "ve:threedotsbelowtone": 7391,
        "ve:rigkashmiriindependentsvaritatone": 7392,
        "ve:atharvaindependentsvaritatone": 7393,
        "ve:visargasvaritasign": 7394,
        "ve:visargaudattasign": 7395,
        "ve:visargaudattasignreversed": 7396,
        "ve:visargaanudattasign": 7397,
        "ve:visargaanudattasignreversed": 7398,
        "ve:visargaudattawithtailsign": 7399,
        "ve:visargaanudattawithtailsign": 7400,
        "ve:anusvaraantargomukhasign": 7401,
        "ve:anusvarabahirgomukhasign": 7402,
        "ve:anusvaravamagomukhasign": 7403,
        "ve:anusvaravamagomukhawithtailsign": 7404,
        "ve:tiryaksign": 7405,
        "ve:hexiformanusvarasignlong": 7406,
        "ve:anusvarasignlong": 7407,
        "ve:rthanganusvarasignlong": 7408,
        "ve:anusvaraubhayatomukhasign": 7409,
        "ve:ardhavisargasign": 7410,
        "ve:rotatedardhavisargasign": 7411,
        "ve:abovetonecandra": 7412,
        "ve:jihvamuliyasign": 7413,
        "ve:upadhmaniyasign": 7414,
        "ve:atikramasign": 7415,
        "ve:ringabovetone": 7416,
        "ve:ringabovetonedbl": 7417,
        "ve:anusvaraantargomukhasigndbl": 7418,
        "Asmall": 7424,
        "AEsmall": 7425,
        "aeturned": 7426,
        "Bbarsmall": 7427,
        "Csmall": 7428,
        "Dsmall": 7429,
        "Ethsmall": 7430,
        "Esmall": 7431,
        "eopenturned": 7432,
        "iturned": 7433,
        "Jsmall": 7434,
        "Ksmall": 7435,
        "Lsmallstroke": 7436,
        "Msmall": 7437,
        "Nsmallreversed": 7438,
        "Osmall": 7439,
        "Oopensmall": 7440,
        "osideways": 7441,
        "oopensideways": 7442,
        "ostrokesideways": 7443,
        "oeturned": 7444,
        "OUsmall": 7445,
        "otophalf": 7446,
        "obottomhalf": 7447,
        "Psmall": 7448,
        "Rsmallreversed": 7449,
        "Rsmallturned": 7450,
        "Tsmall": 7451,
        "Usmall": 7452,
        "usideways": 7453,
        "usidewaysdieresised": 7454,
        "mturnedsideways": 7455,
        "Vsmall": 7456,
        "Wsmall": 7457,
        "Zsmall": 7458,
        "Ezhsmall": 7459,
        "spirantvoicedlaryngeal": 7460,
        "phon:ain": 7461,
        "gr:Gammasmall": 7462,
        "gr:Lambdasmall": 7463,
        "gr:Pismall": 7464,
        "gr:RsmallHO": 7465,
        "gr:Psismall": 7466,
        "Elsmallcyr": 7467,
        "Amod": 7468,
        "Aemod": 7469,
        "Bmod": 7470,
        "Bbarmod": 7471,
        "Dmod": 7472,
        "Emod": 7473,
        "Ereversedmod": 7474,
        "Gmod": 7475,
        "Hmod": 7476,
        "Imod": 7477,
        "Jmod": 7478,
        "Kmod": 7479,
        "Lmod": 7480,
        "Mmod": 7481,
        "Nmod": 7482,
        "Nreversedmod": 7483,
        "Omod": 7484,
        "Oumod": 7485,
        "Pmod": 7486,
        "Rmod": 7487,
        "Tmod": 7488,
        "Umod": 7489,
        "Wmod": 7490,
        "amod": 7491,
        "aturnedmod": 7492,
        "alphamod": 7493,
        "aeturnedmod": 7494,
        "bmod": 7495,
        "dmod": 7496,
        "emod": 7497,
        "schwamod": 7498,
        "eopenmod": 7499,
        "eopenturnedmod": 7500,
        "gmod": 7501,
        "iturnedmod": 7502,
        "kmod": 7503,
        "mmod": 7504,
        "engmod": 7505,
        "omod": 7506,
        "oopenmod": 7507,
        "otophalfmod": 7508,
        "obottomhalfmod": 7509,
        "pmod": 7510,
        "tmod": 7511,
        "umod": 7512,
        "usidewaysmod": 7513,
        "mturnedmod": 7514,
        "vmod": 7515,
        "ainmod": 7516,
        "betamod": 7517,
        "gr:gammamod": 7518,
        "deltamod": 7519,
        "gr:phimod": 7520,
        "chimod": 7521,
        "isubscript": 7522,
        "rsubscript": 7523,
        "usubscript": 7524,
        "vsubscript": 7525,
        "gr:betasubscript": 7526,
        "gr:gammasubscript": 7527,
        "gr:rhosubscript": 7528,
        "gr:phisubscript": 7529,
        "gr:chisubscript": 7530,
        "ue": 7531,
        "bmiddletilde": 7532,
        "dmiddletilde": 7533,
        "fmiddletilde": 7534,
        "mmiddletilde": 7535,
        "nmiddletilde": 7536,
        "pmiddletilde": 7537,
        "rmiddletilde": 7538,
        "rfishmiddletilde": 7539,
        "smiddletilde": 7540,
        "tmiddletilde": 7541,
        "zmiddletilde": 7542,
        "gturned": 7543,
        "ENcyrmod": 7544,
        "ginsular": 7545,
        "thstrike": 7546,
        "Ismallstroke": 7547,
        "iotastroke": 7548,
        "pstroke": 7549,
        "Usmallstroke": 7550,
        "upsilonstroke": 7551,
        "bpalatalhook": 7552,
        "dpalatalhook": 7553,
        "fpalatalhook": 7554,
        "gpalatalhook": 7555,
        "kpalatalhook": 7556,
        "lpalatalhook": 7557,
        "mpalatalhook": 7558,
        "npalatalhook": 7559,
        "ppalatalhook": 7560,
        "rpalatalhook": 7561,
        "spalatalhook": 7562,
        "eshpalatalhook": 7563,
        "vpalatalhook": 7564,
        "xpalatalhook": 7565,
        "zpalatalhook": 7566,
        "aretroflexhook": 7567,
        "alpharetroflexhook": 7568,
        "dhooktail": 7569,
        "eretroflexhook": 7570,
        "eopenretroflexhook": 7571,
        "eopenreversedretroflexhook": 7572,
        "schwaretroflexhook": 7573,
        "iretroflexhook": 7574,
        "oopenretroflexhook": 7575,
        "eshretroflexhook": 7576,
        "uretroflexhook": 7577,
        "ezhretroflexhook": 7578,
        "alphaturnedmod": 7579,
        "cmod": 7580,
        "ccurlmod": 7581,
        "ethmod": 7582,
        "eopenreversedmod": 7583,
        "fmod": 7584,
        "dotlessjstrokemod": 7585,
        "gscriptmod": 7586,
        "hturnedmod": 7587,
        "istrokemod": 7588,
        "iotamod": 7589,
        "Ismallmod": 7590,
        "Istrokesmallmod": 7591,
        "jcrossedtailmod": 7592,
        "lretroflexhookmod": 7593,
        "lpalatalhookmod": 7594,
        "Lsmallmod": 7595,
        "mhookmod": 7596,
        "mlonglegturnedmod": 7597,
        "nlefthookmod": 7598,
        "nretroflexhookmod": 7599,
        "Nsmallmod": 7600,
        "obarmod": 7601,
        "phimod": 7602,
        "shookmod": 7603,
        "eshmod": 7604,
        "tpalatalhookmod": 7605,
        "ubarmod": 7606,
        "upsilonmod": 7607,
        "Usmallmod": 7608,
        "vhookmod": 7609,
        "vturnedmod": 7610,
        "zmod": 7611,
        "zretroflexhookmod": 7612,
        "zcurlmod": 7613,
        "ezhmod": 7614,
        "thetamod": 7615,
        "dottedgravecmb": 7616,
        "dottedacutecmb": 7617,
        "snakebelowcmb": 7618,
        "suspensionmarkcmb": 7619,
        "macronacutecmb": 7620,
        "gravemacroncmb": 7621,
        "macrongravecmb": 7622,
        "acutemacroncmb": 7623,
        "graveacutegravecmb": 7624,
        "acutegraveacutecmb": 7625,
        "rbelowcmb": 7626,
        "brevemacroncmb": 7627,
        "macronbrevecmb": 7628,
        "doubleabovecircumflexcmb": 7629,
        "aboveogonekcmb": 7630,
        "zigzagbelowcmb": 7631,
        "isbelowcmb": 7632,
        "urabovecmb": 7633,
        "usabovecmb": 7634,
        "aaboveflatcmb": 7635,
        "aecmb": 7636,
        "aocmb": 7637,
        "avcmb": 7638,
        "ccedillacmb": 7639,
        "insulardcmb": 7640,
        "ethcmb": 7641,
        "gcmb": 7642,
        "gsmallcmb": 7643,
        "kcmb": 7644,
        "lcmb": 7645,
        "lsmallcmb": 7646,
        "msmallcmb": 7647,
        "ncmb": 7648,
        "nsmallcmb": 7649,
        "rsmallcmb": 7650,
        "rrotundacmb": 7651,
        "scmb": 7652,
        "longscmb": 7653,
        "zcmb": 7654,
        "alphacmb": 7655,
        "bcmb": 7656,
        "betacmb": 7657,
        "schwacmb": 7658,
        "fcmb": 7659,
        "lwithdoublemiddletildecmb": 7660,
        "owithlightcentralizationstrokecmb": 7661,
        "pcmb": 7662,
        "eshcmb": 7663,
        "uwithlightcentralizationstrokecmb": 7664,
        "wcmb": 7665,
        "adieresiscmb": 7666,
        "odieresiscmb": 7667,
        "udieresiscmb": 7668,
        "uptackabovecmb": 7669,
        "kavykaaboverightcmb": 7670,
        "kavykaaboveleftcmb": 7671,
        "dotaboveleftcmb": 7672,
        "wideinvertedbridgebelowcmb": 7673,
        "deletionmarkcmb": 7675,
        "doubleinvertedbelowbrevecmb": 7676,
        "almostequaltobelowcmb": 7677,
        "leftarrowheadabovecmb": 7678,
        "rightarrowheadanddownarrowheadbelowcmb": 7679,
        "Aringbelow": 7680,
        "aringbelow": 7681,
        "Bdot": 7682,
        "bdot": 7683,
        "Bdotbelow": 7684,
        "bdotbelow": 7685,
        "Blinebelow": 7686,
        "blinebelow": 7687,
        "Ccedillaacute": 7688,
        "ccedillaacute": 7689,
        "Ddot": 7690,
        "ddot": 7691,
        "Ddotbelow": 7692,
        "ddotbelow": 7693,
        "Dlinebelow": 7694,
        "dlinebelow": 7695,
        "Dcommaaccent": 7696,
        "dcommaaccent": 7697,
        "Dcircumflexbelow": 7698,
        "dcircumflexbelow": 7699,
        "Emacrongrave": 7700,
        "emacrongrave": 7701,
        "Emacronacute": 7702,
        "emacronacute": 7703,
        "Ecircumflexbelow": 7704,
        "ecircumflexbelow": 7705,
        "Etildebelow": 7706,
        "etildebelow": 7707,
        "Ecedillabreve": 7708,
        "ecedillabreve": 7709,
        "Fdot": 7710,
        "fdot": 7711,
        "Gmacron": 7712,
        "gmacron": 7713,
        "Hdot": 7714,
        "hdot": 7715,
        "Hdotbelow": 7716,
        "hdotbelow": 7717,
        "Hdieresis": 7718,
        "hdieresis": 7719,
        "Hcedilla": 7720,
        "hcedilla": 7721,
        "Hbrevebelow": 7722,
        "hbrevebelow": 7723,
        "Itildebelow": 7724,
        "itildebelow": 7725,
        "Idieresisacute": 7726,
        "idieresisacute": 7727,
        "Kacute": 7728,
        "kacute": 7729,
        "Kdotbelow": 7730,
        "kdotbelow": 7731,
        "Klinebelow": 7732,
        "klinebelow": 7733,
        "Ldotbelow": 7734,
        "ldotbelow": 7735,
        "Lmacrondot": 7736,
        "lmacrondot": 7737,
        "Llinebelow": 7738,
        "llinebelow": 7739,
        "Lcircumflexbelow": 7740,
        "lcircumflexbelow": 7741,
        "Macute": 7742,
        "macute": 7743,
        "Mdot": 7744,
        "mdot": 7745,
        "Mdotbelow": 7746,
        "mdotbelow": 7747,
        "Ndot": 7748,
        "ndot": 7749,
        "Ndotbelow": 7750,
        "ndotbelow": 7751,
        "Nlinebelow": 7752,
        "nlinebelow": 7753,
        "Ncircumflexbelow": 7754,
        "ncircumflexbelow": 7755,
        "Otildeacute": 7756,
        "otildeacute": 7757,
        "Otildedieresis": 7758,
        "otildedieresis": 7759,
        "Omacrongrave": 7760,
        "omacrongrave": 7761,
        "Omacronacute": 7762,
        "omacronacute": 7763,
        "Pacute": 7764,
        "pacute": 7765,
        "Pdot": 7766,
        "pdot": 7767,
        "Rdot": 7768,
        "rdot": 7769,
        "Rdotbelow": 7770,
        "rdotbelow": 7771,
        "Rmacrondot": 7772,
        "rmacrondot": 7773,
        "Rlinebelow": 7774,
        "rlinebelow": 7775,
        "Sdot": 7776,
        "sdot": 7777,
        "Sdotbelow": 7778,
        "sdotbelow": 7779,
        "Sacutedotaccent": 7780,
        "sacutedotaccent": 7781,
        "Scarondot": 7782,
        "scarondot": 7783,
        "Sdotbelowdotabove": 7784,
        "sdotbelowdotabove": 7785,
        "Tdot": 7786,
        "tdot": 7787,
        "Tdotbelow": 7788,
        "tdotbelow": 7789,
        "Tlinebelow": 7790,
        "tlinebelow": 7791,
        "Tcircumflexbelow": 7792,
        "tcircumflexbelow": 7793,
        "Udieresisbelow": 7794,
        "udieresisbelow": 7795,
        "Utildebelow": 7796,
        "utildebelow": 7797,
        "Ucircumflexbelow": 7798,
        "ucircumflexbelow": 7799,
        "Utildeacute": 7800,
        "utildeacute": 7801,
        "Umacrondieresis": 7802,
        "umacrondieresis": 7803,
        "Vtilde": 7804,
        "vtilde": 7805,
        "Vdotbelow": 7806,
        "vdotbelow": 7807,
        "Wgrave": 7808,
        "wgrave": 7809,
        "Wacute": 7810,
        "wacute": 7811,
        "Wdieresis": 7812,
        "wdieresis": 7813,
        "Wdot": 7814,
        "wdot": 7815,
        "Wdotbelow": 7816,
        "wdotbelow": 7817,
        "Xdot": 7818,
        "xdot": 7819,
        "Xdieresis": 7820,
        "xdieresis": 7821,
        "Ydot": 7822,
        "ydot": 7823,
        "Zcircumflex": 7824,
        "zcircumflex": 7825,
        "Zdotbelow": 7826,
        "zdotbelow": 7827,
        "Zlinebelow": 7828,
        "zlinebelow": 7829,
        "hlinebelow": 7830,
        "tdieresis": 7831,
        "wring": 7832,
        "yring": 7833,
        "arighthalfring": 7834,
        "longsdot": 7835,
        "longswithdiagonalstroke": 7836,
        "longswithhighstroke": 7837,
        "Germandbls": 7838,
        "lt:delta": 7839,
        "Adotbelow": 7840,
        "adotbelow": 7841,
        "Ahoi": 7842,
        "ahoi": 7843,
        "Acircumflexacute": 7844,
        "acircumflexacute": 7845,
        "Acircumflexgrave": 7846,
        "acircumflexgrave": 7847,
        "Acircumflexhoi": 7848,
        "acircumflexhoi": 7849,
        "Acircumflextilde": 7850,
        "acircumflextilde": 7851,
        "Acircumflexdotbelow": 7852,
        "acircumflexdotbelow": 7853,
        "Abreveacute": 7854,
        "abreveacute": 7855,
        "Abrevegrave": 7856,
        "abrevegrave": 7857,
        "Abrevehoi": 7858,
        "abrevehoi": 7859,
        "Abrevetilde": 7860,
        "abrevetilde": 7861,
        "Abrevedotbelow": 7862,
        "abrevedotbelow": 7863,
        "Edotbelow": 7864,
        "edotbelow": 7865,
        "Ehoi": 7866,
        "ehoi": 7867,
        "Etilde": 7868,
        "etilde": 7869,
        "Ecircumflexacute": 7870,
        "ecircumflexacute": 7871,
        "Ecircumflexgrave": 7872,
        "ecircumflexgrave": 7873,
        "Ecircumflexhoi": 7874,
        "ecircumflexhoi": 7875,
        "Ecircumflextilde": 7876,
        "ecircumflextilde": 7877,
        "Ecircumflexdotbelow": 7878,
        "ecircumflexdotbelow": 7879,
        "Ihoi": 7880,
        "ihoi": 7881,
        "Idotbelow": 7882,
        "idotbelow": 7883,
        "Odotbelow": 7884,
        "odotbelow": 7885,
        "Ohoi": 7886,
        "ohoi": 7887,
        "Ocircumflexacute": 7888,
        "ocircumflexacute": 7889,
        "Ocircumflexgrave": 7890,
        "ocircumflexgrave": 7891,
        "Ocircumflexhoi": 7892,
        "ocircumflexhoi": 7893,
        "Ocircumflextilde": 7894,
        "ocircumflextilde": 7895,
        "Ocircumflexdotbelow": 7896,
        "ocircumflexdotbelow": 7897,
        "Ohornacute": 7898,
        "ohornacute": 7899,
        "Ohorngrave": 7900,
        "ohorngrave": 7901,
        "Ohornhoi": 7902,
        "ohornhoi": 7903,
        "Ohorntilde": 7904,
        "ohorntilde": 7905,
        "Ohorndotbelow": 7906,
        "ohorndotbelow": 7907,
        "Udotbelow": 7908,
        "udotbelow": 7909,
        "Uhoi": 7910,
        "uhoi": 7911,
        "Uhornacute": 7912,
        "uhornacute": 7913,
        "Uhorngrave": 7914,
        "uhorngrave": 7915,
        "Uhornhoi": 7916,
        "uhornhoi": 7917,
        "Uhorntilde": 7918,
        "uhorntilde": 7919,
        "Uhorndotbelow": 7920,
        "uhorndotbelow": 7921,
        "Ygrave": 7922,
        "ygrave": 7923,
        "Ydotbelow": 7924,
        "ydotbelow": 7925,
        "Yhoi": 7926,
        "yhoi": 7927,
        "Ytilde": 7928,
        "ytilde": 7929,
        "LLwelsh": 7930,
        "llwelsh": 7931,
        "Vwelsh": 7932,
        "vwelsh": 7933,
        "Yloop": 7934,
        "yloop": 7935,
        "alphalenis": 7936,
        "alphaasper": 7937,
        "alphalenisgrave": 7938,
        "alphaaspergrave": 7939,
        "alphalenisacute": 7940,
        "alphaasperacute": 7941,
        "alphalenistilde": 7942,
        "alphaaspertilde": 7943,
        "Alphalenis": 7944,
        "Alphaasper": 7945,
        "Alphalenisgrave": 7946,
        "Alphaaspergrave": 7947,
        "Alphalenisacute": 7948,
        "Alphaasperacute": 7949,
        "Alphalenistilde": 7950,
        "Alphaaspertilde": 7951,
        "epsilonlenis": 7952,
        "epsilonasper": 7953,
        "epsilonlenisgrave": 7954,
        "epsilonaspergrave": 7955,
        "epsilonlenisacute": 7956,
        "epsilonasperacute": 7957,
        "Epsilonlenis": 7960,
        "Epsilonasper": 7961,
        "Epsilonlenisgrave": 7962,
        "Epsilonaspergrave": 7963,
        "Epsilonlenisacute": 7964,
        "Epsilonasperacute": 7965,
        "etalenis": 7968,
        "etaasper": 7969,
        "etalenisgrave": 7970,
        "etaaspergrave": 7971,
        "etalenisacute": 7972,
        "etaasperacute": 7973,
        "etalenistilde": 7974,
        "etaaspertilde": 7975,
        "Etalenis": 7976,
        "Etaasper": 7977,
        "Etalenisgrave": 7978,
        "Etaaspergrave": 7979,
        "Etalenisacute": 7980,
        "Etaasperacute": 7981,
        "Etalenistilde": 7982,
        "Etaaspertilde": 7983,
        "iotalenis": 7984,
        "iotaasper": 7985,
        "iotalenisgrave": 7986,
        "iotaaspergrave": 7987,
        "iotalenisacute": 7988,
        "iotaasperacute": 7989,
        "iotalenistilde": 7990,
        "iotaaspertilde": 7991,
        "Iotalenis": 7992,
        "Iotaasper": 7993,
        "Iotalenisgrave": 7994,
        "Iotaaspergrave": 7995,
        "Iotalenisacute": 7996,
        "Iotaasperacute": 7997,
        "Iotalenistilde": 7998,
        "Iotaaspertilde": 7999,
        "omicronlenis": 8000,
        "omicronasper": 8001,
        "omicronlenisgrave": 8002,
        "omicronaspergrave": 8003,
        "omicronlenisacute": 8004,
        "omicronasperacute": 8005,
        "Omicronlenis": 8008,
        "Omicronasper": 8009,
        "Omicronlenisgrave": 8010,
        "Omicronaspergrave": 8011,
        "Omicronlenisacute": 8012,
        "Omicronasperacute": 8013,
        "upsilonlenis": 8016,
        "upsilonasper": 8017,
        "upsilonlenisgrave": 8018,
        "upsilonaspergrave": 8019,
        "upsilonlenisacute": 8020,
        "upsilonasperacute": 8021,
        "upsilonlenistilde": 8022,
        "upsilonaspertilde": 8023,
        "Upsilonasper": 8025,
        "Upsilonaspergrave": 8027,
        "Upsilonasperacute": 8029,
        "Upsilonaspertilde": 8031,
        "omegalenis": 8032,
        "omegaasper": 8033,
        "omegalenisgrave": 8034,
        "omegaaspergrave": 8035,
        "omegalenisacute": 8036,
        "omegaasperacute": 8037,
        "omegalenistilde": 8038,
        "omegaaspertilde": 8039,
        "Omegalenis": 8040,
        "Omegaasper": 8041,
        "Omegalenisgrave": 8042,
        "Omegaaspergrave": 8043,
        "Omegalenisacute": 8044,
        "Omegaasperacute": 8045,
        "Omegalenistilde": 8046,
        "Omegaaspertilde": 8047,
        "alphagrave": 8048,
        "alphaacute": 8049,
        "epsilongrave": 8050,
        "epsilonacute": 8051,
        "etagrave": 8052,
        "etaacute": 8053,
        "iotagrave": 8054,
        "iotaacute": 8055,
        "omicrongrave": 8056,
        "omicronacute": 8057,
        "upsilongrave": 8058,
        "upsilonacute": 8059,
        "omegagrave": 8060,
        "omegaacute": 8061,
        "alphalenisiotasub": 8064,
        "alphaasperiotasub": 8065,
        "alphalenisgraveiotasub": 8066,
        "alphaaspergraveiotasub": 8067,
        "alphalenisacuteiotasub": 8068,
        "alphaasperacuteiotasub": 8069,
        "alphalenistildeiotasub": 8070,
        "alphaaspertildeiotasub": 8071,
        "Alphalenisiotasub": 8072,
        "Alphaasperiotasub": 8073,
        "Alphalenisgraveiotasub": 8074,
        "Alphaaspergraveiotasub": 8075,
        "Alphalenisacuteiotasub": 8076,
        "Alphaasperacuteiotasub": 8077,
        "Alphalenistildeiotasub": 8078,
        "Alphaaspertildeiotasub": 8079,
        "etalenisiotasub": 8080,
        "etaasperiotasub": 8081,
        "etalenisgraveiotasub": 8082,
        "etaaspergraveiotasub": 8083,
        "etalenisacuteiotasub": 8084,
        "etaasperacuteiotasub": 8085,
        "etalenistildeiotasub": 8086,
        "etaaspertildeiotasub": 8087,
        "Etalenisiotasub": 8088,
        "Etaasperiotasub": 8089,
        "Etalenisgraveiotasub": 8090,
        "Etaaspergraveiotasub": 8091,
        "Etalenisacuteiotasub": 8092,
        "Etaasperacuteiotasub": 8093,
        "Etalenistildeiotasub": 8094,
        "Etaaspertildeiotasub": 8095,
        "omegalenisiotasub": 8096,
        "omegaasperiotasub": 8097,
        "omegalenisgraveiotasub": 8098,
        "omegaaspergraveiotasub": 8099,
        "omegalenisacuteiotasub": 8100,
        "omegaasperacuteiotasub": 8101,
        "omegalenistildeiotasub": 8102,
        "omegaaspertildeiotasub": 8103,
        "Omegalenisiotasub": 8104,
        "Omegaasperiotasub": 8105,
        "Omegalenisgraveiotasub": 8106,
        "Omegaaspergraveiotasub": 8107,
        "Omegalenisacuteiotasub": 8108,
        "Omegaasperacuteiotasub": 8109,
        "Omegalenistildeiotasub": 8110,
        "Omegaaspertildeiotasub": 8111,
        "alphabreve": 8112,
        "alphawithmacron": 8113,
        "alphagraveiotasub": 8114,
        "alphaiotasub": 8115,
        "alphaacuteiotasub": 8116,
        "alphatilde": 8118,
        "alphatildeiotasub": 8119,
        "Alphabreve": 8120,
        "Alphawithmacron": 8121,
        "Alphagrave": 8122,
        "Alphaacute": 8123,
        "Alphaiotasub": 8124,
        "KORONIS": 8125,
        "iotaadscript": 8126,
        "lenis": 8127,
        "gr:tilde": 8128,
        "dieresistilde": 8129,
        "etagraveiotasub": 8130,
        "etaiotasub": 8131,
        "etaacuteiotasub": 8132,
        "etatilde": 8134,
        "etatildeiotasub": 8135,
        "Epsilongrave": 8136,
        "Epsilonacute": 8137,
        "Etagrave": 8138,
        "Etaacute": 8139,
        "Etaiotasub": 8140,
        "lenisgrave": 8141,
        "lenisacute": 8142,
        "lenistilde": 8143,
        "iotabreve": 8144,
        "iotawithmacron": 8145,
        "iotadieresisgrave": 8146,
        "iotadieresisacute": 8147,
        "iotatilde": 8150,
        "iotadieresistilde": 8151,
        "Iotabreve": 8152,
        "Iotawithmacron": 8153,
        "Iotagrave": 8154,
        "Iotaacute": 8155,
        "aspergrave": 8157,
        "asperacute": 8158,
        "aspertilde": 8159,
        "upsilonbreve": 8160,
        "upsilonwithmacron": 8161,
        "upsilondieresisgrave": 8162,
        "upsilondieresisacute": 8163,
        "rholenis": 8164,
        "rhoasper": 8165,
        "upsilontilde": 8166,
        "upsilondieresistilde": 8167,
        "Upsilonbreve": 8168,
        "Upsilonwithmacron": 8169,
        "Upsilongrave": 8170,
        "Upsilonacute": 8171,
        "Rhoasper": 8172,
        "dieresisgrave": 8173,
        "dieresisacute": 8174,
        "gr:grave": 8175,
        "omegagraveiotasub": 8178,
        "omegaiotasub": 8179,
        "omegaacuteiotasub": 8180,
        "omegatilde": 8182,
        "omegatildeiotasub": 8183,
        "Omicrongrave": 8184,
        "Omicronacute": 8185,
        "Omegagrave": 8186,
        "Omegaacute": 8187,
        "Omegaiotasub": 8188,
        "gr:acute": 8189,
        "asper": 8190,
        "enquad": 8192,
        "emquad": 8193,
        "enspace": 8194,
        "emspace": 8195,
        "threeperemspace": 8196,
        "fourperemspace": 8197,
        "sixperemspace": 8198,
        "figurespace": 8199,
        "punctuationspace": 8200,
        "thinspace": 8201,
        "hairspace": 8202,
        "zerowidthspace": 8203,
        "zerowidthnonjoiner": 8204,
        "zerowidthjoiner": 8205,
        "lefttorightmark": 8206,
        "righttoleftmark": 8207,
        "gnrl:hyphen": 8208,
        "nonbreakinghyphen": 8209,
        "figuredash": 8210,
        "endash": 8211,
        "emdash": 8212,
        "horizontalbar": 8213,
        "verticalbardbl": 8214,
        "underscoredbl": 8215,
        "quoteleft": 8216,
        "quoteright": 8217,
        "quotesinglbase": 8218,
        "quotereversed": 8219,
        "quotedblleft": 8220,
        "quotedblright": 8221,
        "quotedblbase": 8222,
        "quotedblreversed": 8223,
        "dagger": 8224,
        "daggerdbl": 8225,
        "bullet": 8226,
        "triangularbullet": 8227,
        "onedotenleader": 8228,
        "twodotenleader": 8229,
        "ellipsis": 8230,
        "hyphenationpoint": 8231,
        "lineseparator": 8232,
        "paragraphseparator": 8233,
        "lefttorightembed": 8234,
        "righttoleftembed": 8235,
        "popdirectionalformatting": 8236,
        "lefttorightoverride": 8237,
        "righttoleftoverride": 8238,
        "narrownobreakspace": 8239,
        "perthousand": 8240,
        "pertenthousandsign": 8241,
        "minute": 8242,
        "second": 8243,
        "millisecond": 8244,
        "minutereversed": 8245,
        "secondreversed": 8246,
        "millisecondreversed": 8247,
        "caret": 8248,
        "guilsinglleft": 8249,
        "guilsinglright": 8250,
        "referencemark": 8251,
        "exclamdbl": 8252,
        "interrobang": 8253,
        "overline": 8254,
        "undertie": 8255,
        "charactertie": 8256,
        "caretinsertionpoint": 8257,
        "asterism": 8258,
        "hyphenbullet": 8259,
        "fraction": 8260,
        "bracketleftsquarequill": 8261,
        "bracketrightsquarequill": 8262,
        "questiondbl": 8263,
        "questionexclamationmark": 8264,
        "exclamationquestion": 8265,
        "tironiansignet": 8266,
        "pilcrowsignreversed": 8267,
        "blackwardsbulletleft": 8268,
        "blackwardsbulletright": 8269,
        "lowasterisk": 8270,
        "semicolonreversed": 8271,
        "closeup": 8272,
        "twoasterisksalignedvertically": 8273,
        "commercialminussign": 8274,
        "swungdash": 8275,
        "invertedundertie": 8276,
        "flowerpunctuationmark": 8277,
        "threedotpunctuation": 8278,
        "quadrupleminute": 8279,
        "fourdotpunctuation": 8280,
        "fivedotpunctuation": 8281,
        "twodotpunctuation": 8282,
        "fourdotmark": 8283,
        "dottedcross": 8284,
        "tricolon": 8285,
        "verticalfourdots": 8286,
        "mediummathematicalspace": 8287,
        "wordjoiner": 8288,
        "functionapplication": 8289,
        "invisibletimes": 8290,
        "invisibleseparator": 8291,
        "invisibleplus": 8292,
        "lefttorightisolate": 8294,
        "righttoleftisolate": 8295,
        "firststrongisolate": 8296,
        "popdirectionalisolate": 8297,
        "inhibitsymmetricswapping": 8298,
        "activatesymmetricswapping": 8299,
        "inhibitarabicformshaping": 8300,
        "activatearabicformshaping": 8301,
        "nationaldigitshapes": 8302,
        "nominaldigitshapes": 8303,
        "zero.superior": 8304,
        "i.superior": 8305,
        "four.superior": 8308,
        "five.superior": 8309,
        "six.superior": 8310,
        "seven.superior": 8311,
        "eight.superior": 8312,
        "nine.superior": 8313,
        "plus.superior": 8314,
        "minus.superior": 8315,
        "equal.superior": 8316,
        "parenleft.superior": 8317,
        "parenright.superior": 8318,
        "n.superior": 8319,
        "zero.inferior": 8320,
        "one.inferior": 8321,
        "two.inferior": 8322,
        "three.inferior": 8323,
        "four.inferior": 8324,
        "five.inferior": 8325,
        "six.inferior": 8326,
        "seven.inferior": 8327,
        "eight.inferior": 8328,
        "nine.inferior": 8329,
        "plus.inferior": 8330,
        "minus.inferior": 8331,
        "equal.inferior": 8332,
        "parenleft.inferior": 8333,
        "parenright.inferior": 8334,
        "a.inferior": 8336,
        "e.inferior": 8337,
        "o.inferior": 8338,
        "x.inferior": 8339,
        "schwa.inferior": 8340,
        "h.inferior": 8341,
        "k.inferior": 8342,
        "l.inferior": 8343,
        "m.inferior": 8344,
        "n.inferior": 8345,
        "p.inferior": 8346,
        "s.inferior": 8347,
        "t.inferior": 8348,
        "euroarchaic": 8352,
        "colonmonetary": 8353,
        "cruzeiro": 8354,
        "franc": 8355,
        "lira": 8356,
        "mill": 8357,
        "naira": 8358,
        "peseta": 8359,
        "rupee": 8360,
        "won": 8361,
        "newsheqel": 8362,
        "dong": 8363,
        "Euro": 8364,
        "kip": 8365,
        "tugrik": 8366,
        "drachma": 8367,
        "germanpenny": 8368,
        "peso": 8369,
        "guarani": 8370,
        "austral": 8371,
        "hryvnia": 8372,
        "cedi": 8373,
        "livretournois": 8374,
        "spesmilo": 8375,
        "tenge": 8376,
        "indianrupee": 8377,
        "turkishlira": 8378,
        "nordicmark": 8379,
        "manat": 8380,
        "ruble": 8381,
        "lari": 8382,
        "bitcoin": 8383,
        "accountof": 8448,
        "addressedsubject": 8449,
        "Cdblstruck": 8450,
        "degreecelsius": 8451,
        "centreline": 8452,
        "careof": 8453,
        "cadauna": 8454,
        "euler": 8455,
        "scruple": 8456,
        "degreefahrenheit": 8457,
        "lttr:gscript": 8458,
        "Hscript": 8459,
        "Hfraktur": 8460,
        "Hdblstruck": 8461,
        "planck": 8462,
        "plancktwopi": 8463,
        "Iscript": 8464,
        "Ifraktur": 8465,
        "Lscript": 8466,
        "litre": 8467,
        "lbbar": 8468,
        "Ndblstruck": 8469,
        "numero": 8470,
        "soundcopyright": 8471,
        "weierstrass": 8472,
        "Pdblstruck": 8473,
        "Qdblstruck": 8474,
        "Rscript": 8475,
        "Rfraktur": 8476,
        "Rdblstruck": 8477,
        "prescription": 8478,
        "response": 8479,
        "servicemark": 8480,
        "telephone": 8481,
        "trademark": 8482,
        "versicle": 8483,
        "Zdblstruck": 8484,
        "ounce": 8485,
        "ohm": 8486,
        "ohminverted": 8487,
        "Zfraktur": 8488,
        "iotaturned": 8489,
        "kelvin": 8490,
        "angstrom": 8491,
        "Bscript": 8492,
        "Cfraktur": 8493,
        "estimated": 8494,
        "escript": 8495,
        "Escript": 8496,
        "Fscript": 8497,
        "Fturned": 8498,
        "Mscript": 8499,
        "oscript": 8500,
        "aleph": 8501,
        "bet": 8502,
        "gimel": 8503,
        "dalet": 8504,
        "information": 8505,
        "Qrotated": 8506,
        "facsimile": 8507,
        "pidblstruck": 8508,
        "gammadblstruck": 8509,
        "Gammadblstruck": 8510,
        "Pidblstruck": 8511,
        "summationdblstruck": 8512,
        "Gturnedsans": 8513,
        "Lturnedsans": 8514,
        "Lreversedsans": 8515,
        "Yturnedsans": 8516,
        "Ddblstruckitalic": 8517,
        "ddblstruckitalic": 8518,
        "edblstruckitalic": 8519,
        "idblstruckitalic": 8520,
        "jdblstruckitalic": 8521,
        "propertyline": 8522,
        "ampersandturned": 8523,
        "per": 8524,
        "aktieselskab": 8525,
        "fturned": 8526,
        "forsamaritan": 8527,
        "oneseventh": 8528,
        "oneninth": 8529,
        "onetenth": 8530,
        "onethird": 8531,
        "twothirds": 8532,
        "onefifth": 8533,
        "twofifths": 8534,
        "threefifths": 8535,
        "fourfifths": 8536,
        "onesixth": 8537,
        "fivesixths": 8538,
        "oneeighth": 8539,
        "threeeighths": 8540,
        "fiveeighths": 8541,
        "seveneighths": 8542,
        "onefraction": 8543,
        "one.roman": 8544,
        "two.roman": 8545,
        "three.roman": 8546,
        "four.roman": 8547,
        "five.roman": 8548,
        "six.roman": 8549,
        "seven.roman": 8550,
        "eight.roman": 8551,
        "nine.roman": 8552,
        "ten.roman": 8553,
        "eleven.roman": 8554,
        "twelve.roman": 8555,
        "fifty.roman": 8556,
        "onehundred.roman": 8557,
        "fivehundred.roman": 8558,
        "onethousand.roman": 8559,
        "one.romansmall": 8560,
        "two.romansmall": 8561,
        "three.romansmall": 8562,
        "four.romansmall": 8563,
        "five.romansmall": 8564,
        "six.romansmall": 8565,
        "seven.romansmall": 8566,
        "eight.romansmall": 8567,
        "nine.romansmall": 8568,
        "ten.romansmall": 8569,
        "eleven.romansmall": 8570,
        "twelve.romansmall": 8571,
        "fifty.romansmall": 8572,
        "onehundred.romansmall": 8573,
        "fivehundred.romansmall": 8574,
        "onethousand.romansmall": 8575,
        "onethousandcd.roman": 8576,
        "fivethousand.roman": 8577,
        "tenthousand.roman": 8578,
        "reversedonehundred.roman": 8579,
        "creversed": 8580,
        "sixlateform.roman": 8581,
        "fiftyearlyform.roman": 8582,
        "fiftythousand.roman": 8583,
        "onehundredthousand.roman": 8584,
        "zerothirds": 8585,
        "turneddigittwo": 8586,
        "turneddigitthree": 8587,
        "arrowleft": 8592,
        "arrowup": 8593,
        "arrowright": 8594,
        "arrowdown": 8595,
        "arrowleftright": 8596,
        "arrowupdown": 8597,
        "arrowNW": 8598,
        "arrowNE": 8599,
        "arrowSE": 8600,
        "arrowSW": 8601,
        "arrowleftstroke": 8602,
        "arrowrightstroke": 8603,
        "arrowleftwave": 8604,
        "arrowrightwave": 8605,
        "arrowlefttwoheaded": 8606,
        "arrowuptwoheaded": 8607,
        "arrowrighttwoheaded": 8608,
        "arrowdowntwoheaded": 8609,
        "arrowlefttail": 8610,
        "arrowrighttail": 8611,
        "arrowleftfrombar": 8612,
        "arrowupfrombar": 8613,
        "arrowrightfrombar": 8614,
        "arrowdownfrombar": 8615,
        "arrowupdownwithbase": 8616,
        "arrowlefthook": 8617,
        "arrowrighthook": 8618,
        "arrowleftloop": 8619,
        "arrowrightloop": 8620,
        "arrowleftrightwave": 8621,
        "arrowleftrightstroke": 8622,
        "arrowdownzigzag": 8623,
        "arrowleftuptip": 8624,
        "arrowrightuptip": 8625,
        "arrowleftdowntip": 8626,
        "arrowrightdowntip": 8627,
        "arrowrightdowncorner": 8628,
        "arrowleftdowncorner": 8629,
        "arrowanticlockwisesemicircle": 8630,
        "arrowclockwisesemicircle": 8631,
        "arrowlongNWtobar": 8632,
        "arrowleftoverrighttobar": 8633,
        "arrowanticlockwiseopencircle": 8634,
        "arrowclockwiseopencircle": 8635,
        "harpoonleftbarbup": 8636,
        "harpoonleftbarbdown": 8637,
        "harpoonupbarbright": 8638,
        "harpoonupbarbleft": 8639,
        "harpoonrightbarbup": 8640,
        "harpoonrightbarbdown": 8641,
        "harpoondownbarbright": 8642,
        "harpoondownbarbleft": 8643,
        "rightarrowoverleftarrow": 8644,
        "uparrowleftofdownarrow": 8645,
        "leftarrowoverrightarrow": 8646,
        "arrowspairedleft": 8647,
        "arrowspairedup": 8648,
        "arrowspairedright": 8649,
        "arrowspaireddown": 8650,
        "leftharpoonoverrightharpoon": 8651,
        "rightharpoonoverleftharpoon": 8652,
        "dblarrowleftstroke": 8653,
        "dblarrowleftrightstroke": 8654,
        "dblarrowrightstroke": 8655,
        "dblarrowleft": 8656,
        "dblarrowup": 8657,
        "dblarrowright": 8658,
        "dblarrowdown": 8659,
        "dblarrowleftright": 8660,
        "dblarrowupdown": 8661,
        "dblarrowNW": 8662,
        "dblarrowNE": 8663,
        "dblarrowSE": 8664,
        "dblarrowSW": 8665,
        "triplearrowleft": 8666,
        "triplearrowright": 8667,
        "arrowleftsquiggle": 8668,
        "arrowrightsquiggle": 8669,
        "dblstrokearrowup": 8670,
        "dblstrokearrowdown": 8671,
        "arrowleftdashed": 8672,
        "arrowupdashed": 8673,
        "arrowrightdashed": 8674,
        "arrowdowndashed": 8675,
        "arrowlefttobar": 8676,
        "arrowrighttobar": 8677,
        "whitearrowleft": 8678,
        "whitearrowup": 8679,
        "whitearrowright": 8680,
        "whitearrowdown": 8681,
        "whitearrowupfrombar": 8682,
        "whitearrowonpedestalup": 8683,
        "horizontalbarwhitearrowonpedestalup": 8684,
        "verticalbarwhitearrowonpedestalup": 8685,
        "whitedblarrowup": 8686,
        "whitedblarrowonpedestalup": 8687,
        "whitearrowfromwallright": 8688,
        "tocornerarrowNW": 8689,
        "tocornerarrowSE": 8690,
        "whitearrowupdown": 8691,
        "arrowrightsmallcircle": 8692,
        "downwarrowleftofuparrow": 8693,
        "threerightarrows": 8694,
        "verticalstrokearrowleft": 8695,
        "verticalstrokearrowright": 8696,
        "verticalstrokearrowleftright": 8697,
        "verticalsdbltrokearrowleft": 8698,
        "verticalsdbltrokearrowright": 8699,
        "verticalsdbltrokearrowleftright": 8700,
        "openheadarrowleft": 8701,
        "openheadarrowright": 8702,
        "openheadarrowleftright": 8703,
        "universal": 8704,
        "complement": 8705,
        "partialdiff": 8706,
        "existential": 8707,
        "notexistential": 8708,
        "emptyset": 8709,
        "increment": 8710,
        "gradient": 8711,
        "element": 8712,
        "notelement": 8713,
        "elementsmall": 8714,
        "suchthat": 8715,
        "notcontains": 8716,
        "containsasmembersmall": 8717,
        "endpro": 8718,
        "product": 8719,
        "coproductarray": 8720,
        "summation": 8721,
        "minus": 8722,
        "minusplus": 8723,
        "dotplus": 8724,
        "divisionslash": 8725,
        "setminus": 8726,
        "asteriskmath": 8727,
        "ringoperator": 8728,
        "bulletoperator": 8729,
        "radical": 8730,
        "math:cuberoot": 8731,
        "math:fourthroot": 8732,
        "proportional": 8733,
        "infinity": 8734,
        "orthogonal": 8735,
        "angle": 8736,
        "measuredangle": 8737,
        "sphericalangle": 8738,
        "divides": 8739,
        "doesnotdivide": 8740,
        "parallel": 8741,
        "notparallel": 8742,
        "logicaland": 8743,
        "logicalor": 8744,
        "intersection": 8745,
        "union": 8746,
        "integral": 8747,
        "integraldbl": 8748,
        "integraltpl": 8749,
        "integralcontour": 8750,
        "integralsurface": 8751,
        "integralvolume": 8752,
        "integralclockwise": 8753,
        "integralcontourclockwise": 8754,
        "integralcontouranticlockwise": 8755,
        "therefore": 8756,
        "because": 8757,
        "ratio": 8758,
        "proportion": 8759,
        "dotminus": 8760,
        "excess": 8761,
        "geometricproportion": 8762,
        "homotic": 8763,
        "similar": 8764,
        "tildereversed": 8765,
        "lazysinverted": 8766,
        "sinewave": 8767,
        "wreathproduct": 8768,
        "nottilde": 8769,
        "minustilde": 8770,
        "asympticallyequal": 8771,
        "notasympticallyequal": 8772,
        "congruent": 8773,
        "approximatelybutnotactuallyequal": 8774,
        "neirapproximatelynoractuallyequal": 8775,
        "approxequal": 8776,
        "notalmostequal": 8777,
        "almostequalorequal": 8778,
        "tildetpl": 8779,
        "allequal": 8780,
        "equivalent": 8781,
        "geometricallyequivalent": 8782,
        "differencebetween": 8783,
        "approacheslimit": 8784,
        "geometricallyequal": 8785,
        "approximatelyequalorimage": 8786,
        "imageorapproximatelyequal": 8787,
        "colonequals": 8788,
        "equalscolon": 8789,
        "ringinequal": 8790,
        "ringequal": 8791,
        "corresponds": 8792,
        "estimates": 8793,
        "equiangular": 8794,
        "starequals": 8795,
        "deltaequal": 8796,
        "equalbydefinition": 8797,
        "measuredby": 8798,
        "questionedequal": 8799,
        "notequal": 8800,
        "equivalence": 8801,
        "notidentical": 8802,
        "strictlyequivalent": 8803,
        "lessequal": 8804,
        "greaterequal": 8805,
        "lessoverequal": 8806,
        "greateroverequal": 8807,
        "lessbutnotequal": 8808,
        "greaterbutnotequal": 8809,
        "muchless": 8810,
        "muchgreater": 8811,
        "between": 8812,
        "notequivalent": 8813,
        "notless": 8814,
        "notgreater": 8815,
        "neirlessnorequal": 8816,
        "neirgreaternorequal": 8817,
        "lessorequivalent": 8818,
        "greaterorequivalent": 8819,
        "neirlessnorequivalent": 8820,
        "neirgreaternorequivalent": 8821,
        "lessorgreater": 8822,
        "greaterorless": 8823,
        "neirlessnorgreater": 8824,
        "neirgreaternorless": 8825,
        "precedes": 8826,
        "succeeds": 8827,
        "precedesorequal": 8828,
        "succeedsorequal": 8829,
        "precedesorequivalent": 8830,
        "succeedsorequivalent": 8831,
        "doesnotprecede": 8832,
        "doesnotsucceed": 8833,
        "propersubset": 8834,
        "propersuperset": 8835,
        "notsubset": 8836,
        "notasersetup": 8837,
        "reflexsubset": 8838,
        "reflexsuperset": 8839,
        "neirasubsetnorequal": 8840,
        "neirasersetnorequalup": 8841,
        "subsetnotequal": 8842,
        "sersetnotequalup": 8843,
        "multiset": 8844,
        "multisetmultiplication": 8845,
        "multisetunion": 8846,
        "squareimage": 8847,
        "squareoriginal": 8848,
        "squareimageorequal": 8849,
        "squareoriginalorequal": 8850,
        "squarecap": 8851,
        "squarecup": 8852,
        "circleplus": 8853,
        "circledminus": 8854,
        "circlemultiply": 8855,
        "circleddivisionslash": 8856,
        "circleddotoperator": 8857,
        "circledringoperator": 8858,
        "circledasteriskoperator": 8859,
        "circledequals": 8860,
        "circleddash": 8861,
        "squaredplus": 8862,
        "squaredminus": 8863,
        "squaredtimes": 8864,
        "squareddotoperator": 8865,
        "tackright": 8866,
        "tackleft": 8867,
        "tackdown": 8868,
        "tackup": 8869,
        "assertion": 8870,
        "models": 8871,
        "true": 8872,
        "forces": 8873,
        "turnstiletplverticalbarright": 8874,
        "turnstiledblverticalbarright": 8875,
        "doesnotprove": 8876,
        "nottrue": 8877,
        "doesnotforce": 8878,
        "negatedturnstiledblverticalbarright": 8879,
        "precedesunderrelation": 8880,
        "succeedsunderrelation": 8881,
        "normalsubgroup": 8882,
        "containsasnormalsubgroup": 8883,
        "normalsubgroorequalup": 8884,
        "containsasnormalsubgroorequalup": 8885,
        "original": 8886,
        "image": 8887,
        "multimap": 8888,
        "hermitianconjugatematrix": 8889,
        "intercalate": 8890,
        "xor": 8891,
        "nand": 8892,
        "nor": 8893,
        "anglearcright": 8894,
        "triangleright": 8895,
        "logicalandarray": 8896,
        "logicalorarray": 8897,
        "intersectionarray": 8898,
        "unionarray": 8899,
        "diamondoperator": 8900,
        "dotmath": 8901,
        "staroperator": 8902,
        "divisiontimes": 8903,
        "math:bowtie": 8904,
        "normalfacrsemidirectproductleft": 8905,
        "normalfacrsemidirectproductright": 8906,
        "semidirectproductleft": 8907,
        "semidirectproductright": 8908,
        "tildeequalsreversed": 8909,
        "curlylogicalor": 8910,
        "curlylogicaland": 8911,
        "subsetdbl": 8912,
        "sersetdblup": 8913,
        "intersectiondbl": 8914,
        "uniondbl": 8915,
        "pitchfork": 8916,
        "equalandparallel": 8917,
        "lessdot": 8918,
        "greaterdot": 8919,
        "verymuchless": 8920,
        "verymuchgreater": 8921,
        "lessequalorgreater": 8922,
        "greaterequalorless": 8923,
        "equalorless": 8924,
        "equalorgreater": 8925,
        "equalorprecedes": 8926,
        "equalorsucceeds": 8927,
        "doesnotprecedeorequal": 8928,
        "doesnotsucceedorequal": 8929,
        "notsquareimageorequal": 8930,
        "notsquareoriginalorequal": 8931,
        "squareimageornotequal": 8932,
        "squareoriginalornotequal": 8933,
        "lessbutnotequivalent": 8934,
        "greaterbutnotequivalent": 8935,
        "precedesbutnotequivalent": 8936,
        "succeedsbutnotequivalent": 8937,
        "notnormalsubgroup": 8938,
        "doesnotcontainasnormalsubgroup": 8939,
        "notnormalsubgroorequalup": 8940,
        "doesnotcontainasnormalsubgroorequalup": 8941,
        "ellipsisvertical": 8942,
        "ellipsismidhorizontal": 8943,
        "ellipsisdiagonalupright": 8944,
        "ellipsisdiagonaldownright": 8945,
        "elementlonghorizontalstroke": 8946,
        "elementverticalbarhorizontalstroke": 8947,
        "elementsmallverticalbarhorizontalstroke": 8948,
        "elementdotabove": 8949,
        "elementoverbar": 8950,
        "elementoverbarsmall": 8951,
        "elementunderbar": 8952,
        "elementtwoshorizontalstroke": 8953,
        "containslonghorizontalstroke": 8954,
        "containsverticalbarhorizontalstroke": 8955,
        "containssmallverticalbarhorizontalstroke": 8956,
        "containsoverbar": 8957,
        "containsoverbarsmall": 8958,
        "znotationbagmembership": 8959,
        "diametersign": 8960,
        "electricarrow": 8961,
        "house": 8962,
        "arrowheadup": 8963,
        "arrowheaddown": 8964,
        "projective": 8965,
        "perspective": 8966,
        "wavyline": 8967,
        "ceilingleft": 8968,
        "ceilingright": 8969,
        "floorleft": 8970,
        "floorright": 8971,
        "cropbottomright": 8972,
        "cropbottomleft": 8973,
        "croptopright": 8974,
        "croptopleft": 8975,
        "revlogicalnot": 8976,
        "lozengesquare": 8977,
        "arc": 8978,
        "segment": 8979,
        "sector": 8980,
        "telephonerecorder": 8981,
        "positionindicator": 8982,
        "viewdatasquare": 8983,
        "placeofinterestsign": 8984,
        "notsignturned": 8985,
        "watch": 8986,
        "hourglass": 8987,
        "cornertopleft": 8988,
        "cornertopright": 8989,
        "cornerbottomleft": 8990,
        "cornerbottomright": 8991,
        "integraltp": 8992,
        "integralbt": 8993,
        "frown": 8994,
        "smile": 8995,
        "arrowheadtwobarsuphorizontal": 8996,
        "option": 8997,
        "eraseright": 8998,
        "clear": 8999,
        "board": 9000,
        "angleleft": 9001,
        "angleright": 9002,
        "eraseleft": 9003,
        "benzenering": 9004,
        "cylindricity": 9005,
        "allaroundprofile": 9006,
        "symmetry": 9007,
        "totalrunout": 9008,
        "dimensionorigin": 9009,
        "conicaltaper": 9010,
        "slope": 9011,
        "counterbore": 9012,
        "countersink": 9013,
        "beamfunc": 9014,
        "squishquadfunc": 9015,
        "quadequalfunc": 9016,
        "quaddividefunc": 9017,
        "quaddiamondfunc": 9018,
        "quadjotfunc": 9019,
        "quadcirclefunc": 9020,
        "circlestilefunc": 9021,
        "circlejotfunc": 9022,
        "slashbarfunc": 9023,
        "backslashbarfunc": 9024,
        "quadslashfunc": 9025,
        "quadbackslashfunc": 9026,
        "quadlessfunc": 9027,
        "quadgreaterfunc": 9028,
        "vaneleftfunc": 9029,
        "vanerightfunc": 9030,
        "quadarrowleftfunc": 9031,
        "quadarrowrightfunc": 9032,
        "circlebackslashfunc": 9033,
        "tackunderlinedownfunc": 9034,
        "deltastilefunc": 9035,
        "quadcaretdownfunc": 9036,
        "quaddeltafunc": 9037,
        "tackjotdownfunc": 9038,
        "vaneupfunc": 9039,
        "quadarrowupfunc": 9040,
        "tackoverbarupfunc": 9041,
        "delstilefunc": 9042,
        "quadcaretupfunc": 9043,
        "quaddelfunc": 9044,
        "tackjotupfunc": 9045,
        "vanedownfunc": 9046,
        "quadarrowdownfunc": 9047,
        "quoteunderlinefunc": 9048,
        "deltaunderlinefunc": 9049,
        "diamondunderlinefunc": 9050,
        "jotunderlinefunc": 9051,
        "circleunderlinefunc": 9052,
        "shoejotupfunc": 9053,
        "quotequadfunc": 9054,
        "circlestarfunc": 9055,
        "quadcolonfunc": 9056,
        "tackdiaeresisupfunc": 9057,
        "deldiaeresisfunc": 9058,
        "stardiaeresisfunc": 9059,
        "jotdiaeresisfunc": 9060,
        "circlediaeresisfunc": 9061,
        "shoestiledownfunc": 9062,
        "shoestileleftfunc": 9063,
        "tildediaeresisfunc": 9064,
        "diaeresisgreaterfunc": 9065,
        "commabarfunc": 9066,
        "deltildefunc": 9067,
        "zildefunc": 9068,
        "stiletildefunc": 9069,
        "semicolonunderlinefunc": 9070,
        "quadnotequalfunc": 9071,
        "quadquestionfunc": 9072,
        "carettildedownfunc": 9073,
        "carettildeupfunc": 9074,
        "iotafunc": 9075,
        "rhofunc": 9076,
        "omegafunc": 9077,
        "alphaunderlinefunc": 9078,
        "epsilonunderlinefunc": 9079,
        "iotaunderlinefunc": 9080,
        "omegaunderlinefunc": 9081,
        "alphafunc": 9082,
        "notcheckmark": 9083,
        "anglezigzagarrowdownright": 9084,
        "shoulderedopenbox": 9085,
        "misc:bell": 128276,
        "linemiddledotvertical": 9087,
        "insertion": 9088,
        "continuousunderline": 9089,
        "discontinuousunderline": 9090,
        "emphasis": 9091,
        "composition": 9092,
        "centrelineverticalsquarewhite": 9093,
        "enter": 9094,
        "alternative": 9095,
        "helm": 9096,
        "circledbarnotchhorizontal": 9097,
        "circledtriangledown": 9098,
        "brokencirclenorthwestarrow": 9099,
        "undo": 9100,
        "monostable": 9101,
        "hysteresis": 9102,
        "htypeopencircuit": 9103,
        "ltypeopencircuit": 9104,
        "passivedown": 9105,
        "outputpassiveup": 9106,
        "directcurrentformtwo": 9107,
        "softwarefunction": 9108,
        "quadfunc": 9109,
        "misc:decimalseparator": 9110,
        "previouspage": 9111,
        "nextpage": 9112,
        "printscreen": 9113,
        "clearscreen": 9114,
        "parenhookupleft": 9115,
        "parenextensionleft": 9116,
        "parenlowerhookleft": 9117,
        "parenhookupright": 9118,
        "parenextensionright": 9119,
        "parenlowerhookright": 9120,
        "bracketcornerupleftsquare": 9121,
        "bracketextensionleftsquare": 9122,
        "bracketlowercornerleftsquare": 9123,
        "bracketcorneruprightsquare": 9124,
        "bracketextensionrightsquare": 9125,
        "bracketlowercornerrightsquare": 9126,
        "brackethookupleftcurly": 9127,
        "bracketmiddlepieceleftcurly": 9128,
        "bracketlowerhookleftcurly": 9129,
        "bracketextensioncurly": 9130,
        "brackethookuprightcurly": 9131,
        "bracketmiddlepiecerightcurly": 9132,
        "bracketlowerhookrightcurly": 9133,
        "integralextension": 9134,
        "lineextensionhorizontal": 9135,
        "bracketsectionupleftlowerrightcurly": 9136,
        "bracketsectionuprightlowerleftcurly": 9137,
        "summationtop": 9138,
        "summationbottom": 9139,
        "brackettopsquare": 9140,
        "bracketbottomsquare": 9141,
        "bracketoverbrackettopbottomsquare": 9142,
        "radicalbottom": 9143,
        "boxlineverticalleft": 9144,
        "boxlineverticalright": 9145,
        "scanonehorizontal": 9146,
        "scanthreehorizontal": 9147,
        "scansevenhorizontal": 9148,
        "scanninehorizontal": 9149,
        "dentistrytopverticalright": 9150,
        "dentistrybottomverticalright": 9151,
        "dentistrycirclevertical": 9152,
        "dentistrycircledownhorizontal": 9153,
        "dentistrycircleuphorizontal": 9154,
        "dentistrytrianglevertical": 9155,
        "dentistrytriangledownhorizontal": 9156,
        "dentistrytriangleuphorizontal": 9157,
        "dentistrywavevertical": 9158,
        "dentistrywavedownhorizontal": 9159,
        "dentistrywaveuphorizontal": 9160,
        "dentistrydownhorizontal": 9161,
        "dentistryuphorizontal": 9162,
        "dentistrytopverticalleft": 9163,
        "dentistrybottomverticalleft": 9164,
        "footsquare": 9165,
        "return": 9166,
        "eject": 9167,
        "lineextensionvertical": 9168,
        "brevemetrical": 9169,
        "longovershortmetrical": 9170,
        "shortoverlongmetrical": 9171,
        "longovertwoshortsmetrical": 9172,
        "twoshortsoverlongmetrical": 9173,
        "twoshortsjoinedmetrical": 9174,
        "trisememetrical": 9175,
        "tetrasememetrical": 9176,
        "pentasememetrical": 9177,
        "earthground": 9178,
        "fuse": 9179,
        "parentop": 9180,
        "parenbottom": 9181,
        "brackettopcurly": 9182,
        "bracketbottomcurly": 9183,
        "bracketshelltop": 9184,
        "bracketshellbottom": 9185,
        "trapeziumwhite": 9186,
        "benzeneringcircle": 9187,
        "straightness": 9188,
        "flatness": 9189,
        "accurrent": 9190,
        "electricalintersection": 9191,
        "decimalexponent": 9192,
        "blackpointingdoubletriangleright": 9193,
        "blackpointingdoubletriangleleft": 9194,
        "blackpointingdoubletriangleup": 9195,
        "blackpointingdoubletriangledown": 9196,
        "blackpointingdoubletrianglebarverticalright": 9197,
        "blackpointingdoubletrianglebarverticalleft": 9198,
        "blackpointingtriangledoublebarverticalright": 9199,
        "alarmclock": 9200,
        "swatchtop": 9201,
        "timerclock": 9202,
        "hourglassflowings": 9203,
        "blackmediumpointingtriangleleft": 9204,
        "blackmediumpointingtriangleright": 9205,
        "blackmediumpointingtriangleup": 9206,
        "blackmediumpointingtriangledown": 9207,
        "doublebarvertical": 9208,
        "blackforstopsquare": 9209,
        "blackcircleforrecord": 9210,
        "power": 9211,
        "poweronoff": 9212,
        "poweron": 9213,
        "powersleep": 9214,
        "observereye": 9215,
        "cntr:null": 9216,
        "cntr:startofheading": 9217,
        "cntr:startoftext": 9218,
        "cntr:endoftext": 9219,
        "cntr:endoftransmission": 9220,
        "cntr:enquiry": 9221,
        "cntr:acknowledge": 9222,
        "cntr:bell": 9223,
        "cntr:backspace": 9224,
        "cntr:horizontaltab": 9225,
        "cntr:linefeed": 9226,
        "cntr:verticaltab": 9227,
        "cntr:formfeed": 9228,
        "cntr:carriagereturn": 9229,
        "cntr:shiftout": 9230,
        "cntr:shiftin": 9231,
        "cntr:datalinkescape": 9232,
        "cntr:devicecontrolone": 9233,
        "cntr:devicecontroltwo": 9234,
        "cntr:devicecontrolthree": 9235,
        "cntr:devicecontrolfour": 9236,
        "cntr:negativeacknowledge": 9237,
        "cntr:synchronousidle": 9238,
        "cntr:endoftransmissionblock": 9239,
        "cntr:cancel": 9240,
        "cntr:endofmedium": 9241,
        "cntr:substitute": 9242,
        "cntr:escape": 9243,
        "cntr:fileseparator": 9244,
        "cntr:groupseparator": 9245,
        "cntr:recordseparator": 9246,
        "cntr:unitseparator": 9247,
        "cntr:space": 9248,
        "cntr:delete": 9249,
        "cntr:blank": 9250,
        "cntr:openbox": 9251,
        "cntr:newline": 9252,
        "cntr:deleteformtwo": 9253,
        "cntr:substituteformtwo": 9254,
        "hook": 9280,
        "ocr:chair": 9281,
        "fork": 9282,
        "invertedfork": 9283,
        "beltbuckle": 9284,
        "ocr:bowtie": 9285,
        "branchbankidentification": 9286,
        "amountofcheck": 9287,
        "ocr:dash": 9288,
        "customeraccountnumber": 9289,
        "backslashdbl": 9290,
        "onecircle": 9312,
        "twocircle": 9313,
        "threecircle": 9314,
        "fourcircle": 9315,
        "fivecircle": 9316,
        "sixcircle": 9317,
        "sevencircle": 9318,
        "eightcircle": 9319,
        "ninecircle": 9320,
        "tencircle": 9321,
        "elevencircle": 9322,
        "twelvecircle": 9323,
        "thirteencircle": 9324,
        "fourteencircle": 9325,
        "fifteencircle": 9326,
        "sixteencircle": 9327,
        "seventeencircle": 9328,
        "eighteencircle": 9329,
        "nineteencircle": 9330,
        "twentycircle": 9331,
        "oneparenthesized": 9332,
        "twoparenthesized": 9333,
        "threeparenthesized": 9334,
        "fourparenthesized": 9335,
        "fiveparenthesized": 9336,
        "sixparenthesized": 9337,
        "sevenparenthesized": 9338,
        "eightparenthesized": 9339,
        "nineparenthesized": 9340,
        "tenparenthesized": 9341,
        "elevenparenthesized": 9342,
        "twelveparenthesized": 9343,
        "thirteenparenthesized": 9344,
        "fourteenparenthesized": 9345,
        "fifteenparenthesized": 9346,
        "sixteenparenthesized": 9347,
        "seventeenparenthesized": 9348,
        "eighteenparenthesized": 9349,
        "nineteenparenthesized": 9350,
        "twentyparenthesized": 9351,
        "oneperiod": 9352,
        "twoperiod": 9353,
        "threeperiod": 9354,
        "fourperiod": 9355,
        "fiveperiod": 9356,
        "sixperiod": 9357,
        "sevenperiod": 9358,
        "eightperiod": 9359,
        "nineperiod": 9360,
        "tenperiod": 9361,
        "elevenperiod": 9362,
        "twelveperiod": 9363,
        "thirteenperiod": 9364,
        "fourteenperiod": 9365,
        "fifteenperiod": 9366,
        "sixteenperiod": 9367,
        "seventeenperiod": 9368,
        "eighteenperiod": 9369,
        "nineteenperiod": 9370,
        "twentyperiod": 9371,
        "aparenthesized": 9372,
        "bparenthesized": 9373,
        "cparenthesized": 9374,
        "dparenthesized": 9375,
        "eparenthesized": 9376,
        "fparenthesized": 9377,
        "gparenthesized": 9378,
        "hparenthesized": 9379,
        "iparenthesized": 9380,
        "jparenthesized": 9381,
        "kparenthesized": 9382,
        "lparenthesized": 9383,
        "mparenthesized": 9384,
        "nparenthesized": 9385,
        "oparenthesized": 9386,
        "pparenthesized": 9387,
        "qparenthesized": 9388,
        "rparenthesized": 9389,
        "sparenthesized": 9390,
        "tparenthesized": 9391,
        "uparenthesized": 9392,
        "vparenthesized": 9393,
        "wparenthesized": 9394,
        "xparenthesized": 9395,
        "yparenthesized": 9396,
        "zparenthesized": 9397,
        "Acircle": 9398,
        "Bcircle": 9399,
        "Ccircle": 9400,
        "Dcircle": 9401,
        "Ecircle": 9402,
        "Fcircle": 9403,
        "Gcircle": 9404,
        "Hcircle": 9405,
        "Icircle": 9406,
        "Jcircle": 9407,
        "Kcircle": 9408,
        "Lcircle": 9409,
        "Mcircle": 9410,
        "Ncircle": 9411,
        "Ocircle": 9412,
        "Pcircle": 9413,
        "Qcircle": 9414,
        "Rcircle": 9415,
        "Scircle": 9416,
        "Tcircle": 9417,
        "Ucircle": 9418,
        "Vcircle": 9419,
        "Wcircle": 9420,
        "Xcircle": 9421,
        "Ycircle": 9422,
        "Zcircle": 9423,
        "acircle": 9424,
        "bcircle": 9425,
        "ccircle": 9426,
        "dcircle": 9427,
        "ecircle": 9428,
        "fcircle": 9429,
        "gcircle": 9430,
        "hcircle": 9431,
        "icircle": 9432,
        "jcircle": 9433,
        "kcircle": 9434,
        "lcircle": 9435,
        "mcircle": 9436,
        "ncircle": 9437,
        "ocircle": 9438,
        "pcircle": 9439,
        "qcircle": 9440,
        "rcircle": 9441,
        "scircle": 9442,
        "tcircle": 9443,
        "ucircle": 9444,
        "vcircle": 9445,
        "wcircle": 9446,
        "xcircle": 9447,
        "ycircle": 9448,
        "zcircle": 9449,
        "zerocircle": 9450,
        "elevencircleblack": 9451,
        "twelvecircleblack": 9452,
        "thirteencircleblack": 9453,
        "fourteencircleblack": 9454,
        "fifteencircleblack": 9455,
        "sixteencircleblack": 9456,
        "seventeencircleblack": 9457,
        "eighteencircleblack": 9458,
        "nineteencircleblack": 9459,
        "twentycircleblack": 9460,
        "onecircledbl": 9461,
        "twocircledbl": 9462,
        "threecircledbl": 9463,
        "fourcircledbl": 9464,
        "fivecircledbl": 9465,
        "sixcircledbl": 9466,
        "sevencircledbl": 9467,
        "eightcircledbl": 9468,
        "ninecircledbl": 9469,
        "tencircledbl": 9470,
        "zerocircleblack": 9471,
        "lighthorz": 9472,
        "heavyhorz": 9473,
        "lightvert": 9474,
        "heavyvert": 9475,
        "lighttrpldashhorz": 9476,
        "heavytrpldashhorz": 9477,
        "lighttrpldashvert": 9478,
        "heavytrpldashvert": 9479,
        "lightquaddashhorz": 9480,
        "heavyquaddashhorz": 9481,
        "lightquaddashvert": 9482,
        "heavyquaddashvert": 9483,
        "lightdnright": 9484,
        "dnlightrightheavy": 9485,
        "dnheavyrightlight": 9486,
        "heavydnright": 9487,
        "lightdnleft": 9488,
        "dnlightleftheavy": 9489,
        "dnheavyleftlight": 9490,
        "heavydnleft": 9491,
        "lightupright": 9492,
        "uplightrightheavy": 9493,
        "upheavyrightlight": 9494,
        "heavyupright": 9495,
        "lightupleft": 9496,
        "uplightleftheavy": 9497,
        "upheavyleftlight": 9498,
        "heavyupleft": 9499,
        "lightvertright": 9500,
        "vertlightrightheavy": 9501,
        "upheavyrightdnlight": 9502,
        "dnheavyrightuplight": 9503,
        "vertheavyrightlight": 9504,
        "dnlightrightupheavy": 9505,
        "uplightrightdnheavy": 9506,
        "heavyvertright": 9507,
        "lightvertleft": 9508,
        "vertlightleftheavy": 9509,
        "upheavyleftdnlight": 9510,
        "dnheavyleftuplight": 9511,
        "vertheavyleftlight": 9512,
        "dnlightleftupheavy": 9513,
        "uplightleftdnheavy": 9514,
        "heavyvertleft": 9515,
        "lightdnhorz": 9516,
        "leftheavyrightdnlight": 9517,
        "rightheavyleftdnlight": 9518,
        "dnlighthorzheavy": 9519,
        "dnheavyhorzlight": 9520,
        "rightlightleftdnheavy": 9521,
        "leftlightrightdnheavy": 9522,
        "heavydnhorz": 9523,
        "lightuphorz": 9524,
        "leftheavyrightuplight": 9525,
        "rightheavyleftuplight": 9526,
        "uplighthorzheavy": 9527,
        "upheavyhorzlight": 9528,
        "rightlightleftupheavy": 9529,
        "leftlightrightupheavy": 9530,
        "heavyuphorz": 9531,
        "lightverthorz": 9532,
        "leftheavyrightvertlight": 9533,
        "rightheavyleftvertlight": 9534,
        "vertlighthorzheavy": 9535,
        "upheavydnhorzlight": 9536,
        "dnheavyuphorzlight": 9537,
        "vertheavyhorzlight": 9538,
        "leftupheavyrightdnlight": 9539,
        "rightupheavyleftdnlight": 9540,
        "leftdnheavyrightuplight": 9541,
        "rightdnheavyleftuplight": 9542,
        "dnlightuphorzheavy": 9543,
        "uplightdnhorzheavy": 9544,
        "rightlightleftvertheavy": 9545,
        "leftlightrightvertheavy": 9546,
        "heavyverthorz": 9547,
        "lightdbldashhorz": 9548,
        "heavydbldashhorz": 9549,
        "lightdbldashvert": 9550,
        "heavydbldashvert": 9551,
        "dblhorz": 9552,
        "dblvert": 9553,
        "dnsngrightdbl": 9554,
        "dndblrightsng": 9555,
        "dbldnright": 9556,
        "dnsngleftdbl": 9557,
        "dndblleftsng": 9558,
        "dbldnleft": 9559,
        "upsngrightdbl": 9560,
        "updblrightsng": 9561,
        "dblupright": 9562,
        "upsngleftdbl": 9563,
        "updblleftsng": 9564,
        "dblupleft": 9565,
        "vertsngrightdbl": 9566,
        "vertdblrightsng": 9567,
        "dblvertright": 9568,
        "vertsngleftdbl": 9569,
        "vertdblleftsng": 9570,
        "dblvertleft": 9571,
        "dnsnghorzdbl": 9572,
        "dndblhorzsng": 9573,
        "dbldnhorz": 9574,
        "upsnghorzdbl": 9575,
        "updblhorzsng": 9576,
        "dbluphorz": 9577,
        "vertsnghorzdbl": 9578,
        "vertdblhorzsng": 9579,
        "dblverthorz": 9580,
        "lightarcdnright": 9581,
        "lightarcdnleft": 9582,
        "lightarcupleft": 9583,
        "lightarcupright": 9584,
        "lightdiaguprightdnleft": 9585,
        "lightdiagupleftdnright": 9586,
        "lightdiagcross": 9587,
        "lightleft": 9588,
        "lightup": 9589,
        "lightright": 9590,
        "lightdn": 9591,
        "heavyleft": 9592,
        "heavyup": 9593,
        "heavyright": 9594,
        "heavydn": 9595,
        "lightleftheavyright": 9596,
        "lightupheavydn": 9597,
        "heavyleftlightright": 9598,
        "heavyuplightdn": 9599,
        "upperHalfBlock": 9600,
        "lowerOneEighthBlock": 9601,
        "lowerOneQuarterBlock": 9602,
        "lowerThreeEighthsBlock": 9603,
        "lowerHalfBlock": 9604,
        "lowerFiveEighthsBlock": 9605,
        "lowerThreeQuartersBlock": 9606,
        "lowerSevenEighthsBlock": 9607,
        "fullBlock": 9608,
        "leftSevenEighthsBlock": 9609,
        "leftThreeQuartersBlock": 9610,
        "leftFiveEighthsBlock": 9611,
        "leftHalfBlock": 9612,
        "leftThreeEighthsBlock": 9613,
        "leftOneQuarterBlock": 9614,
        "leftOneEighthBlock": 9615,
        "rightHalfBlock": 9616,
        "lightShade": 9617,
        "mediumShade": 9618,
        "darkShade": 9619,
        "upperOneEighthBlock": 9620,
        "rightOneEighthBlock": 9621,
        "quadrantLowerLeft": 9622,
        "quadrantLowerRight": 9623,
        "quadrantUpperLeft": 9624,
        "quadrantUpperLeftAndLowerLeftAndLowerRight": 9625,
        "quadrantUpperLeftAndLowerRight": 9626,
        "quadrantUpperLeftAndUpperRightAndLowerLeft": 9627,
        "quadrantUpperLeftAndUpperRightAndLowerRight": 9628,
        "quadrantUpperRight": 9629,
        "quadrantUpperRightAndLowerLeft": 9630,
        "quadrantUpperRightAndLowerLeftAndLowerRight": 9631,
        "squareblack": 9632,
        "squarewhite": 9633,
        "squarewhiteround": 9634,
        "squarewhitewithsquaresmallblack": 9635,
        "squarehorizontalfill": 9636,
        "squareverticalfill": 9637,
        "squareorthogonalcrosshatchfill": 9638,
        "squareupperlefttolowerrightfill": 9639,
        "squareupperrighttolowerleftfill": 9640,
        "squarediagonalcrosshatchfill": 9641,
        "squaresmallblack": 9642,
        "squaresmallwhite": 9643,
        "rectangleblack": 9644,
        "rectanglewhite": 9645,
        "rectangleverticalblack": 9646,
        "rectangleverticalwhite": 9647,
        "parallelogramblack": 9648,
        "parallelogramwhite": 9649,
        "triangleupblack": 9650,
        "triangleupwhite": 9651,
        "triangleupsmallblack": 9652,
        "triangleupsmallwhite": 9653,
        "trianglerightblack": 9654,
        "trianglerightwhite": 9655,
        "trianglerightsmallblack": 9656,
        "trianglerightsmallwhite": 9657,
        "pointerrightblack": 9658,
        "pointerrightwhite": 9659,
        "triangledownblack": 9660,
        "triangledownwhite": 9661,
        "triangledownsmallblack": 9662,
        "triangledownsmallwhite": 9663,
        "triangleleftblack": 9664,
        "triangleleftwhite": 9665,
        "triangleleftsmallblack": 9666,
        "triangleleftsmallwhite": 9667,
        "pointerleftblack": 9668,
        "pointerleftwhite": 9669,
        "gmtr:diamondblack": 9670,
        "gmtr:diamondwhite": 9671,
        "diamondwhitewithdiamondsmallblack": 9672,
        "fisheye": 9673,
        "lozenge": 9674,
        "circlewhite": 9675,
        "circledotted": 9676,
        "circleverticalfill": 9677,
        "bullseye": 9678,
        "circleblack": 9679,
        "circlehalfleftblack": 9680,
        "circlehalfrightblack": 9681,
        "circlelowerhalfblack": 9682,
        "circleupperhalfblack": 9683,
        "circleupperquadrantrightblack": 9684,
        "circleallbutupperquadrantleftblack": 9685,
        "halfcircleleftblack": 9686,
        "halfcirclerightblack": 9687,
        "bulletinverse": 9688,
        "circleinversewhite": 9689,
        "upperhalfcircleinversewhite": 9690,
        "lowerhalfcircleinversewhite": 9691,
        "upperquadrantcirculararcleft": 9692,
        "upperquadrantcirculararcright": 9693,
        "lowerquadrantcirculararcright": 9694,
        "lowerquadrantcirculararcleft": 9695,
        "upperhalfcircle": 9696,
        "lowerhalfcircle": 9697,
        "lowertrianglerightblack": 9698,
        "lowertriangleleftblack": 9699,
        "uppertriangleleftblack": 9700,
        "uppertrianglerightblack": 9701,
        "openbullet": 9702,
        "squarehalfleftblack": 9703,
        "squarehalfrightblack": 9704,
        "squareupperdiagonalhalfleftblack": 9705,
        "squarelowerdiagonalhalfrightblack": 9706,
        "squarewhitebisectinglinevertical": 9707,
        "triangledotupwhite": 9708,
        "trianglehalfupleftblack": 9709,
        "trianglehalfuprightblack": 9710,
        "largecircle": 9711,
        "squarewhiteupperquadrantleft": 9712,
        "squarewhitelowerquadrantleft": 9713,
        "squarewhitelowerquadrantright": 9714,
        "squarewhiteupperquadrantright": 9715,
        "circleupperquadrantleftwhite": 9716,
        "circlelowerquadrantleftwhite": 9717,
        "circlelowerquadrantrightwhite": 9718,
        "circleupperquadrantrightwhite": 9719,
        "uppertriangleleft": 9720,
        "uppertriangleright": 9721,
        "lowertriangleleft": 9722,
        "squaremediumwhite": 9723,
        "squaremediumblack": 9724,
        "squaresmallmediumwhite": 9725,
        "squaresmallmediumblack": 9726,
        "lowertriangleright": 9727,
        "sunraysblack": 9728,
        "cloud": 9729,
        "umbrella": 9730,
        "snowman": 9731,
        "comet": 9732,
        "starblack": 9733,
        "starwhite": 9734,
        "lightning": 9735,
        "thunderstorm": 9736,
        "sun": 9737,
        "nodeascending": 9738,
        "nodedescending": 9739,
        "conjunction": 9740,
        "opposition": 9741,
        "telephoneblack": 9742,
        "telephonewhite": 9743,
        "checkbox": 9744,
        "checkboxchecked": 9745,
        "checkboxx": 9746,
        "saltire": 9747,
        "umbrellaraindrops": 9748,
        "hotbeverage": 9749,
        "shogipiecewhite": 9750,
        "shogipieceblack": 9751,
        "shamrock": 9752,
        "floralheartbulletreversedrotated": 9753,
        "pointingindexleftblack": 9754,
        "pointingindexrightblack": 9755,
        "pointingindexleftwhite": 9756,
        "pointingindexupwhite": 9757,
        "pointingindexrightwhite": 9758,
        "pointingindexdownwhite": 9759,
        "skullcrossbones": 9760,
        "caution": 9761,
        "radioactive": 9762,
        "biohazard": 9763,
        "caduceus": 9764,
        "ankh": 9765,
        "orthodoxcross": 9766,
        "chirho": 9767,
        "crossoflorraine": 9768,
        "crossofjerusalem": 9769,
        "starcrescent": 9770,
        "farsi": 9771,
        "adishakti": 9772,
        "hammersickle": 9773,
        "peace": 9774,
        "yinyang": 9775,
        "trigramheaven": 9776,
        "trigramlake": 9777,
        "trigramfire": 9778,
        "trigramthunder": 9779,
        "trigramwind": 9780,
        "trigramwater": 9781,
        "trigrammountain": 9782,
        "trigramearth": 9783,
        "wheelofdharma": 9784,
        "frowningfacewhite": 9785,
        "smilingfacewhite": 9786,
        "smilingfaceblack": 9787,
        "sunrayswhite": 9788,
        "firstquartermoon": 9789,
        "lastquartermoon": 9790,
        "mercury": 9791,
        "female": 9792,
        "earth": 9793,
        "male": 9794,
        "jiterup": 9795,
        "saturn": 9796,
        "uranus": 9797,
        "neptune": 9798,
        "pluto": 9799,
        "aries": 9800,
        "taurus": 9801,
        "gemini": 9802,
        "cancer": 9803,
        "leo": 9804,
        "virgo": 9805,
        "libra": 9806,
        "scorpius": 9807,
        "sagittarius": 9808,
        "capricorn": 9809,
        "aquarius": 9810,
        "pisces": 9811,
        "kingwhite": 9812,
        "queenwhite": 9813,
        "rookwhite": 9814,
        "bishopwhite": 9815,
        "knightwhite": 9816,
        "pawnwhite": 9817,
        "kingblack": 9818,
        "queenblack": 9819,
        "rookblack": 9820,
        "bishopblack": 9821,
        "knightblack": 9822,
        "pawnblack": 9823,
        "spadeblack": 9824,
        "heartwhite": 9825,
        "misc:diamondwhite": 9826,
        "clubblack": 9827,
        "spadewhite": 9828,
        "heartblack": 9829,
        "misc:diamondblack": 9830,
        "clubwhite": 9831,
        "hotsprings": 9832,
        "quarternote": 9833,
        "eighthnote": 9834,
        "beamedeighthnotes": 9835,
        "beamedsixteenthnotes": 9836,
        "musicflat": 9837,
        "musicnatural": 9838,
        "musicsharp": 9839,
        "westsyriaccross": 9840,
        "eastsyriaccross": 9841,
        "recycleuniversal": 9842,
        "recycleoneplastics": 9843,
        "recycletwoplastics": 9844,
        "recyclethreeplastics": 9845,
        "recyclefourplastics": 9846,
        "recyclefiveplastics": 9847,
        "recyclesixplastics": 9848,
        "recyclesevenplastics": 9849,
        "recyclegeneric": 9850,
        "recycleuniversalblack": 9851,
        "recycledpaper": 9852,
        "recyclepartiallypaper": 9853,
        "permanentpaper": 9854,
        "wheelchair": 9855,
        "dieone": 9856,
        "dietwo": 9857,
        "diethree": 9858,
        "diefour": 9859,
        "diefive": 9860,
        "diesix": 9861,
        "circledotrightwhite": 9862,
        "circletwodotswhite": 9863,
        "circlewhitedotrightblack": 9864,
        "circletwodotsblackwhite": 9865,
        "monogramyang": 9866,
        "monogramyin": 9867,
        "digramgreateryang": 9868,
        "digramlesseryin": 9869,
        "digramlesseryang": 9870,
        "digramgreateryin": 9871,
        "flagwhite": 9872,
        "flagblack": 9873,
        "hammerpick": 9874,
        "anchor": 9875,
        "crossedswords": 9876,
        "staffofaesculapius": 9877,
        "scales": 9878,
        "alembic": 9879,
        "flower": 9880,
        "gear": 9881,
        "staffofhermes": 9882,
        "atom": 9883,
        "fleurdelis": 9884,
        "staroutlinedwhite": 9885,
        "threelinesconvergingright": 9886,
        "threelinesconvergingleft": 9887,
        "warning": 9888,
        "highvoltage": 9889,
        "dfemaledbl": 9890,
        "dmaledbl": 9891,
        "interlockedfemalemale": 9892,
        "malefemale": 9893,
        "malestroke": 9894,
        "malestrokemalefemale": 9895,
        "verticalmalestroke": 9896,
        "horizontalmalestroke": 9897,
        "mediumcirclewhite": 9898,
        "mediumcircleblack": 9899,
        "mediumsmallcirclewhite": 9900,
        "marriage": 9901,
        "divorce": 9902,
        "unmarriedpartnership": 9903,
        "coffin": 9904,
        "funeralurn": 9905,
        "neuter": 9906,
        "ceres": 9907,
        "pallas": 9908,
        "juno": 9909,
        "vesta": 9910,
        "chiron": 9911,
        "moonlilithblack": 9912,
        "sextile": 9913,
        "semisextile": 9914,
        "quincunx": 9915,
        "sesquiquadrate": 9916,
        "soccerball": 9917,
        "baseball": 9918,
        "squaredkey": 9919,
        "draughtsmanwhite": 9920,
        "draughtskingwhite": 9921,
        "draughtsmanblack": 9922,
        "draughtskingblack": 9923,
        "snowmanoutsnow": 9924,
        "sunbehindcloud": 9925,
        "rain": 9926,
        "snowmanblack": 9927,
        "thundercloudrain": 9928,
        "turnedshogipiecewhite": 9929,
        "turnedshogipieceblack": 9930,
        "diamondinsquarewhite": 9931,
        "crossinglanes": 9932,
        "disabledcar": 9933,
        "ophiuchus": 9934,
        "pick": 9935,
        "carsliding": 9936,
        "helmetcrosswhite": 9937,
        "circledcrossinglanes": 9938,
        "chains": 9939,
        "noentry": 9940,
        "alternateonewayleftwaytraffic": 9941,
        "twowayleftwaytrafficblack": 9942,
        "twowayleftwaytrafficwhite": 9943,
        "lanemergeleftblack": 9944,
        "lanemergeleftwhite": 9945,
        "driveslow": 9946,
        "pointingtriangledownheavywhite": 9947,
        "closedentryleft": 9948,
        "squaredsaltire": 9949,
        "fallingdiagonalincircleinsquareblackwhite": 9950,
        "truckblack": 9951,
        "restrictedentryoneleft": 9952,
        "restrictedentrytwoleft": 9953,
        "astronomicaluranus": 9954,
        "circlestroketwodotsaboveheavy": 9955,
        "pentagram": 9956,
        "hedinterlacedpentagramright": 9957,
        "hedinterlacedpentagramleft": 9958,
        "invertedpentagram": 9959,
        "crossonshieldblack": 9960,
        "shintoshrine": 9961,
        "church": 9962,
        "castle": 9963,
        "historicsite": 9964,
        "gearouthub": 9965,
        "gearhles": 9966,
        "maplighthouse": 9967,
        "mountain": 9968,
        "umbrellaonground": 9969,
        "fountain": 9970,
        "flaginhole": 9971,
        "ferry": 9972,
        "sailboat": 9973,
        "squarefourcorners": 9974,
        "skier": 9975,
        "iceskate": 9976,
        "personball": 9977,
        "tent": 9978,
        "japanesebank": 9979,
        "headstonegraveyard": 9980,
        "fuelpump": 9981,
        "consquareupblack": 9982,
        "flaghorizontalmiddlestripeblackwhite": 9983,
        "safetyscissorsblack": 9984,
        "scissorsupperblade": 9985,
        "scissorsblack": 9986,
        "scissorslowerblade": 9987,
        "scissorswhite": 9988,
        "checkwhiteheavy": 9989,
        "telephonelocationsign": 9990,
        "tapedrive": 9991,
        "airplane": 9992,
        "envelope": 9993,
        "raisedfist": 9994,
        "raisedh": 9995,
        "hvictory": 9996,
        "hwriting": 9997,
        "pencillowerright": 9998,
        "pencil": 9999,
        "pencilupperright": 10000,
        "nibwhite": 10001,
        "nibblack": 10002,
        "check": 10003,
        "checkheavy": 10004,
        "multiplicationx": 10005,
        "multiplicationxheavy": 10006,
        "ballotx": 10007,
        "ballotxheavy": 10008,
        "greekcrossoutlined": 10009,
        "greekcrossheavy": 10010,
        "crosscentreopen": 10011,
        "crosscentreopenheavy": 10012,
        "latincross": 10013,
        "latincrossshadowedwhite": 10014,
        "latincrossoutlined": 10015,
        "maltesecross": 10016,
        "starofdavid": 10017,
        "asteriskteardropfour": 10018,
        "asteriskballoonfour": 10019,
        "asteriskballoonheavyfour": 10020,
        "asteriskclubfour": 10021,
        "starpointedblackfour": 10022,
        "starpointedwhitefour": 10023,
        "sparkles": 10024,
        "staroutlinedstresswhite": 10025,
        "starcircledwhite": 10026,
        "starcentreopenblack": 10027,
        "starcentreblackwhite": 10028,
        "staroutlinedblack": 10029,
        "staroutlinedblackheavy": 10030,
        "starpinwheel": 10031,
        "starshadowedwhite": 10032,
        "asteriskheavy": 10033,
        "asteriskcentreopen": 10034,
        "spokedasteriskeight": 10035,
        "starpointedblackeight": 10036,
        "starpointedpinwheeleight": 10037,
        "starpointedblacksix": 10038,
        "compasstarpointedblackeight": 10039,
        "compasstarpointedblackheavyeight": 10040,
        "starpointedblacktwelve": 10041,
        "asteriskpointedsixteen": 10042,
        "asteriskteardrop": 10043,
        "asteriskteardropcentreopen": 10044,
        "asteriskteardropheavy": 10045,
        "florettepetalledblackwhitesix": 10046,
        "floretteblack": 10047,
        "florettewhite": 10048,
        "floretteoutlinedpetalledblackeight": 10049,
        "starcentreopenpointedcircledeight": 10050,
        "asteriskteardroppinwheelheavy": 10051,
        "snowflake": 10052,
        "snowflaketight": 10053,
        "chevronsnowflakeheavy": 10054,
        "sparkle": 10055,
        "sparkleheavy": 10056,
        "asteriskballoon": 10057,
        "asteriskteardroppropellereight": 10058,
        "asteriskteardroppropellerheavyeight": 10059,
        "cross": 10060,
        "circleshadowedwhite": 10061,
        "squaredcrossnegative": 10062,
        "squareshadowlowerrightwhite": 10063,
        "squareshadowupperrightwhite": 10064,
        "squarelowerrightshadowedwhite": 10065,
        "squareupperrightshadowedwhite": 10066,
        "questionblackornament": 10067,
        "questionwhiteornament": 10068,
        "exclamationwhiteornament": 10069,
        "diamondminusxblackwhite": 10070,
        "exclamationheavy": 10071,
        "verticalbarlight": 10072,
        "verticalbarmedium": 10073,
        "verticalbarheavy": 10074,
        "commaheavyturnedornament": 10075,
        "commaheavyornament": 10076,
        "commaheavydoubleturnedornament": 10077,
        "commaheavydoubleornament": 10078,
        "lowcommaheavyornament": 10079,
        "lowcommaheavydoubleornament": 10080,
        "curvedstemparagraphsignornament": 10081,
        "exclamationheavyornament": 10082,
        "heartexclamationheavyornament": 10083,
        "heartblackheavy": 10084,
        "heartbulletrotatedblackheavy": 10085,
        "floralheart": 10086,
        "floralheartbulletrotated": 10087,
        "parenthesisleftmediumornament": 10088,
        "parenthesisrightmediumornament": 10089,
        "parenthesisleftflattenedmediumornament": 10090,
        "parenthesisrightflattenedmediumornament": 10091,
        "bracketleftpointedanglemediumornament": 10092,
        "bracketrightpointedanglemediumornament": 10093,
        "quotationleftpointedangleheavyornament": 10094,
        "quotationrightpointedangleheavyornament": 10095,
        "bracketleftpointedangleheavyornament": 10096,
        "bracketrightpointedangleheavyornament": 10097,
        "bracketshellleftlightornament": 10098,
        "bracketshellrightlightornament": 10099,
        "curlybracketleftmediumornament": 10100,
        "curlybracketrightmediumornament": 10101,
        "onenegativecircled": 10102,
        "twonegativecircled": 10103,
        "threenegativecircled": 10104,
        "fournegativecircled": 10105,
        "fivenegativecircled": 10106,
        "sixnegativecircled": 10107,
        "sevennegativecircled": 10108,
        "eightnegativecircled": 10109,
        "ninenegativecircled": 10110,
        "tennegativecircled": 10111,
        "onesanscircled": 10112,
        "twosanscircled": 10113,
        "threesanscircled": 10114,
        "foursanscircled": 10115,
        "fivesanscircled": 10116,
        "sixsanscircled": 10117,
        "sevensanscircled": 10118,
        "eightsanscircled": 10119,
        "ninesanscircled": 10120,
        "tensanscircled": 10121,
        "onesansnegativecircled": 10122,
        "twosansnegativecircled": 10123,
        "threesansnegativecircled": 10124,
        "foursansnegativecircled": 10125,
        "fivesansnegativecircled": 10126,
        "sixsansnegativecircled": 10127,
        "sevensansnegativecircled": 10128,
        "eightsansnegativecircled": 10129,
        "ninesansnegativecircled": 10130,
        "tensansnegativecircled": 10131,
        "arrowrightwideheavy": 10132,
        "plussignheavy": 10133,
        "minussignheavy": 10134,
        "divisionsignheavy": 10135,
        "arrowheavySE": 10136,
        "arrowrightheavy": 10137,
        "arrowheavyNE": 10138,
        "arrowrightpointed": 10139,
        "arrowrightroundheavy": 10140,
        "arrowrighttriangle": 10141,
        "arrowrighttriangleheavy": 10142,
        "arrowrighttriangledashed": 10143,
        "arrowrighttriangledashedheavy": 10144,
        "arrowrightblack": 10145,
        "arrowheadrightthreeDtoplight": 10146,
        "arrowheadrightthreeDbottomlight": 10147,
        "arrowheadrightblack": 10148,
        "arrowrightcurvedownblackheavy": 10149,
        "arrowrightcurveupblackheavy": 10150,
        "arrowrightsquatblack": 10151,
        "arrowrightpointedblackheavy": 10152,
        "arrowrightrightshadedwhite": 10153,
        "arrowrightleftshadedwhite": 10154,
        "arrowrightbacktiltedshadowedwhite": 10155,
        "arrowrightfronttiltedshadowedwhite": 10156,
        "arrowshadowrightlowerwhiteheavy": 10157,
        "arrowshadowrightupperwhiteheavy": 10158,
        "arrowshadowrightnotchedlowerwhite": 10159,
        "curlyloop": 10160,
        "arrowshadowrightnotchedupperwhite": 10161,
        "arrowrightcircledwhiteheavy": 10162,
        "arrowrightfeatheredwhite": 10163,
        "arrowfeatheredblackSE": 10164,
        "arrowrightfeatheredblack": 10165,
        "arrowfeatheredblackNE": 10166,
        "arrowfeatheredblackheavySE": 10167,
        "arrowrightfeatheredblackheavy": 10168,
        "arrowfeatheredblackheavyNE": 10169,
        "arrowteardropright": 10170,
        "arrowteardroprightheavy": 10171,
        "arrowrightwedge": 10172,
        "arrowrightwedgeheavy": 10173,
        "arrowrightoutlinedopen": 10174,
        "curlyloopdouble": 10175,
        "threedimensionalangle": 10176,
        "tricontainingtriwhiteanglesmall": 10177,
        "perpendicular": 10178,
        "opensubset": 10179,
        "opensuperset": 10180,
        "bagdelimitersshapeleft": 10181,
        "bagdelimitersshaperight": 10182,
        "ordotinside": 10183,
        "solidussubsetreversepreceding": 10184,
        "solidussupersetpreceding": 10185,
        "verticalbarhorizontalstroke": 10186,
        "risingdiagonal": 10187,
        "longdivision": 10188,
        "fallingdiagonal": 10189,
        "dlogicalsquare": 10190,
        "dlogicalorsquare": 10191,
        "centreddotwhitediamond": 10192,
        "andwithdot": 10193,
        "elementopeningup": 10194,
        "lowercornerdotright": 10195,
        "cornerdotupleft": 10196,
        "outerjoinleft": 10197,
        "outerjoinright": 10198,
        "outerjoinfull": 10199,
        "largetackup": 10200,
        "largetackdown": 10201,
        "turnstileleftrightdbl": 10202,
        "tackleftright": 10203,
        "multimapleft": 10204,
        "longtackright": 10205,
        "longtackleft": 10206,
        "tackcircleaboveup": 10207,
        "lozengedividedbyrulehorizontal": 10208,
        "convavediamondwhite": 10209,
        "tickconvavediamondleftwhite": 10210,
        "tickconvavediamondrightwhite": 10211,
        "tickleftwhitesquare": 10212,
        "tickrightwhitesquare": 10213,
        "bracketwhitesquareleft": 10214,
        "bracketwhitesquareright": 10215,
        "bracketangleleft": 10216,
        "bracketangleright": 10217,
        "bracketangledblleft": 10218,
        "bracketangledblright": 10219,
        "bracketshellwhiteleft": 10220,
        "bracketshellwhiteright": 10221,
        "parenflatleft": 10222,
        "parenflatright": 10223,
        "brblank": 10240,
        "dots1": 10241,
        "dots2": 10242,
        "dots12": 10243,
        "dots3": 10244,
        "dots13": 10245,
        "dots23": 10246,
        "dots123": 10247,
        "dots4": 10248,
        "dots14": 10249,
        "dots24": 10250,
        "dots124": 10251,
        "dots34": 10252,
        "dots134": 10253,
        "dots234": 10254,
        "dots1234": 10255,
        "dots5": 10256,
        "dots15": 10257,
        "dots25": 10258,
        "dots125": 10259,
        "dots35": 10260,
        "dots135": 10261,
        "dots235": 10262,
        "dots1235": 10263,
        "dots45": 10264,
        "dots145": 10265,
        "dots245": 10266,
        "dots1245": 10267,
        "dots345": 10268,
        "dots1345": 10269,
        "dots2345": 10270,
        "dots12345": 10271,
        "dots6": 10272,
        "dots16": 10273,
        "dots26": 10274,
        "dots126": 10275,
        "dots36": 10276,
        "dots136": 10277,
        "dots236": 10278,
        "dots1236": 10279,
        "dots46": 10280,
        "dots146": 10281,
        "dots246": 10282,
        "dots1246": 10283,
        "dots346": 10284,
        "dots1346": 10285,
        "dots2346": 10286,
        "dots12346": 10287,
        "dots56": 10288,
        "dots156": 10289,
        "dots256": 10290,
        "dots1256": 10291,
        "dots356": 10292,
        "dots1356": 10293,
        "dots2356": 10294,
        "dots12356": 10295,
        "dots456": 10296,
        "dots1456": 10297,
        "dots2456": 10298,
        "dots12456": 10299,
        "dots3456": 10300,
        "dots13456": 10301,
        "dots23456": 10302,
        "dots123456": 10303,
        "dots7": 10304,
        "dots17": 10305,
        "dots27": 10306,
        "dots127": 10307,
        "dots37": 10308,
        "dots137": 10309,
        "dots237": 10310,
        "dots1237": 10311,
        "dots47": 10312,
        "dots147": 10313,
        "dots247": 10314,
        "dots1247": 10315,
        "dots347": 10316,
        "dots1347": 10317,
        "dots2347": 10318,
        "dots12347": 10319,
        "dots57": 10320,
        "dots157": 10321,
        "dots257": 10322,
        "dots1257": 10323,
        "dots357": 10324,
        "dots1357": 10325,
        "dots2357": 10326,
        "dots12357": 10327,
        "dots457": 10328,
        "dots1457": 10329,
        "dots2457": 10330,
        "dots12457": 10331,
        "dots3457": 10332,
        "dots13457": 10333,
        "dots23457": 10334,
        "dots123457": 10335,
        "dots67": 10336,
        "dots167": 10337,
        "dots267": 10338,
        "dots1267": 10339,
        "dots367": 10340,
        "dots1367": 10341,
        "dots2367": 10342,
        "dots12367": 10343,
        "dots467": 10344,
        "dots1467": 10345,
        "dots2467": 10346,
        "dots12467": 10347,
        "dots3467": 10348,
        "dots13467": 10349,
        "dots23467": 10350,
        "dots123467": 10351,
        "dots567": 10352,
        "dots1567": 10353,
        "dots2567": 10354,
        "dots12567": 10355,
        "dots3567": 10356,
        "dots13567": 10357,
        "dots23567": 10358,
        "dots123567": 10359,
        "dots4567": 10360,
        "dots14567": 10361,
        "dots24567": 10362,
        "dots124567": 10363,
        "dots34567": 10364,
        "dots134567": 10365,
        "dots234567": 10366,
        "dots1234567": 10367,
        "dots8": 10368,
        "dots18": 10369,
        "dots28": 10370,
        "dots128": 10371,
        "dots38": 10372,
        "dots138": 10373,
        "dots238": 10374,
        "dots1238": 10375,
        "dots48": 10376,
        "dots148": 10377,
        "dots248": 10378,
        "dots1248": 10379,
        "dots348": 10380,
        "dots1348": 10381,
        "dots2348": 10382,
        "dots12348": 10383,
        "dots58": 10384,
        "dots158": 10385,
        "dots258": 10386,
        "dots1258": 10387,
        "dots358": 10388,
        "dots1358": 10389,
        "dots2358": 10390,
        "dots12358": 10391,
        "dots458": 10392,
        "dots1458": 10393,
        "dots2458": 10394,
        "dots12458": 10395,
        "dots3458": 10396,
        "dots13458": 10397,
        "dots23458": 10398,
        "dots123458": 10399,
        "dots68": 10400,
        "dots168": 10401,
        "dots268": 10402,
        "dots1268": 10403,
        "dots368": 10404,
        "dots1368": 10405,
        "dots2368": 10406,
        "dots12368": 10407,
        "dots468": 10408,
        "dots1468": 10409,
        "dots2468": 10410,
        "dots12468": 10411,
        "dots3468": 10412,
        "dots13468": 10413,
        "dots23468": 10414,
        "dots123468": 10415,
        "dots568": 10416,
        "dots1568": 10417,
        "dots2568": 10418,
        "dots12568": 10419,
        "dots3568": 10420,
        "dots13568": 10421,
        "dots23568": 10422,
        "dots123568": 10423,
        "dots4568": 10424,
        "dots14568": 10425,
        "dots24568": 10426,
        "dots124568": 10427,
        "dots34568": 10428,
        "dots134568": 10429,
        "dots234568": 10430,
        "dots1234568": 10431,
        "dots78": 10432,
        "dots178": 10433,
        "dots278": 10434,
        "dots1278": 10435,
        "dots378": 10436,
        "dots1378": 10437,
        "dots2378": 10438,
        "dots12378": 10439,
        "dots478": 10440,
        "dots1478": 10441,
        "dots2478": 10442,
        "dots12478": 10443,
        "dots3478": 10444,
        "dots13478": 10445,
        "dots23478": 10446,
        "dots123478": 10447,
        "dots578": 10448,
        "dots1578": 10449,
        "dots2578": 10450,
        "dots12578": 10451,
        "dots3578": 10452,
        "dots13578": 10453,
        "dots23578": 10454,
        "dots123578": 10455,
        "dots4578": 10456,
        "dots14578": 10457,
        "dots24578": 10458,
        "dots124578": 10459,
        "dots34578": 10460,
        "dots134578": 10461,
        "dots234578": 10462,
        "dots1234578": 10463,
        "dots678": 10464,
        "dots1678": 10465,
        "dots2678": 10466,
        "dots12678": 10467,
        "dots3678": 10468,
        "dots13678": 10469,
        "dots23678": 10470,
        "dots123678": 10471,
        "dots4678": 10472,
        "dots14678": 10473,
        "dots24678": 10474,
        "dots124678": 10475,
        "dots34678": 10476,
        "dots134678": 10477,
        "dots234678": 10478,
        "dots1234678": 10479,
        "dots5678": 10480,
        "dots15678": 10481,
        "dots25678": 10482,
        "dots125678": 10483,
        "dots35678": 10484,
        "dots135678": 10485,
        "dots235678": 10486,
        "dots1235678": 10487,
        "dots45678": 10488,
        "dots145678": 10489,
        "dots245678": 10490,
        "dots1245678": 10491,
        "dots345678": 10492,
        "dots1345678": 10493,
        "dots2345678": 10494,
        "dots12345678": 10495,
        "Azu": 11264,
        "Buky": 11265,
        "Vede": 11266,
        "Glagoli": 11267,
        "Dobro": 11268,
        "Yestu": 11269,
        "Zhivete": 11270,
        "Dzelo": 11271,
        "Zemlja": 11272,
        "Izhe": 11273,
        "Initializhe": 11274,
        "glag:I": 11275,
        "Djervi": 11276,
        "Kako": 11277,
        "Ljudije": 11278,
        "Myslite": 11279,
        "Nashi": 11280,
        "Onu": 11281,
        "Pokoji": 11282,
        "Ritsi": 11283,
        "Slovo": 11284,
        "Tvrido": 11285,
        "Uku": 11286,
        "Fritu": 11287,
        "Heru": 11288,
        "Otu": 11289,
        "Pe": 11290,
        "Shta": 11291,
        "Tsi": 11292,
        "Chrivi": 11293,
        "Sha": 11294,
        "Yeru": 11295,
        "Yeri": 11296,
        "Yati": 11297,
        "Spideryha": 11298,
        "Yu": 11299,
        "Smallyus": 11300,
        "Smallyuswithtail": 11301,
        "Yo": 11302,
        "Iotatedsmallyus": 11303,
        "Bigyus": 11304,
        "Iotatedbigyus": 11305,
        "Fita": 11306,
        "Izhitsa": 11307,
        "Shtapic": 11308,
        "Trokutastia": 11309,
        "Latinatemyslite": 11310,
        "azu": 11312,
        "buky": 11313,
        "vede": 11314,
        "glagoli": 11315,
        "dobro": 11316,
        "yestu": 11317,
        "zhivete": 11318,
        "dzelo": 11319,
        "zemlja": 11320,
        "izhe": 11321,
        "initializhe": 11322,
        "glag:i": 11323,
        "djervi": 11324,
        "kako": 11325,
        "ljudije": 11326,
        "myslite": 11327,
        "nashi": 11328,
        "onu": 11329,
        "pokoji": 11330,
        "ritsi": 11331,
        "slovo": 11332,
        "tvrido": 11333,
        "uku": 11334,
        "fritu": 11335,
        "heru": 11336,
        "otu": 11337,
        "pe": 11338,
        "shta": 11339,
        "tsi": 11340,
        "chrivi": 11341,
        "sha": 11342,
        "yeru": 11343,
        "yeri": 11344,
        "yati": 11345,
        "spideryha": 11346,
        "glag:yu": 11347,
        "smallyus": 11348,
        "smallyuswithtail": 11349,
        "yo": 11350,
        "iotatedsmallyus": 11351,
        "bigyus": 11352,
        "iotatedbigyus": 11353,
        "fita": 11354,
        "izhitsa": 11355,
        "shtapic": 11356,
        "trokutastia": 11357,
        "latinatemyslite": 11358,
        "Ldblbar": 11360,
        "ldblbar": 11361,
        "Lmiddletilde": 11362,
        "Pstroke": 11363,
        "Rtail": 11364,
        "astroke": 11365,
        "tstroke": 11366,
        "Hdescender": 11367,
        "hdescender": 11368,
        "Kdescender": 11369,
        "kdescender": 11370,
        "Zdescender": 11371,
        "zdescender": 11372,
        "lt:Alpha": 11373,
        "Mhook": 11374,
        "Aturned": 11375,
        "lt:Alphaturned": 11376,
        "vrighthook": 11377,
        "Whook": 11378,
        "whook": 11379,
        "vcurl": 11380,
        "Hhalf": 11381,
        "hhalf": 11382,
        "phitailless": 11383,
        "enotch": 11384,
        "rtailturned": 11385,
        "olowringinside": 11386,
        "Esmallturned": 11387,
        "j.inferior": 11388,
        "Vmod": 11389,
        "Sswashtail": 11390,
        "Zswashtail": 11391,
        "anGeok": 11520,
        "banGeok": 11521,
        "ganGeok": 11522,
        "donGeok": 11523,
        "enGeok": 11524,
        "vinGeok": 11525,
        "zenGeok": 11526,
        "tanGeok": 11527,
        "inGeok": 11528,
        "kanGeok": 11529,
        "lasGeok": 11530,
        "manGeok": 11531,
        "narGeok": 11532,
        "onGeok": 11533,
        "parGeok": 11534,
        "zharGeok": 11535,
        "raeGeok": 11536,
        "sanGeok": 11537,
        "tarGeok": 11538,
        "unGeok": 11539,
        "pharGeok": 11540,
        "kharGeok": 11541,
        "ghanGeok": 11542,
        "qarGeok": 11543,
        "shinGeok": 11544,
        "chinGeok": 11545,
        "canGeok": 11546,
        "jilGeok": 11547,
        "cilGeok": 11548,
        "charGeok": 11549,
        "xanGeok": 11550,
        "jhanGeok": 11551,
        "haeGeok": 11552,
        "heGeok": 11553,
        "hieGeok": 11554,
        "weGeok": 11555,
        "harGeok": 11556,
        "hoeGeok": 11557,
        "ynGeok": 11559,
        "aenGeok": 11565,
        "anglemarkersubstitutionright": 11776,
        "anglemarkerdottedsubstitutionright": 11777,
        "bracketsubstitutionleft": 11778,
        "bracketsubstitutionright": 11779,
        "bracketdottedsubstitutionleft": 11780,
        "bracketdottedsubstitutionright": 11781,
        "markerraisedinterpolation": 11782,
        "markerdottedraisedinterpolation": 11783,
        "markerdottedtransposition": 11784,
        "brackettranspositionleft": 11785,
        "brackettranspositionright": 11786,
        "squareraised": 11787,
        "bracketraisedleft": 11788,
        "bracketraisedright": 11789,
        "coroniseditorial": 11790,
        "paragraphos": 11791,
        "paragraphosforked": 11792,
        "paragraphosforkedreversed": 11793,
        "hypodiastole": 11794,
        "obelosdotted": 11795,
        "ancoradown": 11796,
        "ancoraup": 11797,
        "angledottedright": 11798,
        "hyphendbloblique": 11799,
        "interrobanginverted": 11800,
        "palmbranch": 11801,
        "hyphendieresis": 11802,
        "tildering": 11803,
        "bracketparaphraselowleft": 11804,
        "bracketparaphraselowright": 11805,
        "tildedotaccent": 11806,
        "tildedotbelow": 11807,
        "barquillverticalleft": 11808,
        "barquillverticalright": 11809,
        "brackethalftopleft": 11810,
        "brackethalftopright": 11811,
        "brackethalfbottomleft": 11812,
        "brackethalfbottomright": 11813,
        "ubracketleft": 11814,
        "ubracketright": 11815,
        "parendblleft": 11816,
        "parendblright": 11817,
        "twodotsoveronedot": 11818,
        "onedotovertwodots": 11819,
        "dotsquarefour": 11820,
        "fivedot": 11821,
        "questionreversed": 11822,
        "tildevertical": 11823,
        "pointring": 11824,
        "wordseparatormiddledot": 11825,
        "turnedcomma": 11826,
        "dotraised": 11827,
        "commaraised": 11828,
        "turnedsemicolon": 11829,
        "daggerwithguardleft": 11830,
        "daggerwithguardright": 11831,
        "turneddagger": 11832,
        "sectionsignhalftop": 11833,
        "emdashdbl": 11834,
        "emdashtpl": 11835,
        "stenographicfullstop": 11836,
        "sixdotsvertical": 11837,
        "wigglylinevertical": 11838,
        "capitulum": 11839,
        "hyphendbl": 11840,
        "commareversed": 11841,
        "quotedbllowreversed": 11842,
        "dashwithupturnleft": 11843,
        "suspensiondbl": 11844,
        "kavykainvertedlow": 11845,
        "kavykawithkavykaaboveinvertedlow": 11846,
        "kavykalow": 11847,
        "kavykawithdotlow": 11848,
        "stackedcommadbl": 11849,
        "solidusdotted": 11850,
        "tripledagger": 11851,
        "medievalcomma": 11852,
        "paragraphus": 11853,
        "punctuselevatus": 11854,
        "cornishversedivider": 11855,
        "ideographicspace": 12288,
        "ideographiccomma": 12289,
        "ideographicperiod": 12290,
        "dittomark": 12291,
        "jis": 12292,
        "ideographiciterationmark": 12293,
        "ideographicclose": 12294,
        "ideographiczero": 12295,
        "anglebracketleft": 12296,
        "anglebracketright": 12297,
        "dblanglebracketleft": 12298,
        "dblanglebracketright": 12299,
        "cornerbracketleft": 12300,
        "cornerbracketright": 12301,
        "whitecornerbracketleft": 12302,
        "whitecornerbracketright": 12303,
        "blacklenticularbracketleft": 12304,
        "blacklenticularbracketright": 12305,
        "postalmark": 12306,
        "getamark": 12307,
        "tortoiseshellbracketleft": 12308,
        "tortoiseshellbracketright": 12309,
        "whitelenticularbracketleft": 12310,
        "whitelenticularbracketright": 12311,
        "whitetortoiseshellbracketleft": 12312,
        "whitetortoiseshellbracketright": 12313,
        "whitesquarebracketleft": 12314,
        "whitesquarebracketright": 12315,
        "wavedash": 12316,
        "quotedblprimereversed": 12317,
        "quotedblprime": 12318,
        "lowquotedblprime": 12319,
        "postalmarkface": 12320,
        "onehangzhou": 12321,
        "twohangzhou": 12322,
        "threehangzhou": 12323,
        "fourhangzhou": 12324,
        "fivehangzhou": 12325,
        "sixhangzhou": 12326,
        "sevenhangzhou": 12327,
        "eighthangzhou": 12328,
        "ninehangzhou": 12329,
        "ideographicleveltonemark": 12330,
        "ideographicrisingtonemark": 12331,
        "ideographicdepartingtonemark": 12332,
        "ideographicenteringtonemark": 12333,
        "hangulsingledottonemark": 12334,
        "hanguldottonemarkdbl": 12335,
        "wavydash": 12336,
        "verticalkanarepeatmark": 12337,
        "verticalkanarepeatwithvoicedsoundmark": 12338,
        "verticalkanarepeatmarkupperhalf": 12339,
        "verticalkanarepeatwithvoicedsoundmarkupperhalf": 12340,
        "verticalkanarepeatmarklowerhalf": 12341,
        "circlepostalmark": 12342,
        "ideographictelegraphlinefeedseparatorsymbol": 12343,
        "tenhangzhou": 12344,
        "twentyhangzhou": 12345,
        "thirtyhangzhou": 12346,
        "verticalideographiciterationmark": 12347,
        "masumark": 12348,
        "partalternationmark": 12349,
        "ideographicvariationindicator": 12350,
        "ideographichalffillspace": 12351,
        "hira:asmall": 12353,
        "hira:a": 12354,
        "hira:ismall": 12355,
        "hira:i": 12356,
        "hira:usmall": 12357,
        "hira:u": 12358,
        "hira:esmall": 12359,
        "hira:e": 12360,
        "hira:osmall": 12361,
        "hira:o": 12362,
        "hira:ka": 12363,
        "hira:ga": 12364,
        "hira:ki": 12365,
        "hira:gi": 12366,
        "hira:ku": 12367,
        "hira:gu": 12368,
        "hira:ke": 12369,
        "hira:ge": 12370,
        "hira:ko": 12371,
        "hira:go": 12372,
        "hira:sa": 12373,
        "hira:za": 12374,
        "hira:si": 12375,
        "hira:zi": 12376,
        "hira:su": 12377,
        "hira:zu": 12378,
        "hira:se": 12379,
        "hira:ze": 12380,
        "hira:so": 12381,
        "hira:zo": 12382,
        "hira:ta": 12383,
        "hira:da": 12384,
        "hira:ti": 12385,
        "hira:di": 12386,
        "hira:tusmall": 12387,
        "hira:tu": 12388,
        "hira:du": 12389,
        "hira:te": 12390,
        "hira:de": 12391,
        "hira:to": 12392,
        "hira:do": 12393,
        "hira:na": 12394,
        "hira:ni": 12395,
        "hira:nu": 12396,
        "hira:ne": 12397,
        "hira:no": 12398,
        "hira:ha": 12399,
        "hira:ba": 12400,
        "hira:pa": 12401,
        "hira:hi": 12402,
        "hira:bi": 12403,
        "hira:pi": 12404,
        "hira:hu": 12405,
        "hira:bu": 12406,
        "hira:pu": 12407,
        "hira:he": 12408,
        "hira:be": 12409,
        "hira:pe": 12410,
        "hira:ho": 12411,
        "hira:bo": 12412,
        "hira:po": 12413,
        "hira:ma": 12414,
        "hira:mi": 12415,
        "hira:mu": 12416,
        "hira:me": 12417,
        "hira:mo": 12418,
        "hira:yasmall": 12419,
        "hira:ya": 12420,
        "hira:yusmall": 12421,
        "hira:yu": 12422,
        "hira:yosmall": 12423,
        "hira:yo": 12424,
        "hira:ra": 12425,
        "hira:ri": 12426,
        "hira:ru": 12427,
        "hira:re": 12428,
        "hira:ro": 12429,
        "hira:wasmall": 12430,
        "hira:wa": 12431,
        "hira:wi": 12432,
        "hira:we": 12433,
        "hira:wo": 12434,
        "hira:n": 12435,
        "hira:vu": 12436,
        "hira:kasmall": 12437,
        "hira:kesmall": 12438,
        "hira:voicedmarkkanacmb": 12441,
        "hira:semivoicedmarkkanacmb": 12442,
        "hira:voicedmarkkana": 12443,
        "hira:semivoicedmarkkana": 12444,
        "hira:iterationhiragana": 12445,
        "hira:voicediterationhiragana": 12446,
        "hira:digraphyori": 12447,
        "kata:doublehyphenkana": 12448,
        "kata:asmall": 12449,
        "kata:a": 12450,
        "kata:ismall": 12451,
        "kata:i": 12452,
        "kata:usmall": 12453,
        "kata:u": 12454,
        "kata:esmall": 12455,
        "kata:e": 12456,
        "kata:osmall": 12457,
        "kata:o": 12458,
        "kata:ka": 12459,
        "kata:ga": 12460,
        "kata:ki": 12461,
        "kata:gi": 12462,
        "kata:ku": 12463,
        "kata:gu": 12464,
        "kata:ke": 12465,
        "kata:ge": 12466,
        "kata:ko": 12467,
        "kata:go": 12468,
        "kata:sa": 12469,
        "kata:za": 12470,
        "kata:si": 12471,
        "kata:zi": 12472,
        "kata:su": 12473,
        "kata:zu": 12474,
        "kata:se": 12475,
        "kata:ze": 12476,
        "kata:so": 12477,
        "kata:zo": 12478,
        "kata:ta": 12479,
        "kata:da": 12480,
        "kata:ti": 12481,
        "kata:di": 12482,
        "kata:tusmall": 12483,
        "kata:tu": 12484,
        "kata:du": 12485,
        "kata:te": 12486,
        "kata:de": 12487,
        "kata:to": 12488,
        "kata:do": 12489,
        "kata:na": 12490,
        "kata:ni": 12491,
        "kata:nu": 12492,
        "kata:ne": 12493,
        "kata:no": 12494,
        "kata:ha": 12495,
        "kata:ba": 12496,
        "kata:pa": 12497,
        "kata:hi": 12498,
        "kata:bi": 12499,
        "kata:pi": 12500,
        "kata:hu": 12501,
        "kata:bu": 12502,
        "kata:pu": 12503,
        "kata:he": 12504,
        "kata:be": 12505,
        "kata:pe": 12506,
        "kata:ho": 12507,
        "kata:bo": 12508,
        "kata:po": 12509,
        "kata:ma": 12510,
        "kata:mi": 12511,
        "kata:mu": 12512,
        "kata:me": 12513,
        "kata:mo": 12514,
        "kata:yasmall": 12515,
        "kata:ya": 12516,
        "kata:yusmall": 12517,
        "kata:yu": 12518,
        "kata:yosmall": 12519,
        "kata:yo": 12520,
        "kata:ra": 12521,
        "kata:ri": 12522,
        "kata:ru": 12523,
        "kata:re": 12524,
        "kata:ro": 12525,
        "kata:wasmall": 12526,
        "kata:wa": 12527,
        "kata:wi": 12528,
        "kata:we": 12529,
        "kata:wo": 12530,
        "kata:n": 12531,
        "kata:vu": 12532,
        "kata:kasmall": 12533,
        "kata:kesmall": 12534,
        "kata:va": 12535,
        "kata:vi": 12536,
        "kata:ve": 12537,
        "kata:vo": 12538,
        "kata:middledot": 12539,
        "kata:prolongedkana": 12540,
        "kata:iteration": 12541,
        "kata:voicediteration": 12542,
        "kata:digraphkoto": 12543,
        "bopo:b": 12549,
        "bopo:p": 12550,
        "bopo:m": 12551,
        "bopo:f": 12552,
        "bopo:d": 12553,
        "bopo:t": 12554,
        "bopo:n": 12555,
        "bopo:l": 12556,
        "bopo:g": 12557,
        "bopo:k": 12558,
        "bopo:h": 12559,
        "bopo:j": 12560,
        "bopo:q": 12561,
        "bopo:x": 12562,
        "bopo:zh": 12563,
        "bopo:ch": 12564,
        "bopo:sh": 12565,
        "bopo:r": 12566,
        "bopo:z": 12567,
        "bopo:c": 12568,
        "bopo:s": 12569,
        "bopo:a": 12570,
        "bopo:o": 12571,
        "bopo:e": 12572,
        "bopo:eh": 12573,
        "bopo:ai": 12574,
        "bopo:ei": 12575,
        "bopo:au": 12576,
        "bopo:ou": 12577,
        "bopo:an": 12578,
        "bopo:en": 12579,
        "bopo:ang": 12580,
        "bopo:eng": 12581,
        "bopo:er": 12582,
        "bopo:i": 12583,
        "bopo:u": 12584,
        "bopo:iu": 12585,
        "bopo:v": 12586,
        "bopo:ng": 12587,
        "bopo:gn": 12588,
        "bopo:ih": 12589,
        "bopo:owithdotabove": 12590,
        "bopo:nn": 12591,
        "ko:kiyeok": 12593,
        "ko:ssangkiyeok": 12594,
        "ko:kiyeoksios": 12595,
        "ko:nieun": 12596,
        "ko:nieuncieuc": 12597,
        "ko:nieunhieuh": 12598,
        "ko:tikeut": 12599,
        "ko:ssangtikeut": 12600,
        "ko:rieul": 12601,
        "ko:rieulkiyeok": 12602,
        "ko:rieulmieum": 12603,
        "ko:rieulpieup": 12604,
        "ko:rieulsios": 12605,
        "ko:rieulthieuth": 12606,
        "ko:rieulphieuph": 12607,
        "ko:rieulhieuh": 12608,
        "ko:mieum": 12609,
        "ko:pieup": 12610,
        "ko:ssangpieup": 12611,
        "ko:pieupsios": 12612,
        "ko:sios": 12613,
        "ko:ssangsios": 12614,
        "ko:ieung": 12615,
        "ko:cieuc": 12616,
        "ko:ssangcieuc": 12617,
        "ko:chieuch": 12618,
        "ko:khieukh": 12619,
        "ko:thieuth": 12620,
        "ko:phieuph": 12621,
        "ko:hieuh": 12622,
        "ko:a": 12623,
        "ko:ae": 12624,
        "ko:ya": 12625,
        "ko:yae": 12626,
        "ko:eo": 12627,
        "ko:e": 12628,
        "ko:yeo": 12629,
        "ko:ye": 12630,
        "ko:o": 12631,
        "ko:wa": 12632,
        "ko:wae": 12633,
        "ko:oe": 12634,
        "ko:yo": 12635,
        "ko:u": 12636,
        "ko:weo": 12637,
        "ko:we": 12638,
        "ko:wi": 12639,
        "ko:yu": 12640,
        "ko:eu": 12641,
        "ko:yi": 12642,
        "ko:i": 12643,
        "ko:filler": 12644,
        "ko:ssangnieun": 12645,
        "ko:nieuntikeut": 12646,
        "ko:nieunsios": 12647,
        "ko:nieunpansios": 12648,
        "ko:rieulkiyeoksios": 12649,
        "ko:rieultikeut": 12650,
        "ko:rieulpieupsios": 12651,
        "ko:rieulpansios": 12652,
        "ko:rieulyeorinhieuh": 12653,
        "ko:mieumpieup": 12654,
        "ko:mieumsios": 12655,
        "ko:mieumpansios": 12656,
        "ko:kapyeounmieum": 12657,
        "ko:pieupkiyeok": 12658,
        "ko:pieuptikeut": 12659,
        "ko:pieupsioskiyeok": 12660,
        "ko:pieupsiostikeut": 12661,
        "ko:pieupcieuc": 12662,
        "ko:pieupthieuth": 12663,
        "ko:kapyeounpieup": 12664,
        "ko:kapyeounssangpieup": 12665,
        "ko:sioskiyeok": 12666,
        "ko:siosnieun": 12667,
        "ko:siostikeut": 12668,
        "ko:siospieup": 12669,
        "ko:sioscieuc": 12670,
        "ko:pansios": 12671,
        "ko:ssangieung": 12672,
        "ko:yesieung": 12673,
        "ko:yesieungsios": 12674,
        "ko:yesieungpansios": 12675,
        "ko:kapyeounphieuph": 12676,
        "ko:ssanghieuh": 12677,
        "ko:yeorinhieuh": 12678,
        "ko:yoya": 12679,
        "ko:yoyae": 12680,
        "ko:yoi": 12681,
        "ko:yuyeo": 12682,
        "ko:yuye": 12683,
        "ko:yui": 12684,
        "ko:araea": 12685,
        "ko:araeae": 12686,
        "hangulkiyeokparen": 12800,
        "hangulnieunparen": 12801,
        "hangultikeutparen": 12802,
        "hangulrieulparen": 12803,
        "hangulmieumparen": 12804,
        "hangulpieupparen": 12805,
        "hangulsiosparen": 12806,
        "hangulieungparen": 12807,
        "hangulcieucparen": 12808,
        "hangulchieuchparen": 12809,
        "hangulkhieukhparen": 12810,
        "hangulthieuthparen": 12811,
        "hangulphieuphparen": 12812,
        "hangulhieuhparen": 12813,
        "hangulkiyeokaparen": 12814,
        "hangulnieunaparen": 12815,
        "hangultikeutaparen": 12816,
        "hangulrieulaparen": 12817,
        "hangulmieumaparen": 12818,
        "hangulpieupaparen": 12819,
        "hangulsiosaparen": 12820,
        "hangulieungaparen": 12821,
        "hangulcieucaparen": 12822,
        "hangulchieuchaparen": 12823,
        "hangulkhieukhaparen": 12824,
        "hangulthieuthaparen": 12825,
        "hangulphieuphaparen": 12826,
        "hangulhieuhaparen": 12827,
        "hangulcieucuparen": 12828,
        "ojeonparen": 12829,
        "ohuparen": 12830,
        "oneideographicparen": 12832,
        "twoideographicparen": 12833,
        "threeideographicparen": 12834,
        "fourideographicparen": 12835,
        "fiveideographicparen": 12836,
        "sixideographicparen": 12837,
        "sevenideographicparen": 12838,
        "eightideographicparen": 12839,
        "nineideographicparen": 12840,
        "tenideographicparen": 12841,
        "moonideographicparen": 12842,
        "fireideographicparen": 12843,
        "waterideographicparen": 12844,
        "woodideographicparen": 12845,
        "metalideographicparen": 12846,
        "earthideographicparen": 12847,
        "sunideographicparen": 12848,
        "stockideographicparen": 12849,
        "haveideographicparen": 12850,
        "societyideographicparen": 12851,
        "nameideographicparen": 12852,
        "specialideographicparen": 12853,
        "financialideographicparen": 12854,
        "congratulationideographicparen": 12855,
        "laborideographicparen": 12856,
        "representideographicparen": 12857,
        "callideographicparen": 12858,
        "studyideographicparen": 12859,
        "superviseideographicparen": 12860,
        "enterpriseideographicparen": 12861,
        "resourceideographicparen": 12862,
        "allianceideographicparen": 12863,
        "festivalideographicparen": 12864,
        "restideographicparen": 12865,
        "selfideographicparen": 12866,
        "reachideographicparen": 12867,
        "questionideographiccircled": 12868,
        "kindergartenideographiccircled": 12869,
        "schoolideographiccircled": 12870,
        "kotoideographiccircled": 12871,
        "tencirclesquare": 12872,
        "twentycirclesquare": 12873,
        "thirtycirclesquare": 12874,
        "fortycirclesquare": 12875,
        "fiftycirclesquare": 12876,
        "sixtycirclesquare": 12877,
        "seventycirclesquare": 12878,
        "eightycirclesquare": 12879,
        "partnership": 12880,
        "twentyonecircle": 12881,
        "twentytwocircle": 12882,
        "twentythreecircle": 12883,
        "twentyfourcircle": 12884,
        "twentyfivecircle": 12885,
        "twentysixcircle": 12886,
        "twentysevencircle": 12887,
        "twentyeightcircle": 12888,
        "twentyninecircle": 12889,
        "thirtycircle": 12890,
        "thirtyonecircle": 12891,
        "thirtytwocircle": 12892,
        "thirtythreecircle": 12893,
        "thirtyfourcircle": 12894,
        "thirtyfivecircle": 12895,
        "kiyeokcirclekorean": 12896,
        "nieuncirclekorean": 12897,
        "tikeutcirclekorean": 12898,
        "rieulcirclekorean": 12899,
        "mieumcirclekorean": 12900,
        "pieupcirclekorean": 12901,
        "sioscirclekorean": 12902,
        "ieungcirclekorean": 12903,
        "cieuccirclekorean": 12904,
        "chieuchcirclekorean": 12905,
        "khieukhcirclekorean": 12906,
        "thieuthcirclekorean": 12907,
        "phieuphcirclekorean": 12908,
        "hieuhcirclekorean": 12909,
        "kiyeokacirclekorean": 12910,
        "nieunacirclekorean": 12911,
        "tikeutacirclekorean": 12912,
        "rieulacirclekorean": 12913,
        "mieumacirclekorean": 12914,
        "pieupacirclekorean": 12915,
        "siosacirclekorean": 12916,
        "ieungacirclekorean": 12917,
        "cieucacirclekorean": 12918,
        "chieuchacirclekorean": 12919,
        "khieukhacirclekorean": 12920,
        "thieuthacirclekorean": 12921,
        "phieuphacirclekorean": 12922,
        "hieuhacirclekorean": 12923,
        "chamkocircle": 12924,
        "jueuicircle": 12925,
        "ieungucirclekorean": 12926,
        "koreanstandardsymbol": 12927,
        "oneideographiccircled": 12928,
        "twoideographiccircled": 12929,
        "threeideographiccircled": 12930,
        "fourideographiccircled": 12931,
        "fiveideographiccircled": 12932,
        "sixideographiccircled": 12933,
        "sevenideographiccircled": 12934,
        "eightideographiccircled": 12935,
        "nineideographiccircled": 12936,
        "tenideographiccircled": 12937,
        "moonideographiccircled": 12938,
        "fireideographiccircled": 12939,
        "waterideographiccircled": 12940,
        "woodideographiccircled": 12941,
        "metalideographiccircled": 12942,
        "earthideographiccircled": 12943,
        "sunideographiccircled": 12944,
        "stockideographiccircled": 12945,
        "haveideographiccircled": 12946,
        "societyideographiccircled": 12947,
        "nameideographiccircled": 12948,
        "specialideographiccircled": 12949,
        "financialideographiccircled": 12950,
        "congratulationideographiccircled": 12951,
        "laborideographiccircled": 12952,
        "secretideographiccircled": 12953,
        "maleideographiccircled": 12954,
        "femaleideographiccircled": 12955,
        "suitableideographiccircled": 12956,
        "excellentideographiccircled": 12957,
        "printideographiccircled": 12958,
        "attentionideographiccircled": 12959,
        "itemideographiccircled": 12960,
        "restideographiccircled": 12961,
        "copyideographiccircled": 12962,
        "correctideographiccircled": 12963,
        "highideographiccircled": 12964,
        "centreideographiccircled": 12965,
        "lowideographiccircled": 12966,
        "leftideographiccircled": 12967,
        "rightideographiccircled": 12968,
        "medicineideographiccircled": 12969,
        "religionideographiccircled": 12970,
        "studyideographiccircled": 12971,
        "superviseideographiccircled": 12972,
        "enterpriseideographiccircled": 12973,
        "resourceideographiccircled": 12974,
        "allianceideographiccircled": 12975,
        "nightideographiccircled": 12976,
        "thirtysixcircle": 12977,
        "thirtysevencircle": 12978,
        "thirtyeightcircle": 12979,
        "thirtyninecircle": 12980,
        "fortycircle": 12981,
        "fortyonecircle": 12982,
        "fortytwocircle": 12983,
        "fortythreecircle": 12984,
        "fortyfourcircle": 12985,
        "fortyfivecircle": 12986,
        "fortysixcircle": 12987,
        "fortysevencircle": 12988,
        "fortyeightcircle": 12989,
        "fortyninecircle": 12990,
        "fiftycircle": 12991,
        "januarytelegraph": 12992,
        "februarytelegraph": 12993,
        "marchtelegraph": 12994,
        "apriltelegraph": 12995,
        "maytelegraph": 12996,
        "junetelegraph": 12997,
        "julytelegraph": 12998,
        "augusttelegraph": 12999,
        "septembertelegraph": 13000,
        "octobertelegraph": 13001,
        "novembertelegraph": 13002,
        "decembertelegraph": 13003,
        "Hgfullwidth": 13004,
        "ergfullwidth": 13005,
        "eVfullwidth": 13006,
        "LTDfullwidth": 13007,
        "acirclekatakana": 13008,
        "icirclekatakana": 13009,
        "ucirclekatakana": 13010,
        "ecirclekatakana": 13011,
        "ocirclekatakana": 13012,
        "kacirclekatakana": 13013,
        "kicirclekatakana": 13014,
        "kucirclekatakana": 13015,
        "kecirclekatakana": 13016,
        "kocirclekatakana": 13017,
        "sacirclekatakana": 13018,
        "sicirclekatakana": 13019,
        "sucirclekatakana": 13020,
        "secirclekatakana": 13021,
        "socirclekatakana": 13022,
        "tacirclekatakana": 13023,
        "ticirclekatakana": 13024,
        "tucirclekatakana": 13025,
        "tecirclekatakana": 13026,
        "tocirclekatakana": 13027,
        "nacirclekatakana": 13028,
        "nicirclekatakana": 13029,
        "nucirclekatakana": 13030,
        "necirclekatakana": 13031,
        "nocirclekatakana": 13032,
        "hacirclekatakana": 13033,
        "hicirclekatakana": 13034,
        "hucirclekatakana": 13035,
        "hecirclekatakana": 13036,
        "hocirclekatakana": 13037,
        "macirclekatakana": 13038,
        "micirclekatakana": 13039,
        "mucirclekatakana": 13040,
        "mecirclekatakana": 13041,
        "mocirclekatakana": 13042,
        "yacirclekatakana": 13043,
        "yucirclekatakana": 13044,
        "yocirclekatakana": 13045,
        "racirclekatakana": 13046,
        "ricirclekatakana": 13047,
        "rucirclekatakana": 13048,
        "recirclekatakana": 13049,
        "rocirclekatakana": 13050,
        "wacirclekatakana": 13051,
        "wicirclekatakana": 13052,
        "wecirclekatakana": 13053,
        "wocirclekatakana": 13054,
        "squareeranamereiwa": 13055,
        "apaatosquare": 13056,
        "aruhuasquare": 13057,
        "anpeasquare": 13058,
        "aarusquare": 13059,
        "iningusquare": 13060,
        "intisquare": 13061,
        "uonsquare": 13062,
        "esukuudosquare": 13063,
        "eekaasquare": 13064,
        "onsusquare": 13065,
        "oomusquare": 13066,
        "kairisquare": 13067,
        "karattosquare": 13068,
        "karoriisquare": 13069,
        "garonsquare": 13070,
        "ganmasquare": 13071,
        "gigasquare": 13072,
        "giniisquare": 13073,
        "kyuriisquare": 13074,
        "girudaasquare": 13075,
        "kirosquare": 13076,
        "kiroguramusquare": 13077,
        "kiromeetorusquare": 13078,
        "kirowattosquare": 13079,
        "guramusquare": 13080,
        "guramutonsquare": 13081,
        "kuruzeirosquare": 13082,
        "kuroonesquare": 13083,
        "keesusquare": 13084,
        "korunasquare": 13085,
        "kooposquare": 13086,
        "saikurusquare": 13087,
        "santiimusquare": 13088,
        "siringusquare": 13089,
        "sentisquare": 13090,
        "sentosquare": 13091,
        "daasusquare": 13092,
        "desisquare": 13093,
        "dorusquare": 13094,
        "tonsquare": 13095,
        "nanosquare": 13096,
        "nottosquare": 13097,
        "haitusquare": 13098,
        "paasentosquare": 13099,
        "paatusquare": 13100,
        "baarerusquare": 13101,
        "piasutorusquare": 13102,
        "pikurusquare": 13103,
        "pikosquare": 13104,
        "birusquare": 13105,
        "huaraddosquare": 13106,
        "huiitosquare": 13107,
        "bussyerusquare": 13108,
        "huransquare": 13109,
        "hekutaarusquare": 13110,
        "pesosquare": 13111,
        "penihisquare": 13112,
        "herutusquare": 13113,
        "pensusquare": 13114,
        "peezisquare": 13115,
        "beetasquare": 13116,
        "pointosquare": 13117,
        "borutosquare": 13118,
        "honsquare": 13119,
        "pondosquare": 13120,
        "hoorusquare": 13121,
        "hoonsquare": 13122,
        "maikurosquare": 13123,
        "mairusquare": 13124,
        "mahhasquare": 13125,
        "marukusquare": 13126,
        "mansyonsquare": 13127,
        "mikuronsquare": 13128,
        "mirisquare": 13129,
        "miribaarusquare": 13130,
        "megasquare": 13131,
        "megatonsquare": 13132,
        "meetorusquare": 13133,
        "yaadosquare": 13134,
        "yaarusquare": 13135,
        "yuansquare": 13136,
        "rittorusquare": 13137,
        "rirasquare": 13138,
        "rupiisquare": 13139,
        "ruuburusquare": 13140,
        "remusquare": 13141,
        "rentogensquare": 13142,
        "wattosquare": 13143,
        "ideographictelegraphsymbolforhourzero": 13144,
        "ideographictelegraphsymbolforhourone": 13145,
        "ideographictelegraphsymbolforhourtwo": 13146,
        "ideographictelegraphsymbolforhourthree": 13147,
        "ideographictelegraphsymbolforhourfour": 13148,
        "ideographictelegraphsymbolforhourfive": 13149,
        "ideographictelegraphsymbolforhoursix": 13150,
        "ideographictelegraphsymbolforhourseven": 13151,
        "ideographictelegraphsymbolforhoureight": 13152,
        "ideographictelegraphsymbolforhournine": 13153,
        "ideographictelegraphsymbolforhourten": 13154,
        "ideographictelegraphsymbolforhoureleven": 13155,
        "ideographictelegraphsymbolforhourtwelve": 13156,
        "ideographictelegraphsymbolforhourthirteen": 13157,
        "ideographictelegraphsymbolforhourfourteen": 13158,
        "ideographictelegraphsymbolforhourfifteen": 13159,
        "ideographictelegraphsymbolforhoursixteen": 13160,
        "ideographictelegraphsymbolforhourseventeen": 13161,
        "ideographictelegraphsymbolforhoureighteen": 13162,
        "ideographictelegraphsymbolforhournineteen": 13163,
        "ideographictelegraphsymbolforhourtwenty": 13164,
        "ideographictelegraphsymbolforhourtwentyone": 13165,
        "ideographictelegraphsymbolforhourtwentytwo": 13166,
        "ideographictelegraphsymbolforhourtwentythree": 13167,
        "ideographictelegraphsymbolforhourtwentyfour": 13168,
        "hpafullwidth": 13169,
        "dafullwidth": 13170,
        "aufullwidth": 13171,
        "barfullwidth": 13172,
        "ovfullwidth": 13173,
        "pcfullwidth": 13174,
        "dmfullwidth": 13175,
        "dm2fullwidth": 13176,
        "dm3fullwidth": 13177,
        "iufullwidth": 13178,
        "eranameheiseisquare": 13179,
        "eranamesyouwasquare": 13180,
        "eranametaisyousquare": 13181,
        "eranamemeizisquare": 13182,
        "corporationsquare": 13183,
        "paampsfullwidth": 13184,
        "nafullwidth": 13185,
        "muafullwidth": 13186,
        "mafullwidth": 13187,
        "kafullwidth": 13188,
        "kbfullwidth": 13189,
        "mbfullwidth": 13190,
        "gbfullwidth": 13191,
        "calfullwidth": 13192,
        "kcalfullwidth": 13193,
        "pffullwidth": 13194,
        "nffullwidth": 13195,
        "muffullwidth": 13196,
        "mugfullwidth": 13197,
        "mgfullwidth": 13198,
        "kgfullwidth": 13199,
        "hzfullwidth": 13200,
        "khzfullwidth": 13201,
        "mhzfullwidth": 13202,
        "ghzfullwidth": 13203,
        "thzfullwidth": 13204,
        "mulfullwidth": 13205,
        "mlfullwidth": 13206,
        "dlfullwidth": 13207,
        "klfullwidth": 13208,
        "fmfullwidth": 13209,
        "nmfullwidth": 13210,
        "mumfullwidth": 13211,
        "mmfullwidth": 13212,
        "cmfullwidth": 13213,
        "kmfullwidth": 13214,
        "mm2fullwidth": 13215,
        "cm2fullwidth": 13216,
        "m2fullwidth": 13217,
        "km2fullwidth": 13218,
        "mm3fullwidth": 13219,
        "cm3fullwidth": 13220,
        "m3fullwidth": 13221,
        "km3fullwidth": 13222,
        "moversfullwidth": 13223,
        "movers2fullwidth": 13224,
        "pafullwidth": 13225,
        "kpafullwidth": 13226,
        "mpafullwidth": 13227,
        "gpafullwidth": 13228,
        "radfullwidth": 13229,
        "radoversfullwidth": 13230,
        "radovers2fullwidth": 13231,
        "psfullwidth": 13232,
        "nsfullwidth": 13233,
        "musfullwidth": 13234,
        "msfullwidth": 13235,
        "pvfullwidth": 13236,
        "nvfullwidth": 13237,
        "muvfullwidth": 13238,
        "mvfullwidth": 13239,
        "kvfullwidth": 13240,
        "mvmegafullwidth": 13241,
        "pwfullwidth": 13242,
        "nwfullwidth": 13243,
        "muwfullwidth": 13244,
        "mwfullwidth": 13245,
        "kwfullwidth": 13246,
        "mwmegafullwidth": 13247,
        "kohmfullwidth": 13248,
        "mohmfullwidth": 13249,
        "amfullwidth": 13250,
        "bqfullwidth": 13251,
        "ccfullwidth": 13252,
        "cdfullwidth": 13253,
        "coverkgfullwidth": 13254,
        "cofullwidth": 13255,
        "dbfullwidth": 13256,
        "gyfullwidth": 13257,
        "hafullwidth": 13258,
        "hpfullwidth": 13259,
        "infullwidth": 13260,
        "kkfullwidth": 13261,
        "kmcapitalfullwidth": 13262,
        "ktfullwidth": 13263,
        "lmfullwidth": 13264,
        "lnfullwidth": 13265,
        "logfullwidth": 13266,
        "lxfullwidth": 13267,
        "mbsmallfullwidth": 13268,
        "milfullwidth": 13269,
        "molfullwidth": 13270,
        "phfullwidth": 13271,
        "pmfullwidth": 13272,
        "ppmfullwidth": 13273,
        "prfullwidth": 13274,
        "srfullwidth": 13275,
        "svfullwidth": 13276,
        "wbfullwidth": 13277,
        "vovermfullwidth": 13278,
        "aovermfullwidth": 13279,
        "dayonetelegraph": 13280,
        "daytwotelegraph": 13281,
        "daythreetelegraph": 13282,
        "dayfourtelegraph": 13283,
        "dayfivetelegraph": 13284,
        "daysixtelegraph": 13285,
        "dayseventelegraph": 13286,
        "dayeighttelegraph": 13287,
        "dayninetelegraph": 13288,
        "daytentelegraph": 13289,
        "dayeleventelegraph": 13290,
        "daytwelvetelegraph": 13291,
        "daythirteentelegraph": 13292,
        "dayfourteentelegraph": 13293,
        "dayfifteentelegraph": 13294,
        "daysixteentelegraph": 13295,
        "dayseventeentelegraph": 13296,
        "dayeighteentelegraph": 13297,
        "daynineteentelegraph": 13298,
        "daytwentytelegraph": 13299,
        "daytwentyonetelegraph": 13300,
        "daytwentytwotelegraph": 13301,
        "daytwentythreetelegraph": 13302,
        "daytwentyfourtelegraph": 13303,
        "daytwentyfivetelegraph": 13304,
        "daytwentysixtelegraph": 13305,
        "daytwentyseventelegraph": 13306,
        "daytwentyeighttelegraph": 13307,
        "daytwentyninetelegraph": 13308,
        "daythirtytelegraph": 13309,
        "daythirtyonetelegraph": 13310,
        "galsquare": 13311,
        "stresstonemod": 42784,
        "stresslowtonemod": 42785,
        "Egyptalef": 42786,
        "egyptalef": 42787,
        "Egyptain": 42788,
        "egyptain": 42789,
        "Heng": 42790,
        "heng": 42791,
        "Tz": 42792,
        "tz": 42793,
        "Tresillo": 42794,
        "tresillo": 42795,
        "Cuatrillo": 42796,
        "cuatrillo": 42797,
        "Cuatrillocomma": 42798,
        "cuatrillocomma": 42799,
        "Fsmall": 42800,
        "Ssmall": 42801,
        "AA": 42802,
        "aa": 42803,
        "AO": 42804,
        "ao": 42805,
        "AU": 42806,
        "au": 42807,
        "AV": 42808,
        "av": 42809,
        "AVhorizontalbar": 42810,
        "avhorizontalbar": 42811,
        "AY": 42812,
        "ay": 42813,
        "Cdotreversed": 42814,
        "cdotreversed": 42815,
        "Kstroke": 42816,
        "kstroke": 42817,
        "Kdiagonalstroke": 42818,
        "kdiagonalstroke": 42819,
        "Kstrokediagonalstroke": 42820,
        "kstrokediagonalstroke": 42821,
        "Lbroken": 42822,
        "lbroken": 42823,
        "Lstroke": 42824,
        "lstroke": 42825,
        "Ostroke": 42826,
        "ostroke": 42827,
        "Oloop": 42828,
        "oloop": 42829,
        "OO": 42830,
        "oo": 42831,
        "Pstrokedescender": 42832,
        "pstrokedescender": 42833,
        "Pflourish": 42834,
        "pflourish": 42835,
        "Ptail": 42836,
        "ptail": 42837,
        "Qstrokedescender": 42838,
        "qstrokedescender": 42839,
        "Qdiagonalstroke": 42840,
        "qdiagonalstroke": 42841,
        "Rrotunda": 42842,
        "rrotunda": 42843,
        "Rumrotunda": 42844,
        "rumrotunda": 42845,
        "Vdiagonalstroke": 42846,
        "vdiagonalstroke": 42847,
        "Vy": 42848,
        "vy": 42849,
        "Visigothicz": 42850,
        "visigothicz": 42851,
        "Thornstroke": 42852,
        "thornstroke": 42853,
        "Thornstrokedescender": 42854,
        "thornstrokedescender": 42855,
        "Vend": 42856,
        "vend": 42857,
        "Et": 42858,
        "et": 42859,
        "Is": 42860,
        "is": 42861,
        "Con": 42862,
        "con": 42863,
        "usmod": 42864,
        "dum": 42865,
        "lum": 42866,
        "mum": 42867,
        "num": 42868,
        "rum": 42869,
        "Rumsmall": 42870,
        "tum": 42871,
        "um": 42872,
        "Dinsular": 42873,
        "dinsular": 42874,
        "Finsular": 42875,
        "finsular": 42876,
        "Ginsular": 42877,
        "Ginsularturned": 42878,
        "ginsularturned": 42879,
        "Lturned": 42880,
        "lturned": 42881,
        "Rinsular": 42882,
        "rinsular": 42883,
        "Sinsular": 42884,
        "sinsular": 42885,
        "Tinsular": 42886,
        "tinsular": 42887,
        "circumflexlow": 42888,
        "colonmod": 42889,
        "shortequalsmod": 42890,
        "Saltillo": 42891,
        "saltillo": 42892,
        "Hturned": 42893,
        "lbeltretroflex": 42894,
        "sinologicaldot": 42895,
        "Ndescender": 42896,
        "ndescender": 42897,
        "Cbar": 42898,
        "cbar": 42899,
        "cpalatalhook": 42900,
        "hpalatalhook": 42901,
        "Bflourish": 42902,
        "bflourish": 42903,
        "Fstroke": 42904,
        "fstroke": 42905,
        "Volapukae": 42906,
        "volapukae": 42907,
        "Volapukoe": 42908,
        "volapukoe": 42909,
        "Volapukue": 42910,
        "volapukue": 42911,
        "Gobliquestroke": 42912,
        "gobliquestroke": 42913,
        "Kobliquestroke": 42914,
        "kobliquestroke": 42915,
        "Nobliquestroke": 42916,
        "nobliquestroke": 42917,
        "Robliquestroke": 42918,
        "robliquestroke": 42919,
        "Sobliquestroke": 42920,
        "sobliquestroke": 42921,
        "Hhook": 42922,
        "Ereversedopen": 42923,
        "Scriptg": 42924,
        "Lbelt": 42925,
        "Ismall": 42926,
        "Qsmall": 42927,
        "Kturned": 42928,
        "Tturned": 42929,
        "Jcrossed-tail": 42930,
        "lt:Chi": 42931,
        "lt:Beta": 42932,
        "lt:beta": 42933,
        "lt:Omega": 42934,
        "lt:omega": 42935,
        "Ustroke": 42936,
        "ustroke": 42937,
        "Glottala": 42938,
        "glottala": 42939,
        "Glottali": 42940,
        "glottali": 42941,
        "Glottalu": 42942,
        "glottalu": 42943,
        "Anglicanaw": 42946,
        "anglicanaw": 42947,
        "Cpalatalhook": 42948,
        "Shook": 42949,
        "Zpalatalhook": 42950,
        "iepigraphicsideways": 42999,
        "Hstrokemod": 43000,
        "ligatureoemod": 43001,
        "Mturnedsmall": 43002,
        "freversedepigraphic": 43003,
        "preversedepigraphic": 43004,
        "mepigraphicinverted": 43005,
        "iaepigraphic": 43006,
        "archaicmepigraphic": 43007,
        "panyangga": 43392,
        "cecak": 43393,
        "layar": 43394,
        "wignyan": 43395,
        "java:a": 43396,
        "ikawi": 43397,
        "java:i": 43398,
        "ii": 43399,
        "java:u": 43400,
        "pacerek": 43401,
        "ngalelet": 43402,
        "ngaleletraswadi": 43403,
        "java:e": 43404,
        "ai": 43405,
        "java:o": 43406,
        "ka": 43407,
        "kasasak": 43408,
        "kamurda": 43409,
        "ga": 43410,
        "gamurda": 43411,
        "nga": 43412,
        "ca": 43413,
        "camurda": 43414,
        "ja": 43415,
        "nyamurda": 43416,
        "jamahaprana": 43417,
        "nya": 43418,
        "tta": 43419,
        "ttamahaprana": 43420,
        "dda": 43421,
        "ddamahaprana": 43422,
        "namurda": 43423,
        "ta": 43424,
        "tamurda": 43425,
        "da": 43426,
        "damahaprana": 43427,
        "na": 43428,
        "pa": 43429,
        "pamurda": 43430,
        "ba": 43431,
        "bamurda": 43432,
        "ma": 43433,
        "ya": 43434,
        "ra": 43435,
        "raagung": 43436,
        "la": 43437,
        "wa": 43438,
        "samurda": 43439,
        "samahaprana": 43440,
        "sa": 43441,
        "ha": 43442,
        "cecaktelu": 43443,
        "tarungvowel": 43444,
        "tolongvowel": 43445,
        "wuluvowel": 43446,
        "wulumelikvowel": 43447,
        "sukuvowel": 43448,
        "sukumendutvowel": 43449,
        "talingvowel": 43450,
        "dirgamurevowel": 43451,
        "pepetvowel": 43452,
        "keretconsonant": 43453,
        "pengkalconsonant": 43454,
        "cakraconsonant": 43455,
        "pangkon": 43456,
        "rerengganleft": 43457,
        "rerengganright": 43458,
        "andappada": 43459,
        "madyapada": 43460,
        "luhurpada": 43461,
        "windupada": 43462,
        "pangkatpada": 43463,
        "lingsapada": 43464,
        "lungsipada": 43465,
        "adegpada": 43466,
        "adegadegpada": 43467,
        "piselehpada": 43468,
        "turnedpiselehpada": 43469,
        "pangrangkep": 43471,
        "java:zero": 43472,
        "java:one": 43473,
        "java:two": 43474,
        "java:three": 43475,
        "java:four": 43476,
        "java:five": 43477,
        "java:six": 43478,
        "java:seven": 43479,
        "java:eight": 43480,
        "java:nine": 43481,
        "tirtatumetespada": 43486,
        "isen-isenpada": 43487,
        "redalphabar": 43824,
        "areversedschwa": 43825,
        "efractur": 43826,
        "redebar": 43827,
        "eflourish": 43828,
        "flenis": 43829,
        "gtailscript": 43830,
        "llazyinverteds": 43831,
        "lmiddledbltilde": 43832,
        "lmiddlering": 43833,
        "mtail": 43834,
        "ntail": 43835,
        "engtail": 43836,
        "ofractur": 43837,
        "ofracturstroke": 43838,
        "ostrokeopen": 43839,
        "oeinverted": 43840,
        "oestroketurned": 43841,
        "oehorizontalstroketurned": 43842,
        "ooopenturned": 43843,
        "ooopenstroketurned": 43844,
        "stirrupr": 43845,
        "Rrightlegsmall": 43846,
        "routhandle": 43847,
        "rdbl": 43848,
        "rtail": 43849,
        "rtaildbl": 43850,
        "rscript": 43851,
        "rringscript": 43852,
        "baselineesh": 43853,
        "urightlegshort": 43854,
        "urightlegbarshort": 43855,
        "ui": 43856,
        "uiturned": 43857,
        "ulefthook": 43858,
        "lt:chi": 43859,
        "lt:chilowrightring": 43860,
        "lt:chilowleftserif": 43861,
        "xlowrightring": 43862,
        "xlongleftleg": 43863,
        "xlongleftlegandlowrightring": 43864,
        "xlongleftlegserif": 43865,
        "yrightlegshort": 43866,
        "breveinvertedmod": 43867,
        "hengmod": 43868,
        "llazyinvertedsmod": 43869,
        "lmiddletildemod": 43870,
        "ulefthookmod": 43871,
        "sakhayat": 43872,
        "iotifiede": 43873,
        "oeopen": 43874,
        "uo": 43875,
        "alphainverted": 43876,
        "lt:Omegasmall": 43877,
        "dzdigraphretroflexhook": 43878,
        "tsdigraphretroflexhook": 43879,
        "chrk:asmall": 43888,
        "chrk:esmall": 43889,
        "chrk:ismall": 43890,
        "chrk:osmall": 43891,
        "chrk:usmall": 43892,
        "chrk:vsmall": 43893,
        "chrk:gasmall": 43894,
        "chrk:kasmall": 43895,
        "chrk:gesmall": 43896,
        "chrk:gismall": 43897,
        "chrk:gosmall": 43898,
        "chrk:gusmall": 43899,
        "chrk:gvsmall": 43900,
        "chrk:hasmall": 43901,
        "chrk:hesmall": 43902,
        "chrk:hismall": 43903,
        "chrk:hosmall": 43904,
        "chrk:husmall": 43905,
        "chrk:hvsmall": 43906,
        "chrk:lasmall": 43907,
        "chrk:lesmall": 43908,
        "chrk:lismall": 43909,
        "chrk:losmall": 43910,
        "chrk:lusmall": 43911,
        "chrk:lvsmall": 43912,
        "chrk:masmall": 43913,
        "chrk:mesmall": 43914,
        "chrk:mismall": 43915,
        "chrk:mosmall": 43916,
        "chrk:musmall": 43917,
        "chrk:nasmall": 43918,
        "chrk:hnasmall": 43919,
        "chrk:nahsmall": 43920,
        "chrk:nesmall": 43921,
        "chrk:nismall": 43922,
        "chrk:nosmall": 43923,
        "chrk:nusmall": 43924,
        "chrk:nvsmall": 43925,
        "chrk:quasmall": 43926,
        "chrk:quesmall": 43927,
        "chrk:quismall": 43928,
        "chrk:quosmall": 43929,
        "chrk:quusmall": 43930,
        "chrk:quvsmall": 43931,
        "chrk:sasmall": 43932,
        "chrk:ssmall": 43933,
        "chrk:sesmall": 43934,
        "chrk:sismall": 43935,
        "chrk:sosmall": 43936,
        "chrk:susmall": 43937,
        "chrk:svsmall": 43938,
        "chrk:dasmall": 43939,
        "chrk:tasmall": 43940,
        "chrk:desmall": 43941,
        "chrk:tesmall": 43942,
        "chrk:dismall": 43943,
        "chrk:tismall": 43944,
        "chrk:dosmall": 43945,
        "chrk:dusmall": 43946,
        "chrk:dvsmall": 43947,
        "chrk:dlasmall": 43948,
        "chrk:tlasmall": 43949,
        "chrk:tlesmall": 43950,
        "chrk:tlismall": 43951,
        "chrk:tlosmall": 43952,
        "chrk:tlusmall": 43953,
        "chrk:tlvsmall": 43954,
        "chrk:tsasmall": 43955,
        "chrk:tsesmall": 43956,
        "chrk:tsismall": 43957,
        "chrk:tsosmall": 43958,
        "chrk:tsusmall": 43959,
        "chrk:tsvsmall": 43960,
        "chrk:wasmall": 43961,
        "chrk:wesmall": 43962,
        "chrk:wismall": 43963,
        "chrk:wosmall": 43964,
        "chrk:wusmall": 43965,
        "chrk:wvsmall": 43966,
        "chrk:yasmall": 43967,
        "f_f": 64256,
        "fi": 64257,
        "fl": 64258,
        "f_f_i": 64259,
        "f_f_l": 64260,
        "longs_t": 64261,
        "s_t": 64262,
        "men_nowarmn": 64275,
        "men_echarmn": 64276,
        "men_iniarmn": 64277,
        "vew_nowarmn": 64278,
        "men_xeharmn": 64279,
        "yodwithhiriq:hb": 64285,
        "varikajudeospanish:hb": 64286,
        "yod_yod_patah:hb": 64287,
        "ayinalt:hb": 64288,
        "alefwide:hb": 64289,
        "daletwide:hb": 64290,
        "hewide:hb": 64291,
        "kafwide:hb": 64292,
        "lamedwide:hb": 64293,
        "finalmemwide:hb": 64294,
        "reshwide:hb": 64295,
        "tavwide:hb": 64296,
        "plussignalt:hb": 64297,
        "shinwithshinDot:hb": 64298,
        "shinwithsinDot:hb": 64299,
        "shinwithdageshandshinDot:hb": 64300,
        "shinwithdageshandsinDot:hb": 64301,
        "alefwithpatah:hb": 64302,
        "alefwithqamats:hb": 64303,
        "alefwithmapiq:hb": 64304,
        "betwithdagesh:hb": 64305,
        "gimelwithdagesh:hb": 64306,
        "daletwithdagesh:hb": 64307,
        "hewithmapiq:hb": 64308,
        "vavwithdagesh:hb": 64309,
        "zayinwithdagesh:hb": 64310,
        "tetwithdagesh:hb": 64312,
        "yodwithdagesh:hb": 64313,
        "finalkafwithdagesh:hb": 64314,
        "kafwithdagesh:hb": 64315,
        "lamedwithdagesh:hb": 64316,
        "memwithdagesh:hb": 64318,
        "nunwithdagesh:hb": 64320,
        "samekhwithdagesh:hb": 64321,
        "finalpewithdagesh:hb": 64323,
        "pewithdagesh:hb": 64324,
        "tsadiwithdagesh:hb": 64326,
        "qofwithdagesh:hb": 64327,
        "reshwithdagesh:hb": 64328,
        "shinwithdagesh:hb": 64329,
        "tavwithdagesh:hb": 64330,
        "vavwithholam:hb": 64331,
        "betwithrafe:hb": 64332,
        "kafwithrafe:hb": 64333,
        "pewithrafe:hb": 64334,
        "ligaturealeflamed:hb": 64335,
        "alefwasla.isol": 64336,
        "alefwasla.fina": 64337,
        "beeh.isol": 64338,
        "beeh.fina": 64339,
        "beeh.init": 64340,
        "beeh.medi": 64341,
        "peh.isol": 64342,
        "peh.fina": 64343,
        "peh.init": 64344,
        "peh.medi": 64345,
        "beheh.isol": 64346,
        "beheh.fina": 64347,
        "beheh.init": 64348,
        "beheh.medi": 64349,
        "tteheh.isol": 64350,
        "tteheh.fina": 64351,
        "tteheh.init": 64352,
        "tteheh.medi": 64353,
        "teheh.isol": 64354,
        "teheh.fina": 64355,
        "teheh.init": 64356,
        "teheh.medi": 64357,
        "tteh.isol": 64358,
        "tteh.fina": 64359,
        "tteh.init": 64360,
        "tteh.medi": 64361,
        "veh.isol": 64362,
        "veh.fina": 64363,
        "veh.init": 64364,
        "veh.medi": 64365,
        "peheh.isol": 64366,
        "peheh.fina": 64367,
        "peheh.init": 64368,
        "peheh.medi": 64369,
        "dyeh.isol": 64370,
        "dyeh.fina": 64371,
        "dyeh.init": 64372,
        "dyeh.medi": 64373,
        "nyeh.isol": 64374,
        "nyeh.fina": 64375,
        "nyeh.init": 64376,
        "nyeh.medi": 64377,
        "tcheh.isol": 64378,
        "tcheh.fina": 64379,
        "tcheh.init": 64380,
        "tcheh.medi": 64381,
        "tcheheh.isol": 64382,
        "tcheheh.fina": 64383,
        "tcheheh.init": 64384,
        "tcheheh.medi": 64385,
        "ddahal.isol": 64386,
        "ddahal.fina": 64387,
        "dahal.isol": 64388,
        "dahal.fina": 64389,
        "dul.isol": 64390,
        "dul.fina": 64391,
        "ddal.isol": 64392,
        "ddal.fina": 64393,
        "jeh.isol": 64394,
        "jeh.fina": 64395,
        "rreh.isol": 64396,
        "rreh.fina": 64397,
        "keheh.isol": 64398,
        "keheh.fina": 64399,
        "keheh.init": 64400,
        "keheh.medi": 64401,
        "gaf.isol": 64402,
        "gaf.fina": 64403,
        "gaf.init": 64404,
        "gaf.medi": 64405,
        "gueh.isol": 64406,
        "gueh.fina": 64407,
        "gueh.init": 64408,
        "gueh.medi": 64409,
        "ngoeh.isol": 64410,
        "ngoeh.fina": 64411,
        "ngoeh.init": 64412,
        "ngoeh.medi": 64413,
        "noonghunna.isol": 64414,
        "noonghunna.fina": 64415,
        "rnoon.isol": 64416,
        "rnoon.fina": 64417,
        "rnoon.init": 64418,
        "rnoon.medi": 64419,
        "hehyeh.isol": 64420,
        "hehyeh.fina": 64421,
        "hehgoal.isol": 64422,
        "hehgoal.fina": 64423,
        "hehgoal.init": 64424,
        "hehgoal.medi": 64425,
        "hehdoachashmee.isol": 64426,
        "hehdoachashmee.fina": 64427,
        "hehdoachashmee.init": 64428,
        "hehdoachashmee.medi": 64429,
        "yehbarree.isol": 64430,
        "yehbarree.fina": 64431,
        "yehbarreehamza.isol": 64432,
        "yehbarreehamza.fina": 64433,
        "symboldotabove": 64434,
        "symboldotbelow": 64435,
        "symboltwodotsabove": 64436,
        "symboltwodotsbelow": 64437,
        "symbolabovethreedotsabove": 64438,
        "symbolbelowthreedotsabove": 64439,
        "symbolpointingabovedownthreedotsabove": 64440,
        "symbolpointingbelowdownthreedotsabove": 64441,
        "symbolfourdotsabove": 64442,
        "symbolfourdotsbelow": 64443,
        "symboldoubleverticalbarbelow": 64444,
        "symboltwodotsverticallyabove": 64445,
        "symboltwodotsverticallybelow": 64446,
        "symbolring": 64447,
        "symboltahabovesmall": 64448,
        "symboltahbelowsmall": 64449,
        "ng.isol": 64467,
        "ng.fina": 64468,
        "ng.init": 64469,
        "ng.medi": 64470,
        "u.isol": 64471,
        "u.fina": 64472,
        "oe.isol": 64473,
        "oe.fina": 64474,
        "yu.isol": 64475,
        "yu.fina": 64476,
        "uhamza.isol": 64477,
        "ve.isol": 64478,
        "ve.fina": 64479,
        "oekirghiz.isol": 64480,
        "oekirghiz.fina": 64481,
        "yukirghiz.isol": 64482,
        "yukirghiz.fina": 64483,
        "e.isol": 64484,
        "e.fina": 64485,
        "e.init": 64486,
        "e.medi": 64487,
        "uighurkazakhkirghizalefmaksura.init": 64488,
        "uighurkazakhkirghizalefmaksura.medi": 64489,
        "yeh.init_hamzaabove.medi_alef.fina": 64490,
        "yeh.medi_hamzaabove.medi_alef.fina": 64491,
        "yeh.init_hamzaabove.medi_ae.fina": 64492,
        "yeh.medi_hamzaabove.medi_ae.fina": 64493,
        "yeh.init_hamzaabove.medi_waw.fina": 64494,
        "yeh.medi_hamzaabove.medi_waw.fina": 64495,
        "yeh.init_hamzaabove.medi_u.fina": 64496,
        "yeh.medi_hamzaabove.medi_u.fina": 64497,
        "yeh.init_hamzaabove.medi_oe.fina": 64498,
        "yeh.medi_hamzaabove.medi_oe.fina": 64499,
        "yeh.init_hamzaabove.medi_yu.fina": 64500,
        "yeh.medi_hamzaabove.medi_yu.fina": 64501,
        "yeh.init_hamzaabove.medi_e.fina": 64502,
        "yeh.medi_hamzaabove.medi_e.fina": 64503,
        "yeh.init_hamzaabove.medi_e.medi": 64504,
        "uighurkirghizyeh.init_hamzaabove.medi_alefmaksura.fina": 64505,
        "uighurkirghizyeh.medi_hamzaabove.medi_alefmaksura.fina": 64506,
        "uighurkirghizyeh.init_hamzaabove.medi_alefmaksura.medi": 64507,
        "yehfarsi.isol": 64508,
        "yehfarsi.fina": 64509,
        "yehfarsi.init": 64510,
        "yehfarsi.medi": 64511,
        "yeh.init_hamzaabove.medi_jeem.fina": 64512,
        "yeh.init_hamzaabove.medi_hah.fina": 64513,
        "yeh.init_hamzaabove.medi_meem.fina": 64514,
        "yeh.init_hamzaabove.medi_alefmaksura.fina": 64515,
        "yeh.init_hamzaabove.medi_yeh.fina": 64516,
        "beh.init_jeem.fina": 64517,
        "beh.init_hah.fina": 64518,
        "beh.init_khah.fina": 64519,
        "beh.init_meem.fina": 64520,
        "beh.init_alefmaksura.fina": 64521,
        "beh.init_yeh.fina": 64522,
        "teh.init_jeem.fina": 64523,
        "teh.init_hah.fina": 64524,
        "teh.init_khah.fina": 64525,
        "teh.init_meem.fina": 64526,
        "teh.init_alefmaksura.fina": 64527,
        "teh.init_yeh.fina": 64528,
        "theh.init_jeem.fina": 64529,
        "theh.init_meem.fina": 64530,
        "theh.init_alefmaksura.fina": 64531,
        "theh.init_yeh.fina": 64532,
        "jeem.init_hah.fina": 64533,
        "jeem.init_meem.fina": 64534,
        "hah.init_jeem.fina": 64535,
        "hah.init_meem.fina": 64536,
        "khah.init_jeem.fina": 64537,
        "khah.init_hah.fina": 64538,
        "khah.init_meem.fina": 64539,
        "seen.init_jeem.fina": 64540,
        "seen.init_hah.fina": 64541,
        "seen.init_khah.fina": 64542,
        "seen.init_meem.fina": 64543,
        "sad.init_hah.fina": 64544,
        "sad.init_meem.fina": 64545,
        "dad.init_jeem.fina": 64546,
        "dad.init_hah.fina": 64547,
        "dad.init_khah.fina": 64548,
        "dad.init_meem.fina": 64549,
        "tah.init_hah.fina": 64550,
        "tah.init_meem.fina": 64551,
        "zah.init_meem.fina": 64552,
        "ain.init_jeem.fina": 64553,
        "ain.init_meem.fina": 64554,
        "ghain.init_jeem.fina": 64555,
        "ghain.init_meem.fina": 64556,
        "feh.init_jeem.fina": 64557,
        "feh.init_hah.fina": 64558,
        "feh.init_khah.fina": 64559,
        "feh.init_meem.fina": 64560,
        "feh.init_alefmaksura.fina": 64561,
        "feh.init_yeh.fina": 64562,
        "qaf.init_hah.fina": 64563,
        "qaf.init_meem.fina": 64564,
        "qaf.init_alefmaksura.fina": 64565,
        "qaf.init_yeh.fina": 64566,
        "kaf.init_alef.fina": 64567,
        "kaf.init_jeem.fina": 64568,
        "kaf.init_hah.fina": 64569,
        "kaf.init_khah.fina": 64570,
        "kaf.init_lam.fina": 64571,
        "kaf.init_meem.fina": 64572,
        "kaf.init_alefmaksura.fina": 64573,
        "kaf.init_yeh.fina": 64574,
        "lam.init_jeem.fina": 64575,
        "lam.init_hah.fina": 64576,
        "lam.init_khah.fina": 64577,
        "lam.init_meem.fina": 64578,
        "lam.init_alefmaksura.fina": 64579,
        "lam.init_yeh.fina": 64580,
        "meem.init_jeem.fina": 64581,
        "meem.init_hah.fina": 64582,
        "meem.init_khah.fina": 64583,
        "meem.init_meem.fina": 64584,
        "meem.init_alefmaksura.fina": 64585,
        "meem.init_yeh.fina": 64586,
        "noon.init_jeem.fina": 64587,
        "noon.init_hah.fina": 64588,
        "noon.init_khah.fina": 64589,
        "noon.init_meem.fina": 64590,
        "noon.init_alefmaksura.fina": 64591,
        "noon.init_yeh.fina": 64592,
        "heh.init_jeem.fina": 64593,
        "heh.init_meem.fina": 64594,
        "heh.init_alefmaksura.fina": 64595,
        "heh.init_yeh.fina": 64596,
        "yeh.init_jeem.fina": 64597,
        "yeh.init_hah.fina": 64598,
        "yeh.init_khah.fina": 64599,
        "yeh.init_meem.fina": 64600,
        "yeh.init_alefmaksura.fina": 64601,
        "yeh.init_yeh.fina": 64602,
        "thal.init_superscriptalef.fina": 64603,
        "reh.init_superscriptalef.fina": 64604,
        "alefmaksura.init_superscriptalef.fina": 64605,
        "shaddaDammatanIsol": 64606,
        "shaddaKasratanIsol": 64607,
        "shaddaFathaIsol": 64608,
        "shaddaDammaIsol": 64609,
        "shaddaKasraIsol": 64610,
        "shaddaAlefIsol": 64611,
        "yeh.medi_hamzaabove.medi_reh.fina": 64612,
        "yeh.medi_hamzaabove.medi_zain.fina": 64613,
        "yeh.medi_hamzaabove.medi_meem.fina": 64614,
        "yeh.medi_hamzaabove.medi_noon.fina": 64615,
        "yeh.medi_hamzaabove.medi_alefmaksura.fina": 64616,
        "yeh.medi_hamzaabove.medi_yeh.fina": 64617,
        "beh.medi_reh.fina": 64618,
        "beh.medi_zain.fina": 64619,
        "beh.medi_meem.fina": 64620,
        "beh.medi_noon.fina": 64621,
        "beh.medi_alefmaksura.fina": 64622,
        "beh.medi_yeh.fina": 64623,
        "teh.medi_reh.fina": 64624,
        "teh.medi_zain.fina": 64625,
        "teh.medi_meem.fina": 64626,
        "teh.medi_noon.fina": 64627,
        "teh.medi_alefmaksura.fina": 64628,
        "teh.medi_yeh.fina": 64629,
        "theh.medi_reh.fina": 64630,
        "theh.medi_zain.fina": 64631,
        "theh.medi_meem.fina": 64632,
        "theh.medi_noon.fina": 64633,
        "theh.medi_alefmaksura.fina": 64634,
        "theh.medi_yeh.fina": 64635,
        "feh.medi_alefmaksura.fina": 64636,
        "feh.medi_yeh.fina": 64637,
        "qaf.medi_alefmaksura.fina": 64638,
        "qaf.medi_yeh.fina": 64639,
        "kaf.medi_alef.fina": 64640,
        "kaf.medi_lam.fina": 64641,
        "kaf.medi_meem.fina": 64642,
        "kaf.medi_alefmaksura.fina": 64643,
        "kaf.medi_yeh.fina": 64644,
        "lam.medi_meem.fina": 64645,
        "lam.medi_alefmaksura.fina": 64646,
        "lam.medi_yeh.fina": 64647,
        "meem.medi_alef.fina": 64648,
        "meem.medi_meem.fina": 64649,
        "noon.medi_reh.fina": 64650,
        "noon.medi_zain.fina": 64651,
        "noon.medi_meem.fina": 64652,
        "noon.medi_noon.fina": 64653,
        "noon.medi_alefmaksura.fina": 64654,
        "noon.medi_yeh.fina": 64655,
        "alefmaksura.medi_superscriptalef.fina": 64656,
        "yeh.medi_reh.fina": 64657,
        "yeh.medi_zain.fina": 64658,
        "yeh.medi_meem.fina": 64659,
        "yeh.medi_noon.fina": 64660,
        "yeh.medi_alefmaksura.fina": 64661,
        "yeh.medi_yeh.fina": 64662,
        "yeh.init_hamzaabove.medi_jeem.medi": 64663,
        "yeh.init_hamzaabove.medi_hah.medi": 64664,
        "yeh.init_hamzaabove.medi_khah.medi": 64665,
        "yeh.init_hamzaabove.medi_meem.medi": 64666,
        "yeh.init_hamzaabove.medi_heh.medi": 64667,
        "beh.init_jeem.medi": 64668,
        "beh.init_hah.medi": 64669,
        "beh.init_khah.medi": 64670,
        "beh.init_meem.medi": 64671,
        "beh.init_heh.medi": 64672,
        "teh.init_jeem.medi": 64673,
        "teh.init_hah.medi": 64674,
        "teh.init_khah.medi": 64675,
        "teh.init_meem.medi": 64676,
        "teh.init_heh.medi": 64677,
        "theh.init_meem.medi": 64678,
        "jeem.init_hah.medi": 64679,
        "jeem.init_meem.medi": 64680,
        "hah.init_jeem.medi": 64681,
        "hah.init_meem.medi": 64682,
        "khah.init_jeem.medi": 64683,
        "khah.init_meem.medi": 64684,
        "seen.init_jeem.medi": 64685,
        "seen.init_hah.medi": 64686,
        "seen.init_khah.medi": 64687,
        "seen.init_meem.medi": 64688,
        "sad.init_hah.medi": 64689,
        "sad.init_khah.medi": 64690,
        "sad.init_meem.medi": 64691,
        "dad.init_jeem.medi": 64692,
        "dad.init_hah.medi": 64693,
        "dad.init_khah.medi": 64694,
        "dad.init_meem.medi": 64695,
        "tah.init_hah.medi": 64696,
        "zah.init_meem.medi": 64697,
        "ain.init_jeem.medi": 64698,
        "ain.init_meem.medi": 64699,
        "ghain.init_jeem.medi": 64700,
        "ghain.init_meem.medi": 64701,
        "feh.init_jeem.medi": 64702,
        "feh.init_hah.medi": 64703,
        "feh.init_khah.medi": 64704,
        "feh.init_meem.medi": 64705,
        "qaf.init_hah.medi": 64706,
        "qaf.init_meem.medi": 64707,
        "kaf.init_jeem.medi": 64708,
        "kaf.init_hah.medi": 64709,
        "kaf.init_khah.medi": 64710,
        "kaf.init_lam.medi": 64711,
        "kaf.init_meem.medi": 64712,
        "lam.init_jeem.medi": 64713,
        "lam.init_hah.medi": 64714,
        "lam.init_khah.medi": 64715,
        "lam.init_meem.medi": 64716,
        "lam.init_heh.medi": 64717,
        "meem.init_jeem.medi": 64718,
        "meem.init_hah.medi": 64719,
        "meem.init_khah.medi": 64720,
        "meem.init_meem.medi": 64721,
        "noon.init_jeem.medi": 64722,
        "noon.init_hah.medi": 64723,
        "noon.init_khah.medi": 64724,
        "noon.init_meem.medi": 64725,
        "noon.init_heh.medi": 64726,
        "heh.init_jeem.medi": 64727,
        "heh.init_meem.medi": 64728,
        "heh.init_superscriptalef.medi": 64729,
        "yeh.init_jeem.medi": 64730,
        "yeh.init_hah.medi": 64731,
        "yeh.init_khah.medi": 64732,
        "yeh.init_meem.medi": 64733,
        "yeh.init_heh.medi": 64734,
        "yeh.medi_hamzaabove.medi_meem.medi": 64735,
        "yeh.medi_hamzaabove.medi_heh.medi": 64736,
        "beh.medi_meem.medi": 64737,
        "beh.medi_heh.medi": 64738,
        "teh.medi_meem.medi": 64739,
        "teh.medi_heh.medi": 64740,
        "theh.medi_meem.medi": 64741,
        "theh.medi_heh.medi": 64742,
        "seen.medi_meem.medi": 64743,
        "seen.medi_heh.medi": 64744,
        "sheen.medi_meem.medi": 64745,
        "sheen.medi_heh.medi": 64746,
        "kaf.medi_lam.medi": 64747,
        "kaf.medi_meem.medi": 64748,
        "lam.medi_meem.medi": 64749,
        "noon.medi_meem.medi": 64750,
        "noon.medi_heh.medi": 64751,
        "yeh.medi_meem.medi": 64752,
        "yeh.medi_heh.medi": 64753,
        "shaddaFathaMedi": 64754,
        "shaddaDammaMedi": 64755,
        "shaddaKasraMedi": 64756,
        "tah.init_alefmaksura.fina": 64757,
        "tah.init_yeh.fina": 64758,
        "ain.init_alefmaksura.fina": 64759,
        "ain.init_yeh.fina": 64760,
        "ghain.init_alefmaksura.fina": 64761,
        "ghain.init_yeh.fina": 64762,
        "seen.init_alefmaksura.fina": 64763,
        "seen.init_yeh.fina": 64764,
        "sheen.init_alefmaksura.fina": 64765,
        "sheen.init_yeh.fina": 64766,
        "hah.init_alefmaksura.fina": 64767,
        "hah.init_yeh.fina": 64768,
        "jeem.init_alefmaksura.fina": 64769,
        "jeem.init_yeh.fina": 64770,
        "khah.init_alefmaksura.fina": 64771,
        "khah.init_yeh.fina": 64772,
        "sad.init_alefmaksura.fina": 64773,
        "sad.init_yeh.fina": 64774,
        "dad.init_alefmaksura.fina": 64775,
        "dad.init_yeh.fina": 64776,
        "sheen.init_jeem.fina": 64777,
        "sheen.init_hah.fina": 64778,
        "sheen.init_khah.fina": 64779,
        "sheen.init_meem.fina": 64780,
        "sheen.init_reh.fina": 64781,
        "seen.init_reh.fina": 64782,
        "sad.init_reh.fina": 64783,
        "dad.init_reh.fina": 64784,
        "tah.medi_alefmaksura.fina": 64785,
        "tah.medi_yeh.fina": 64786,
        "ain.medi_alefmaksura.fina": 64787,
        "ain.medi_yeh.fina": 64788,
        "ghain.medi_alefmaksura.fina": 64789,
        "ghain.medi_yeh.fina": 64790,
        "seen.medi_alefmaksura.fina": 64791,
        "seen.medi_yeh.fina": 64792,
        "sheen.medi_alefmaksura.fina": 64793,
        "sheen.medi_yeh.fina": 64794,
        "hah.medi_alefmaksura.fina": 64795,
        "hah.medi_yeh.fina": 64796,
        "jeem.medi_alefmaksura.fina": 64797,
        "jeem.medi_yeh.fina": 64798,
        "khah.medi_alefmaksura.fina": 64799,
        "khah.medi_yeh.fina": 64800,
        "sad.medi_alefmaksura.fina": 64801,
        "sad.medi_yeh.fina": 64802,
        "dad.medi_alefmaksura.fina": 64803,
        "dad.medi_yeh.fina": 64804,
        "sheen.medi_jeem.fina": 64805,
        "sheen.medi_hah.fina": 64806,
        "sheen.medi_khah.fina": 64807,
        "sheen.medi_meem.fina": 64808,
        "sheen.medi_reh.fina": 64809,
        "seen.medi_reh.fina": 64810,
        "sad.medi_reh.fina": 64811,
        "dad.medi_reh.fina": 64812,
        "sheen.init_jeem.medi": 64813,
        "sheen.init_hah.medi": 64814,
        "sheen.init_khah.medi": 64815,
        "sheen.init_meem.medi": 64816,
        "seen.init_heh.medi": 64817,
        "sheen.init_heh.medi": 64818,
        "tah.init_meem.medi": 64819,
        "seen.medi_jeem.medi": 64820,
        "seen.medi_hah.medi": 64821,
        "seen.medi_khah.medi": 64822,
        "sheen.medi_jeem.medi": 64823,
        "sheen.medi_hah.medi": 64824,
        "sheen.medi_khah.medi": 64825,
        "tah.medi_meem.medi": 64826,
        "zah.medi_meem.medi": 64827,
        "alef.medi_fathatan.fina": 64828,
        "alef.init_fathatan.fina": 64829,
        "ornateleftparenthesis": 64830,
        "ornaterightparenthesis": 64831,
        "teh.init_jeem.medi_meem.medi": 64848,
        "teh.medi_hah.medi_jeem.fina": 64849,
        "teh.init_hah.medi_jeem.medi": 64850,
        "teh.init_hah.medi_meem.medi": 64851,
        "teh.init_khah.medi_meem.medi": 64852,
        "teh.init_meem.medi_jeem.medi": 64853,
        "teh.init_meem.medi_hah.medi": 64854,
        "teh.init_meem.medi_khah.medi": 64855,
        "jeem.medi_meem.medi_hah.fina": 64856,
        "jeem.init_meem.medi_hah.medi": 64857,
        "hah.medi_meem.medi_yeh.fina": 64858,
        "hah.medi_meem.medi_alefmaksura.fina": 64859,
        "seen.init_hah.medi_jeem.medi": 64860,
        "seen.init_jeem.medi_hah.medi": 64861,
        "seen.medi_jeem.medi_alefmaksura.fina": 64862,
        "seen.medi_meem.medi_hah.fina": 64863,
        "seen.init_meem.medi_hah.medi": 64864,
        "seen.init_meem.medi_jeem.medi": 64865,
        "seen.medi_meem.medi_meem.fina": 64866,
        "seen.init_meem.medi_meem.medi": 64867,
        "sad.medi_hah.medi_hah.fina": 64868,
        "sad.init_hah.medi_hah.medi": 64869,
        "sad.medi_meem.medi_meem.fina": 64870,
        "sheen.medi_hah.medi_meem.fina": 64871,
        "sheen.init_hah.medi_meem.medi": 64872,
        "sheen.medi_jeem.medi_yeh.fina": 64873,
        "sheen.medi_meem.medi_khah.fina": 64874,
        "sheen.init_meem.medi_khah.medi": 64875,
        "sheen.medi_meem.medi_meem.fina": 64876,
        "sheen.init_meem.medi_meem.medi": 64877,
        "dad.medi_hah.medi_alefmaksura.fina": 64878,
        "dad.medi_khah.medi_meem.fina": 64879,
        "dad.init_khah.medi_meem.medi": 64880,
        "tah.medi_meem.medi_hah.fina": 64881,
        "tah.init_meem.medi_hah.medi": 64882,
        "tah.init_meem.medi_meem.medi": 64883,
        "tah.medi_meem.medi_yeh.fina": 64884,
        "ain.medi_jeem.medi_meem.fina": 64885,
        "ain.medi_meem.medi_meem.fina": 64886,
        "ain.init_meem.medi_meem.medi": 64887,
        "ain.medi_meem.medi_alefmaksura.fina": 64888,
        "ghain.medi_meem.medi_meem.fina": 64889,
        "ghain.medi_meem.medi_yeh.fina": 64890,
        "ghain.medi_meem.medi_alefmaksura.fina": 64891,
        "feh.medi_khah.medi_meem.fina": 64892,
        "feh.init_khah.medi_meem.medi": 64893,
        "qaf.medi_meem.medi_hah.fina": 64894,
        "qaf.medi_meem.medi_meem.fina": 64895,
        "lam.medi_hah.medi_meem.fina": 64896,
        "lam.medi_hah.medi_yeh.fina": 64897,
        "lam.medi_hah.medi_alefmaksura.fina": 64898,
        "lam.init_jeem.medi_jeem.medi": 64899,
        "lam.medi_jeem.medi_jeem.fina": 64900,
        "lam.medi_khah.medi_meem.fina": 64901,
        "lam.init_khah.medi_meem.medi": 64902,
        "lam.medi_meem.medi_hah.fina": 64903,
        "lam.init_meem.medi_hah.medi": 64904,
        "meem.init_hah.medi_jeem.medi": 64905,
        "meem.init_hah.medi_meem.medi": 64906,
        "meem.medi_hah.medi_yeh.fina": 64907,
        "meem.init_jeem.medi_hah.medi": 64908,
        "meem.init_jeem.medi_meem.medi": 64909,
        "meem.init_khah.medi_jeem.medi": 64910,
        "meem.init_khah.medi_meem.medi": 64911,
        "meem.init_jeem.medi_khah.medi": 64914,
        "heh.init_meem.medi_jeem.medi": 64915,
        "heh.init_meem.medi_meem.medi": 64916,
        "noon.init_hah.medi_meem.medi": 64917,
        "noon.medi_hah.medi_alefmaksura.fina": 64918,
        "noon.medi_jeem.medi_meem.fina": 64919,
        "noon.init_jeem.medi_meem.medi": 64920,
        "noon.medi_jeem.medi_alefmaksura.fina": 64921,
        "noon.medi_meem.medi_yeh.fina": 64922,
        "noon.medi_meem.medi_alefmaksura.fina": 64923,
        "yeh.medi_meem.medi_meem.fina": 64924,
        "yeh.init_meem.medi_meem.medi": 64925,
        "beh.medi_khah.medi_yeh.fina": 64926,
        "teh.medi_jeem.medi_yeh.fina": 64927,
        "teh.medi_jeem.medi_alefmaksura.fina": 64928,
        "teh.medi_khah.medi_yeh.fina": 64929,
        "teh.medi_khah.medi_alefmaksura.fina": 64930,
        "teh.medi_meem.medi_yeh.fina": 64931,
        "teh.medi_meem.medi_alefmaksura.fina": 64932,
        "jeem.medi_meem.medi_yeh.fina": 64933,
        "jeem.medi_hah.medi_alefmaksura.fina": 64934,
        "jeem.medi_meem.medi_alefmaksura.fina": 64935,
        "seen.medi_khah.medi_alefmaksura.fina": 64936,
        "sad.medi_hah.medi_yeh.fina": 64937,
        "sheen.medi_hah.medi_yeh.fina": 64938,
        "dad.medi_hah.medi_yeh.fina": 64939,
        "lam.medi_jeem.medi_yeh.fina": 64940,
        "lam.medi_meem.medi_yeh.fina": 64941,
        "yeh.medi_hah.medi_yeh.fina": 64942,
        "yeh.medi_jeem.medi_yeh.fina": 64943,
        "yeh.medi_meem.medi_yeh.fina": 64944,
        "meem.medi_meem.medi_yeh.fina": 64945,
        "qaf.medi_meem.medi_yeh.fina": 64946,
        "noon.medi_hah.medi_yeh.fina": 64947,
        "qaf.init_meem.medi_hah.medi": 64948,
        "lam.init_hah.medi_meem.medi": 64949,
        "ain.medi_meem.medi_yeh.fina": 64950,
        "kaf.medi_meem.medi_yeh.fina": 64951,
        "noon.init_jeem.medi_hah.medi": 64952,
        "meem.medi_khah.medi_yeh.fina": 64953,
        "lam.init_jeem.medi_meem.medi": 64954,
        "kaf.medi_meem.medi_meem.fina": 64955,
        "lam.medi_jeem.medi_meem.fina": 64956,
        "noon.medi_jeem.medi_hah.fina": 64957,
        "jeem.medi_hah.medi_yeh.fina": 64958,
        "hah.medi_jeem.medi_yeh.fina": 64959,
        "meem.medi_jeem.medi_yeh.fina": 64960,
        "feh.medi_meem.medi_yeh.fina": 64961,
        "beh.medi_hah.medi_yeh.fina": 64962,
        "kaf.init_meem.medi_meem.medi": 64963,
        "ain.init_jeem.medi_meem.medi": 64964,
        "sad.init_meem.medi_meem.medi": 64965,
        "seen.medi_khah.medi_yeh.fina": 64966,
        "noon.medi_jeem.medi_yeh.fina": 64967,
        "SallaUsedAsKoranicStopSign": 65008,
        "QalaUsedAsKoranicStopSign": 65009,
        "Allah": 65010,
        "Akbar": 65011,
        "Mohammad": 65012,
        "Salam": 65013,
        "Rasoul": 65014,
        "Alayhe": 65015,
        "Wasallam": 65016,
        "Salla": 65017,
        "SallallahouAlayheWasallam": 65018,
        "Jallajalalouhou": 65019,
        "rial": 65020,
        "BismillahArRahmanArRaheem": 65021,
        "vert:comma": 65040,
        "vert:ideographiccomma": 65041,
        "vert:ideographicfullstop": 65042,
        "vert:colon": 65043,
        "vert:semicolon": 65044,
        "vert:exclam": 65045,
        "vert:question": 65046,
        "vert:bracketwhiteleft": 65047,
        "vert:brakcetwhiteright": 65048,
        "vert:ellipsishor": 65049,
        "twodotleadervertical": 65072,
        "emdashvertical": 65073,
        "endashvertical": 65074,
        "underscorevertical": 65075,
        "underscorewavyvertical": 65076,
        "parenleftvertical": 65077,
        "parenrightvertical": 65078,
        "braceleftvertical": 65079,
        "bracerightvertical": 65080,
        "tortoiseshellbracketleftvertical": 65081,
        "tortoiseshellbracketrightvertical": 65082,
        "blacklenticularbracketleftvertical": 65083,
        "blacklenticularbracketrightvertical": 65084,
        "dblanglebracketleftvertical": 65085,
        "dblanglebracketrightvertical": 65086,
        "anglebracketleftvertical": 65087,
        "anglebracketrightvertical": 65088,
        "cornerbracketleftvertical": 65089,
        "cornerbracketrightvertical": 65090,
        "whitecornerbracketleftvertical": 65091,
        "whitecornerbracketrightvertical": 65092,
        "sesamedot": 65093,
        "whitesesamedot": 65094,
        "squarebracketleftvertical": 65095,
        "squarebracketrightvertical": 65096,
        "overlinedashed": 65097,
        "overlinecenterline": 65098,
        "overlinewavy": 65099,
        "overlinedblwavy": 65100,
        "underscoredashed": 65101,
        "underscorecenterline": 65102,
        "underscorewavy": 65103,
        "commasmall": 65104,
        "ideographiccommasmall": 65105,
        "periodsmall": 65106,
        "semicolonsmall": 65108,
        "colonsmall": 65109,
        "questionsmall": 65110,
        "exclamsmall": 65111,
        "emdashsmall": 65112,
        "parenthesisleftsmall": 65113,
        "parenthesisrightsmall": 65114,
        "braceleftsmall": 65115,
        "bracerightsmall": 65116,
        "tortoiseshellbracketleftsmall": 65117,
        "tortoiseshellbracketrightsmall": 65118,
        "numbersignsmall": 65119,
        "ampersandsmall": 65120,
        "asterisksmall": 65121,
        "plussmall": 65122,
        "hyphensmall": 65123,
        "lesssmall": 65124,
        "greatersmall": 65125,
        "equalsmall": 65126,
        "backslashsmall": 65128,
        "dollarsmall": 65129,
        "percentsmall": 65130,
        "commercialatsmall": 65131,
        "fathatanIsol": 65136,
        "tatweelFathatanAbove": 65137,
        "dammatanIsol": 65138,
        "kashidaFina": 65139,
        "kasratanIsol": 65140,
        "fathaIsol": 65142,
        "fathaMedi": 65143,
        "dammaIsol": 65144,
        "dammaMedi": 65145,
        "kasraIsol": 65146,
        "kasraMedi": 65147,
        "shaddaIsol": 65148,
        "shaddaMedi": 65149,
        "sukunIsol": 65150,
        "sukunMedi": 65151,
        "hamzaIsol": 65152,
        "alefmadda.isol": 65153,
        "alefmadda.fina": 65154,
        "alefhamza.isol": 65155,
        "alefhamza.fina": 65156,
        "wawhamza.isol": 65157,
        "wawhamza.fina": 65158,
        "alefhamzabelow.isol": 65159,
        "alefhamzabelow.fina": 65160,
        "yehhamza.isol": 65161,
        "yehhamza.fina": 65162,
        "yehhamza.init": 65163,
        "yehhamza.medi": 65164,
        "alef.isol": 65165,
        "alef.fina": 65166,
        "beh.isol": 65167,
        "beh.fina": 65168,
        "beh.init": 65169,
        "beh.medi": 65170,
        "tehmarbuta.isol": 65171,
        "tehmarbuta.fina": 65172,
        "teh.isol": 65173,
        "teh.fina": 65174,
        "teh.init": 65175,
        "teh.medi": 65176,
        "theh.isol": 65177,
        "theh.fina": 65178,
        "theh.init": 65179,
        "theh.medi": 65180,
        "jeem.isol": 65181,
        "jeem.fina": 65182,
        "jeem.init": 65183,
        "jeem.medi": 65184,
        "hah.isol": 65185,
        "hah.fina": 65186,
        "hah.init": 65187,
        "hah.medi": 65188,
        "khah.isol": 65189,
        "khah.fina": 65190,
        "khah.init": 65191,
        "khah.medi": 65192,
        "dal.isol": 65193,
        "dal.fina": 65194,
        "thal.isol": 65195,
        "thal.fina": 65196,
        "reh.isol": 65197,
        "reh.fina": 65198,
        "zain.isol": 65199,
        "zain.fina": 65200,
        "seen.isol": 65201,
        "seen.fina": 65202,
        "seen.init": 65203,
        "seen.medi": 65204,
        "sheen.isol": 65205,
        "sheen.fina": 65206,
        "sheen.init": 65207,
        "sheen.medi": 65208,
        "sad.isol": 65209,
        "sad.fina": 65210,
        "sad.init": 65211,
        "sad.medi": 65212,
        "dad.isol": 65213,
        "dad.fina": 65214,
        "dad.init": 65215,
        "dad.medi": 65216,
        "tah.isol": 65217,
        "tah.fina": 65218,
        "tah.init": 65219,
        "tah.medi": 65220,
        "zah.isol": 65221,
        "zah.fina": 65222,
        "zah.init": 65223,
        "zah.medi": 65224,
        "ain.isol": 65225,
        "ain.fina": 65226,
        "ain.init": 65227,
        "ain.medi": 65228,
        "ghain.isol": 65229,
        "ghain.fina": 65230,
        "ghain.init": 65231,
        "ghain.medi": 65232,
        "feh.isol": 65233,
        "feh.fina": 65234,
        "feh.init": 65235,
        "feh.medi": 65236,
        "qaf.isol": 65237,
        "qaf.fina": 65238,
        "qaf.init": 65239,
        "qaf.medi": 65240,
        "kaf.isol": 65241,
        "kaf.fina": 65242,
        "kaf.init": 65243,
        "kaf.medi": 65244,
        "lam.isol": 65245,
        "lam.fina": 65246,
        "lam.init": 65247,
        "lam.medi": 65248,
        "meem.isol": 65249,
        "meem.fina": 65250,
        "meem.init": 65251,
        "meem.medi": 65252,
        "noon.isol": 65253,
        "noon.fina": 65254,
        "noon.init": 65255,
        "noon.medi": 65256,
        "heh.isol": 65257,
        "heh.fina": 65258,
        "heh.init": 65259,
        "heh.medi": 65260,
        "waw.isol": 65261,
        "waw.fina": 65262,
        "alefmaksura.isol": 65263,
        "alefmaksura.fina": 65264,
        "yeh.isol": 65265,
        "yeh.fina": 65266,
        "yeh.init": 65267,
        "yeh.medi": 65268,
        "lam.init_alef.medi_maddaabove.fina": 65269,
        "lam.medi_alef.medi_maddaabove.fina": 65270,
        "lam.init_alef.medi_hamzaabove.fina": 65271,
        "lam.medi_alef.medi_hamzaabove.fina": 65272,
        "lam.init_alef.medi_hamzabelow.fina": 65273,
        "lam.medi_alef.medi_hamzabelow.fina": 65274,
        "lam.init_alef.fina": 65275,
        "lam.medi_alef.fina": 65276,
        "zerowidthnobreakspace": 65279,
        "fwd:exclam": 65281,
        "fwd:quotedbl": 65282,
        "fwd:numbersign": 65283,
        "fwd:dollar": 65284,
        "fwd:percent": 65285,
        "fwd:ampersand": 65286,
        "fwd:quotesingle": 65287,
        "fwd:parenthesisleft": 65288,
        "fwd:parenthesisright": 65289,
        "fwd:asterisk": 65290,
        "fwd:plus": 65291,
        "fwd:comma": 65292,
        "fwd:hyphen": 65293,
        "fwd:period": 65294,
        "fwd:slash": 65295,
        "fwd:zero": 65296,
        "fwd:one": 65297,
        "fwd:two": 65298,
        "fwd:three": 65299,
        "fwd:four": 65300,
        "fwd:five": 65301,
        "fwd:six": 65302,
        "fwd:seven": 65303,
        "fwd:eight": 65304,
        "fwd:nine": 65305,
        "fwd:colon": 65306,
        "fwd:semicolon": 65307,
        "fwd:less": 65308,
        "fwd:equal": 65309,
        "fwd:greater": 65310,
        "fwd:question": 65311,
        "fwd:at": 65312,
        "fwd:A": 65313,
        "fwd:B": 65314,
        "fwd:C": 65315,
        "fwd:D": 65316,
        "fwd:E": 65317,
        "fwd:F": 65318,
        "fwd:G": 65319,
        "fwd:H": 65320,
        "fwd:I": 65321,
        "fwd:J": 65322,
        "fwd:K": 65323,
        "fwd:L": 65324,
        "fwd:M": 65325,
        "fwd:N": 65326,
        "fwd:O": 65327,
        "fwd:P": 65328,
        "fwd:Q": 65329,
        "fwd:R": 65330,
        "fwd:S": 65331,
        "fwd:T": 65332,
        "fwd:U": 65333,
        "fwd:V": 65334,
        "fwd:W": 65335,
        "fwd:X": 65336,
        "fwd:Y": 65337,
        "fwd:Z": 65338,
        "fwd:bracketleft": 65339,
        "fwd:backslash": 65340,
        "fwd:bracketright": 65341,
        "fwd:asciicircum": 65342,
        "fwd:underscore": 65343,
        "fwd:grave": 65344,
        "fwd:a": 65345,
        "fwd:b": 65346,
        "fwd:c": 65347,
        "fwd:d": 65348,
        "fwd:e": 65349,
        "fwd:f": 65350,
        "fwd:g": 65351,
        "fwd:h": 65352,
        "fwd:i": 65353,
        "fwd:j": 65354,
        "fwd:k": 65355,
        "fwd:l": 65356,
        "fwd:m": 65357,
        "fwd:n": 65358,
        "fwd:o": 65359,
        "fwd:p": 65360,
        "fwd:q": 65361,
        "fwd:r": 65362,
        "fwd:s": 65363,
        "fwd:t": 65364,
        "fwd:u": 65365,
        "fwd:v": 65366,
        "fwd:w": 65367,
        "fwd:x": 65368,
        "fwd:y": 65369,
        "fwd:z": 65370,
        "fwd:braceleft": 65371,
        "fwd:bar": 65372,
        "fwd:braceright": 65373,
        "fwd:asciitilde": 65374,
        "fwd:leftwhiteparenthesis": 65375,
        "fwd:rightwhiteparenthesis": 65376,
        "hwd:ideographicfullstop": 65377,
        "hwd:leftcornerbracket": 65378,
        "hwd:rightcornerbracket": 65379,
        "hwd:ideographiccomma": 65380,
        "hwd:kata:middledot": 65381,
        "hwd:kata:wo": 65382,
        "hwd:kata:asmall": 65383,
        "hwd:kata:ismall": 65384,
        "hwd:kata:usmall": 65385,
        "hwd:kata:esmall": 65386,
        "hwd:kata:osmall": 65387,
        "hwd:kata:yasmall": 65388,
        "hwd:kata:yusmall": 65389,
        "hwd:kata:yosmall": 65390,
        "hwd:kata:tusmall": 65391,
        "hwd:kata:prolongedkana": 65392,
        "hwd:kata:a": 65393,
        "hwd:kata:i": 65394,
        "hwd:kata:u": 65395,
        "hwd:kata:e": 65396,
        "hwd:kata:o": 65397,
        "hwd:kata:ka": 65398,
        "hwd:kata:ki": 65399,
        "hwd:kata:ku": 65400,
        "hwd:kata:ke": 65401,
        "hwd:kata:ko": 65402,
        "hwd:kata:sa": 65403,
        "hwd:kata:si": 65404,
        "hwd:kata:su": 65405,
        "hwd:kata:se": 65406,
        "hwd:kata:so": 65407,
        "hwd:kata:ta": 65408,
        "hwd:kata:ti": 65409,
        "hwd:kata:tu": 65410,
        "hwd:kata:te": 65411,
        "hwd:kata:to": 65412,
        "hwd:kata:na": 65413,
        "hwd:kata:ni": 65414,
        "hwd:kata:nu": 65415,
        "hwd:kata:ne": 65416,
        "hwd:kata:no": 65417,
        "hwd:kata:ha": 65418,
        "hwd:kata:hi": 65419,
        "hwd:kata:hu": 65420,
        "hwd:kata:he": 65421,
        "hwd:kata:ho": 65422,
        "hwd:kata:ma": 65423,
        "hwd:kata:mi": 65424,
        "hwd:kata:mu": 65425,
        "hwd:kata:me": 65426,
        "hwd:kata:mo": 65427,
        "hwd:kata:ya": 65428,
        "hwd:kata:yu": 65429,
        "hwd:kata:yo": 65430,
        "hwd:kata:ra": 65431,
        "hwd:kata:ri": 65432,
        "hwd:kata:ru": 65433,
        "hwd:kata:re": 65434,
        "hwd:kata:ro": 65435,
        "hwd:kata:wa": 65436,
        "hwd:kata:n": 65437,
        "hwd:kata:voiced": 65438,
        "hwd:kata:semi-voiced": 65439,
        "hwd:hangulfiller": 65440,
        "hwd:kiyeok": 65441,
        "hwd:ssangkiyeok": 65442,
        "hwd:kiyeoksios": 65443,
        "hwd:nieun": 65444,
        "hwd:nieuncieuc": 65445,
        "hwd:nieunhieuh": 65446,
        "hwd:tikeut": 65447,
        "hwd:ssangtikeut": 65448,
        "hwd:rieul": 65449,
        "hwd:rieulkiyeok": 65450,
        "hwd:rieulmieum": 65451,
        "hwd:rieulpieup": 65452,
        "hwd:rieulsios": 65453,
        "hwd:rieulthieuth": 65454,
        "hwd:rieulphieuph": 65455,
        "hwd:rieulhieuh": 65456,
        "hwd:mieum": 65457,
        "hwd:pieup": 65458,
        "hwd:ssangpieup": 65459,
        "hwd:pieupsios": 65460,
        "hwd:sios": 65461,
        "hwd:ssangsios": 65462,
        "hwd:ieung": 65463,
        "hwd:cieuc": 65464,
        "hwd:ssangcieuc": 65465,
        "hwd:chieuch": 65466,
        "hwd:khieukh": 65467,
        "hwd:thieuth": 65468,
        "hwd:phieuph": 65469,
        "hwd:hieuh": 65470,
        "hwd:a": 65474,
        "hwd:ae": 65475,
        "hwd:ya": 65476,
        "hwd:yae": 65477,
        "hwd:eo": 65478,
        "hwd:e": 65479,
        "hwd:yeo": 65482,
        "hwd:ye": 65483,
        "hwd:o": 65484,
        "hwd:wa": 65485,
        "hwd:wae": 65486,
        "hwd:oe": 65487,
        "hwd:yo": 65490,
        "hwd:u": 65491,
        "hwd:weo": 65492,
        "hwd:we": 65493,
        "hwd:wi": 65494,
        "hwd:yu": 65495,
        "hwd:eu": 65498,
        "hwd:yi": 65499,
        "hwd:i": 65500,
        "fwd:centsign": 65504,
        "fwd:poundsign": 65505,
        "fwd:notsign": 65506,
        "fwd:macron": 65507,
        "fwd:brokenbar": 65508,
        "fwd:yensign": 65509,
        "fwd:wonsign": 65510,
        "hwd:formslightvertical": 65512,
        "hwd:leftwardsarrow": 65513,
        "hwd:upwardsarrow": 65514,
        "hwd:rightwardsarrow": 65515,
        "hwd:downwardsarrow": 65516,
        "hwd:blacksquare": 65517,
        "hwd:whitecircle": 65518,
        "interlinearanchor": 65529,
        "interlinearseparator": 65530,
        "interlinearterminator": 65531,
        "replacementcharobj": 65532,
        "replacementchar": 65533,
        "zanb:a": 72192,
        "zanb:vowelsigni": 72193,
        "zanb:vowelsignue": 72194,
        "zanb:vowelsignu": 72195,
        "zanb:vowelsigne": 72196,
        "zanb:vowelsignoe": 72197,
        "zanb:vowelsigno": 72198,
        "zanb:vowelsignai": 72199,
        "zanb:vowelsignau": 72200,
        "zanb:vowelsignreversedi": 72201,
        "zanb:vowellengthmark": 72202,
        "zanb:ka": 72203,
        "zanb:kha": 72204,
        "zanb:ga": 72205,
        "zanb:gha": 72206,
        "zanb:nga": 72207,
        "zanb:ca": 72208,
        "zanb:cha": 72209,
        "zanb:ja": 72210,
        "zanb:nya": 72211,
        "zanb:tta": 72212,
        "zanb:ttha": 72213,
        "zanb:dda": 72214,
        "zanb:ddha": 72215,
        "zanb:nna": 72216,
        "zanb:ta": 72217,
        "zanb:tha": 72218,
        "zanb:da": 72219,
        "zanb:dha": 72220,
        "zanb:na": 72221,
        "zanb:pa": 72222,
        "zanb:pha": 72223,
        "zanb:ba": 72224,
        "zanb:bha": 72225,
        "zanb:ma": 72226,
        "zanb:tsa": 72227,
        "zanb:tsha": 72228,
        "zanb:dza": 72229,
        "zanb:dzha": 72230,
        "zanb:zha": 72231,
        "zanb:za": 72232,
        "zanb:dashA": 72233,
        "zanb:ya": 72234,
        "zanb:ra": 72235,
        "zanb:la": 72236,
        "zanb:va": 72237,
        "zanb:sha": 72238,
        "zanb:ssa": 72239,
        "zanb:sa": 72240,
        "zanb:ha": 72241,
        "zanb:kssa": 72242,
        "zanb:finalconsonantmark": 72243,
        "zanb:signvirama": 72244,
        "zanb:signcandrabindu": 72245,
        "zanb:signcandrabinduwithornament": 72246,
        "zanb:signcandrawithornament": 72247,
        "zanb:signanusvara": 72248,
        "zanb:signvisarga": 72249,
        "zanb:raclusterinit": 72250,
        "zanb:yaclusterfina": 72251,
        "zanb:raclusterfina": 72252,
        "zanb:laclusterfina": 72253,
        "zanb:vaclusterfina": 72254,
        "zanb:initialheadmark": 72255,
        "zanb:closingheadmark": 72256,
        "zanb:marktsheg": 72257,
        "zanb:markshad": 72258,
        "zanb:markdoubleshad": 72259,
        "zanb:marklongtsheg": 72260,
        "zanb:initialheadmarkdbllined": 72261,
        "zanb:closingheadmarkdbllined": 72262,
        "zanb:subjoiner": 72263,
        "Abold": 119808,
        "Bbold": 119809,
        "Cbold": 119810,
        "Dbold": 119811,
        "Ebold": 119812,
        "Fbold": 119813,
        "Gbold": 119814,
        "Hbold": 119815,
        "Ibold": 119816,
        "Jbold": 119817,
        "Kbold": 119818,
        "Lbold": 119819,
        "Mbold": 119820,
        "Nbold": 119821,
        "Obold": 119822,
        "Pbold": 119823,
        "Qbold": 119824,
        "Rbold": 119825,
        "Sbold": 119826,
        "Tbold": 119827,
        "Ubold": 119828,
        "Vbold": 119829,
        "Wbold": 119830,
        "Xbold": 119831,
        "Ybold": 119832,
        "Zbold": 119833,
        "abold": 119834,
        "bbold": 119835,
        "cbold": 119836,
        "dbold": 119837,
        "ebold": 119838,
        "fbold": 119839,
        "gbold": 119840,
        "hbold": 119841,
        "ibold": 119842,
        "jbold": 119843,
        "kbold": 119844,
        "lbold": 119845,
        "mbold": 119846,
        "nbold": 119847,
        "obold": 119848,
        "pbold": 119849,
        "qbold": 119850,
        "rbold": 119851,
        "sbold": 119852,
        "tbold": 119853,
        "ubold": 119854,
        "vbold": 119855,
        "wbold": 119856,
        "xbold": 119857,
        "ybold": 119858,
        "zbold": 119859,
        "Aitalic": 119860,
        "Bitalic": 119861,
        "Citalic": 119862,
        "Ditalic": 119863,
        "Eitalic": 119864,
        "Fitalic": 119865,
        "Gitalic": 119866,
        "Hitalic": 119867,
        "Iitalic": 119868,
        "Jitalic": 119869,
        "Kitalic": 119870,
        "Litalic": 119871,
        "Mitalic": 119872,
        "Nitalic": 119873,
        "Oitalic": 119874,
        "Pitalic": 119875,
        "Qitalic": 119876,
        "Ritalic": 119877,
        "Sitalic": 119878,
        "Titalic": 119879,
        "Uitalic": 119880,
        "Vitalic": 119881,
        "Witalic": 119882,
        "Xitalic": 119883,
        "Yitalic": 119884,
        "Zitalic": 119885,
        "aitalic": 119886,
        "bitalic": 119887,
        "citalic": 119888,
        "ditalic": 119889,
        "eitalic": 119890,
        "fitalic": 119891,
        "gitalic": 119892,
        "iitalic": 119894,
        "jitalic": 119895,
        "kitalic": 119896,
        "litalic": 119897,
        "mitalic": 119898,
        "nitalic": 119899,
        "oitalic": 119900,
        "pitalic": 119901,
        "qitalic": 119902,
        "ritalic": 119903,
        "sitalic": 119904,
        "titalic": 119905,
        "uitalic": 119906,
        "vitalic": 119907,
        "witalic": 119908,
        "xitalic": 119909,
        "yitalic": 119910,
        "zitalic": 119911,
        "Abolditalic": 119912,
        "Bbolditalic": 119913,
        "Cbolditalic": 119914,
        "Dbolditalic": 119915,
        "Ebolditalic": 119916,
        "Fbolditalic": 119917,
        "Gbolditalic": 119918,
        "Hbolditalic": 119919,
        "Ibolditalic": 119920,
        "Jbolditalic": 119921,
        "Kbolditalic": 119922,
        "Lbolditalic": 119923,
        "Mbolditalic": 119924,
        "Nbolditalic": 119925,
        "Obolditalic": 119926,
        "Pbolditalic": 119927,
        "Qbolditalic": 119928,
        "Rbolditalic": 119929,
        "Sbolditalic": 119930,
        "Tbolditalic": 119931,
        "Ubolditalic": 119932,
        "Vbolditalic": 119933,
        "Wbolditalic": 119934,
        "Xbolditalic": 119935,
        "Ybolditalic": 119936,
        "Zbolditalic": 119937,
        "abolditalic": 119938,
        "bbolditalic": 119939,
        "cbolditalic": 119940,
        "dbolditalic": 119941,
        "ebolditalic": 119942,
        "fbolditalic": 119943,
        "gbolditalic": 119944,
        "hbolditalic": 119945,
        "ibolditalic": 119946,
        "jbolditalic": 119947,
        "kbolditalic": 119948,
        "lbolditalic": 119949,
        "mbolditalic": 119950,
        "nbolditalic": 119951,
        "obolditalic": 119952,
        "pbolditalic": 119953,
        "qbolditalic": 119954,
        "rbolditalic": 119955,
        "sbolditalic": 119956,
        "tbolditalic": 119957,
        "ubolditalic": 119958,
        "vbolditalic": 119959,
        "wbolditalic": 119960,
        "xbolditalic": 119961,
        "ybolditalic": 119962,
        "zbolditalic": 119963,
        "Ascript": 119964,
        "Cscript": 119966,
        "Dscript": 119967,
        "Gscript": 119970,
        "Jscript": 119973,
        "Kscript": 119974,
        "Nscript": 119977,
        "Oscript": 119978,
        "Pscript": 119979,
        "Qscript": 119980,
        "Sscript": 119982,
        "Tscript": 119983,
        "Uscript": 119984,
        "Vscript": 119985,
        "Wscript": 119986,
        "Xscript": 119987,
        "Yscript": 119988,
        "Zscript": 119989,
        "ascript": 119990,
        "bscript": 119991,
        "cscript": 119992,
        "dscript": 119993,
        "fscript": 119995,
        "hscript": 119997,
        "iscript": 119998,
        "jscript": 119999,
        "kscript": 120000,
        "lscript": 120001,
        "mscript": 120002,
        "nscript": 120003,
        "pscript": 120005,
        "qscript": 120006,
        "math:rscript": 120007,
        "sscript": 120008,
        "tscript": 120009,
        "uscript": 120010,
        "vscript": 120011,
        "wscript": 120012,
        "xscript": 120013,
        "yscript": 120014,
        "zscript": 120015,
        "Aboldscript": 120016,
        "Bboldscript": 120017,
        "Cboldscript": 120018,
        "Dboldscript": 120019,
        "Eboldscript": 120020,
        "Fboldscript": 120021,
        "Gboldscript": 120022,
        "Hboldscript": 120023,
        "Iboldscript": 120024,
        "Jboldscript": 120025,
        "Kboldscript": 120026,
        "Lboldscript": 120027,
        "Mboldscript": 120028,
        "Nboldscript": 120029,
        "Oboldscript": 120030,
        "Pboldscript": 120031,
        "Qboldscript": 120032,
        "Rboldscript": 120033,
        "Sboldscript": 120034,
        "Tboldscript": 120035,
        "Uboldscript": 120036,
        "Vboldscript": 120037,
        "Wboldscript": 120038,
        "Xboldscript": 120039,
        "Yboldscript": 120040,
        "Zboldscript": 120041,
        "aboldscript": 120042,
        "bboldscript": 120043,
        "cboldscript": 120044,
        "dboldscript": 120045,
        "eboldscript": 120046,
        "fboldscript": 120047,
        "gboldscript": 120048,
        "hboldscript": 120049,
        "iboldscript": 120050,
        "jboldscript": 120051,
        "kboldscript": 120052,
        "lboldscript": 120053,
        "mboldscript": 120054,
        "nboldscript": 120055,
        "oboldscript": 120056,
        "pboldscript": 120057,
        "qboldscript": 120058,
        "rboldscript": 120059,
        "sboldscript": 120060,
        "tboldscript": 120061,
        "uboldscript": 120062,
        "vboldscript": 120063,
        "wboldscript": 120064,
        "xboldscript": 120065,
        "yboldscript": 120066,
        "zboldscript": 120067,
        "Afraktur": 120068,
        "Bfraktur": 120069,
        "Dfraktur": 120071,
        "Efraktur": 120072,
        "Ffraktur": 120073,
        "Gfraktur": 120074,
        "Jfraktur": 120077,
        "Kfraktur": 120078,
        "Lfraktur": 120079,
        "Mfraktur": 120080,
        "Nfraktur": 120081,
        "Ofraktur": 120082,
        "Pfraktur": 120083,
        "Qfraktur": 120084,
        "Sfraktur": 120086,
        "Tfraktur": 120087,
        "Ufraktur": 120088,
        "Vfraktur": 120089,
        "Wfraktur": 120090,
        "Xfraktur": 120091,
        "Yfraktur": 120092,
        "afraktur": 120094,
        "bfraktur": 120095,
        "cfraktur": 120096,
        "dfraktur": 120097,
        "efraktur": 120098,
        "ffraktur": 120099,
        "gfraktur": 120100,
        "hfraktur": 120101,
        "ifraktur": 120102,
        "jfraktur": 120103,
        "kfraktur": 120104,
        "lfraktur": 120105,
        "mfraktur": 120106,
        "nfraktur": 120107,
        "ofraktur": 120108,
        "pfraktur": 120109,
        "qfraktur": 120110,
        "rfraktur": 120111,
        "sfraktur": 120112,
        "tfraktur": 120113,
        "ufraktur": 120114,
        "vfraktur": 120115,
        "wfraktur": 120116,
        "xfraktur": 120117,
        "yfraktur": 120118,
        "zfraktur": 120119,
        "Adblstruck": 120120,
        "Bdblstruck": 120121,
        "Ddblstruck": 120123,
        "Edblstruck": 120124,
        "Fdblstruck": 120125,
        "Gdblstruck": 120126,
        "Idblstruck": 120128,
        "Jdblstruck": 120129,
        "Kdblstruck": 120130,
        "Ldblstruck": 120131,
        "Mdblstruck": 120132,
        "Odblstruck": 120134,
        "Sdblstruck": 120138,
        "Tdblstruck": 120139,
        "Udblstruck": 120140,
        "Vdblstruck": 120141,
        "Wdblstruck": 120142,
        "Xdblstruck": 120143,
        "Ydblstruck": 120144,
        "adblstruck": 120146,
        "bdblstruck": 120147,
        "cdblstruck": 120148,
        "ddblstruck": 120149,
        "edblstruck": 120150,
        "fdblstruck": 120151,
        "gdblstruck": 120152,
        "hdblstruck": 120153,
        "idblstruck": 120154,
        "jdblstruck": 120155,
        "kdblstruck": 120156,
        "ldblstruck": 120157,
        "mdblstruck": 120158,
        "ndblstruck": 120159,
        "odblstruck": 120160,
        "pdblstruck": 120161,
        "qdblstruck": 120162,
        "rdblstruck": 120163,
        "sdblstruck": 120164,
        "tdblstruck": 120165,
        "udblstruck": 120166,
        "vdblstruck": 120167,
        "wdblstruck": 120168,
        "xdblstruck": 120169,
        "ydblstruck": 120170,
        "zdblstruck": 120171,
        "Aboldfraktur": 120172,
        "Bboldfraktur": 120173,
        "Cboldfraktur": 120174,
        "Dboldfraktur": 120175,
        "Eboldfraktur": 120176,
        "Fboldfraktur": 120177,
        "Gboldfraktur": 120178,
        "Hboldfraktur": 120179,
        "Iboldfraktur": 120180,
        "Jboldfraktur": 120181,
        "Kboldfraktur": 120182,
        "Lboldfraktur": 120183,
        "Mboldfraktur": 120184,
        "Nboldfraktur": 120185,
        "Oboldfraktur": 120186,
        "Pboldfraktur": 120187,
        "Qboldfraktur": 120188,
        "Rboldfraktur": 120189,
        "Sboldfraktur": 120190,
        "Tboldfraktur": 120191,
        "Uboldfraktur": 120192,
        "Vboldfraktur": 120193,
        "Wboldfraktur": 120194,
        "Xboldfraktur": 120195,
        "Yboldfraktur": 120196,
        "Zboldfraktur": 120197,
        "aboldfraktur": 120198,
        "bboldfraktur": 120199,
        "cboldfraktur": 120200,
        "dboldfraktur": 120201,
        "eboldfraktur": 120202,
        "fboldfraktur": 120203,
        "gboldfraktur": 120204,
        "hboldfraktur": 120205,
        "iboldfraktur": 120206,
        "jboldfraktur": 120207,
        "kboldfraktur": 120208,
        "lboldfraktur": 120209,
        "mboldfraktur": 120210,
        "nboldfraktur": 120211,
        "oboldfraktur": 120212,
        "pboldfraktur": 120213,
        "qboldfraktur": 120214,
        "rboldfraktur": 120215,
        "sboldfraktur": 120216,
        "tboldfraktur": 120217,
        "uboldfraktur": 120218,
        "vboldfraktur": 120219,
        "wboldfraktur": 120220,
        "xboldfraktur": 120221,
        "yboldfraktur": 120222,
        "zboldfraktur": 120223,
        "Asans": 120224,
        "Bsans": 120225,
        "Csans": 120226,
        "Dsans": 120227,
        "Esans": 120228,
        "Fsans": 120229,
        "Gsans": 120230,
        "Hsans": 120231,
        "Isans": 120232,
        "Jsans": 120233,
        "Ksans": 120234,
        "Lsans": 120235,
        "Msans": 120236,
        "Nsans": 120237,
        "Osans": 120238,
        "Psans": 120239,
        "Qsans": 120240,
        "Rsans": 120241,
        "Ssans": 120242,
        "Tsans": 120243,
        "Usans": 120244,
        "Vsans": 120245,
        "Wsans": 120246,
        "Xsans": 120247,
        "Ysans": 120248,
        "Zsans": 120249,
        "asans": 120250,
        "bsans": 120251,
        "csans": 120252,
        "dsans": 120253,
        "esans": 120254,
        "fsans": 120255,
        "gsans": 120256,
        "hsans": 120257,
        "isans": 120258,
        "jsans": 120259,
        "ksans": 120260,
        "lsans": 120261,
        "msans": 120262,
        "nsans": 120263,
        "osans": 120264,
        "psans": 120265,
        "qsans": 120266,
        "rsans": 120267,
        "ssans": 120268,
        "tsans": 120269,
        "usans": 120270,
        "vsans": 120271,
        "wsans": 120272,
        "xsans": 120273,
        "ysans": 120274,
        "zsans": 120275,
        "Aboldsans": 120276,
        "Bboldsans": 120277,
        "Cboldsans": 120278,
        "Dboldsans": 120279,
        "Eboldsans": 120280,
        "Fboldsans": 120281,
        "Gboldsans": 120282,
        "Hboldsans": 120283,
        "Iboldsans": 120284,
        "Jboldsans": 120285,
        "Kboldsans": 120286,
        "Lboldsans": 120287,
        "Mboldsans": 120288,
        "Nboldsans": 120289,
        "Oboldsans": 120290,
        "Pboldsans": 120291,
        "Qboldsans": 120292,
        "Rboldsans": 120293,
        "Sboldsans": 120294,
        "Tboldsans": 120295,
        "Uboldsans": 120296,
        "Vboldsans": 120297,
        "Wboldsans": 120298,
        "Xboldsans": 120299,
        "Yboldsans": 120300,
        "Zboldsans": 120301,
        "aboldsans": 120302,
        "bboldsans": 120303,
        "cboldsans": 120304,
        "dboldsans": 120305,
        "eboldsans": 120306,
        "fboldsans": 120307,
        "gboldsans": 120308,
        "hboldsans": 120309,
        "iboldsans": 120310,
        "jboldsans": 120311,
        "kboldsans": 120312,
        "lboldsans": 120313,
        "mboldsans": 120314,
        "nboldsans": 120315,
        "oboldsans": 120316,
        "pboldsans": 120317,
        "qboldsans": 120318,
        "rboldsans": 120319,
        "sboldsans": 120320,
        "tboldsans": 120321,
        "uboldsans": 120322,
        "vboldsans": 120323,
        "wboldsans": 120324,
        "xboldsans": 120325,
        "yboldsans": 120326,
        "zboldsans": 120327,
        "Aitalicsans": 120328,
        "Bitalicsans": 120329,
        "Citalicsans": 120330,
        "Ditalicsans": 120331,
        "Eitalicsans": 120332,
        "Fitalicsans": 120333,
        "Gitalicsans": 120334,
        "Hitalicsans": 120335,
        "Iitalicsans": 120336,
        "Jitalicsans": 120337,
        "Kitalicsans": 120338,
        "Litalicsans": 120339,
        "Mitalicsans": 120340,
        "Nitalicsans": 120341,
        "Oitalicsans": 120342,
        "Pitalicsans": 120343,
        "Qitalicsans": 120344,
        "Ritalicsans": 120345,
        "Sitalicsans": 120346,
        "Titalicsans": 120347,
        "Uitalicsans": 120348,
        "Vitalicsans": 120349,
        "Witalicsans": 120350,
        "Xitalicsans": 120351,
        "Yitalicsans": 120352,
        "Zitalicsans": 120353,
        "aitalicsans": 120354,
        "bitalicsans": 120355,
        "citalicsans": 120356,
        "ditalicsans": 120357,
        "eitalicsans": 120358,
        "fitalicsans": 120359,
        "gitalicsans": 120360,
        "hitalicsans": 120361,
        "iitalicsans": 120362,
        "jitalicsans": 120363,
        "kitalicsans": 120364,
        "litalicsans": 120365,
        "mitalicsans": 120366,
        "nitalicsans": 120367,
        "oitalicsans": 120368,
        "pitalicsans": 120369,
        "qitalicsans": 120370,
        "ritalicsans": 120371,
        "sitalicsans": 120372,
        "titalicsans": 120373,
        "uitalicsans": 120374,
        "vitalicsans": 120375,
        "witalicsans": 120376,
        "xitalicsans": 120377,
        "yitalicsans": 120378,
        "zitalicsans": 120379,
        "Abolditalicsans": 120380,
        "Bbolditalicsans": 120381,
        "Cbolditalicsans": 120382,
        "Dbolditalicsans": 120383,
        "Ebolditalicsans": 120384,
        "Fbolditalicsans": 120385,
        "Gbolditalicsans": 120386,
        "Hbolditalicsans": 120387,
        "Ibolditalicsans": 120388,
        "Jbolditalicsans": 120389,
        "Kbolditalicsans": 120390,
        "Lbolditalicsans": 120391,
        "Mbolditalicsans": 120392,
        "Nbolditalicsans": 120393,
        "Obolditalicsans": 120394,
        "Pbolditalicsans": 120395,
        "Qbolditalicsans": 120396,
        "Rbolditalicsans": 120397,
        "Sbolditalicsans": 120398,
        "Tbolditalicsans": 120399,
        "Ubolditalicsans": 120400,
        "Vbolditalicsans": 120401,
        "Wbolditalicsans": 120402,
        "Xbolditalicsans": 120403,
        "Ybolditalicsans": 120404,
        "Zbolditalicsans": 120405,
        "abolditalicsans": 120406,
        "bbolditalicsans": 120407,
        "cbolditalicsans": 120408,
        "dbolditalicsans": 120409,
        "ebolditalicsans": 120410,
        "fbolditalicsans": 120411,
        "gbolditalicsans": 120412,
        "hbolditalicsans": 120413,
        "ibolditalicsans": 120414,
        "jbolditalicsans": 120415,
        "kbolditalicsans": 120416,
        "lbolditalicsans": 120417,
        "mbolditalicsans": 120418,
        "nbolditalicsans": 120419,
        "obolditalicsans": 120420,
        "pbolditalicsans": 120421,
        "qbolditalicsans": 120422,
        "rbolditalicsans": 120423,
        "sbolditalicsans": 120424,
        "tbolditalicsans": 120425,
        "ubolditalicsans": 120426,
        "vbolditalicsans": 120427,
        "wbolditalicsans": 120428,
        "xbolditalicsans": 120429,
        "ybolditalicsans": 120430,
        "zbolditalicsans": 120431,
        "Amono": 120432,
        "Bmono": 120433,
        "Cmono": 120434,
        "Dmono": 120435,
        "Emono": 120436,
        "Fmono": 120437,
        "Gmono": 120438,
        "Hmono": 120439,
        "Imono": 120440,
        "Jmono": 120441,
        "Kmono": 120442,
        "Lmono": 120443,
        "Mmono": 120444,
        "Nmono": 120445,
        "Omono": 120446,
        "Pmono": 120447,
        "Qmono": 120448,
        "Rmono": 120449,
        "Smono": 120450,
        "Tmono": 120451,
        "Umono": 120452,
        "Vmono": 120453,
        "Wmono": 120454,
        "Xmono": 120455,
        "Ymono": 120456,
        "Zmono": 120457,
        "amono": 120458,
        "bmono": 120459,
        "cmono": 120460,
        "dmono": 120461,
        "emono": 120462,
        "fmono": 120463,
        "gmono": 120464,
        "hmono": 120465,
        "imono": 120466,
        "jmono": 120467,
        "kmono": 120468,
        "lmono": 120469,
        "mmono": 120470,
        "nmono": 120471,
        "omono": 120472,
        "pmono": 120473,
        "qmono": 120474,
        "rmono": 120475,
        "smono": 120476,
        "tmono": 120477,
        "umono": 120478,
        "vmono": 120479,
        "wmono": 120480,
        "xmono": 120481,
        "ymono": 120482,
        "zmono": 120483,
        "dotlessiitalic": 120484,
        "dotlessjitalic": 120485,
        "Alphabold": 120488,
        "Betabold": 120489,
        "Gammabold": 120490,
        "Deltabold": 120491,
        "Epsilonbold": 120492,
        "Zetabold": 120493,
        "Etabold": 120494,
        "Thetabold": 120495,
        "Iotabold": 120496,
        "Kappabold": 120497,
        "Lamdabold": 120498,
        "Mubold": 120499,
        "Nubold": 120500,
        "Xibold": 120501,
        "Omicronbold": 120502,
        "Pibold": 120503,
        "Rhobold": 120504,
        "Thetasymbolbold": 120505,
        "Sigmabold": 120506,
        "Taubold": 120507,
        "Upsilonbold": 120508,
        "Phibold": 120509,
        "Chibold": 120510,
        "Psibold": 120511,
        "Omegabold": 120512,
        "boldnabla": 120513,
        "alphabold": 120514,
        "betabold": 120515,
        "gammabold": 120516,
        "deltabold": 120517,
        "epsilonbold": 120518,
        "zetabold": 120519,
        "etabold": 120520,
        "thetabold": 120521,
        "iotabold": 120522,
        "kappabold": 120523,
        "lamdabold": 120524,
        "mubold": 120525,
        "nubold": 120526,
        "xibold": 120527,
        "omicronbold": 120528,
        "pibold": 120529,
        "rhobold": 120530,
        "finalsigmabold": 120531,
        "sigmabold": 120532,
        "taubold": 120533,
        "upsilonbold": 120534,
        "phibold": 120535,
        "chibold": 120536,
        "psibold": 120537,
        "omegabold": 120538,
        "boldpartialdiff": 120539,
        "epsilonsymbolbold": 120540,
        "thetasymbolbold": 120541,
        "kappasymbolbold": 120542,
        "phisymbolbold": 120543,
        "rhosymbolbold": 120544,
        "pisymbolbold": 120545,
        "Alphaitalic": 120546,
        "Betaitalic": 120547,
        "Gammaitalic": 120548,
        "Deltaitalic": 120549,
        "Epsilonitalic": 120550,
        "Zetaitalic": 120551,
        "Etaitalic": 120552,
        "Thetaitalic": 120553,
        "Iotaitalic": 120554,
        "Kappaitalic": 120555,
        "Lamdaitalic": 120556,
        "Muitalic": 120557,
        "Nuitalic": 120558,
        "Xiitalic": 120559,
        "Omicronitalic": 120560,
        "Piitalic": 120561,
        "Rhoitalic": 120562,
        "Thetasymbolitalic": 120563,
        "Sigmaitalic": 120564,
        "Tauitalic": 120565,
        "Upsilonitalic": 120566,
        "Phiitalic": 120567,
        "Chiitalic": 120568,
        "Psiitalic": 120569,
        "Omegaitalic": 120570,
        "italicnabla": 120571,
        "alphaitalic": 120572,
        "betaitalic": 120573,
        "gammaitalic": 120574,
        "deltaitalic": 120575,
        "epsilonitalic": 120576,
        "zetaitalic": 120577,
        "etaitalic": 120578,
        "thetaitalic": 120579,
        "iotaitalic": 120580,
        "kappaitalic": 120581,
        "lamdaitalic": 120582,
        "muitalic": 120583,
        "nuitalic": 120584,
        "xiitalic": 120585,
        "omicronitalic": 120586,
        "piitalic": 120587,
        "rhoitalic": 120588,
        "finalsigmaitalic": 120589,
        "sigmaitalic": 120590,
        "tauitalic": 120591,
        "upsilonitalic": 120592,
        "phiitalic": 120593,
        "chiitalic": 120594,
        "psiitalic": 120595,
        "omegaitalic": 120596,
        "italicpartialdiff": 120597,
        "epsilonsymbolitalic": 120598,
        "thetasymbolitalic": 120599,
        "kappasymbolitalic": 120600,
        "phisymbolitalic": 120601,
        "rhosymbolitalic": 120602,
        "pisymbolitalic": 120603,
        "Alphabolditalic": 120604,
        "Betabolditalic": 120605,
        "Gammabolditalic": 120606,
        "Deltabolditalic": 120607,
        "Epsilonbolditalic": 120608,
        "Zetabolditalic": 120609,
        "Etabolditalic": 120610,
        "Thetabolditalic": 120611,
        "Iotabolditalic": 120612,
        "Kappabolditalic": 120613,
        "Lamdabolditalic": 120614,
        "Mubolditalic": 120615,
        "Nubolditalic": 120616,
        "Xibolditalic": 120617,
        "Omicronbolditalic": 120618,
        "Pibolditalic": 120619,
        "Rhobolditalic": 120620,
        "Thetasymbolbolditalic": 120621,
        "Sigmabolditalic": 120622,
        "Taubolditalic": 120623,
        "Upsilonbolditalic": 120624,
        "Phibolditalic": 120625,
        "Chibolditalic": 120626,
        "Psibolditalic": 120627,
        "Omegabolditalic": 120628,
        "bolditalicnabla": 120629,
        "alphabolditalic": 120630,
        "betabolditalic": 120631,
        "gammabolditalic": 120632,
        "deltabolditalic": 120633,
        "epsilonbolditalic": 120634,
        "zetabolditalic": 120635,
        "etabolditalic": 120636,
        "thetabolditalic": 120637,
        "iotabolditalic": 120638,
        "kappabolditalic": 120639,
        "lamdabolditalic": 120640,
        "mubolditalic": 120641,
        "nubolditalic": 120642,
        "xibolditalic": 120643,
        "omicronbolditalic": 120644,
        "pibolditalic": 120645,
        "rhobolditalic": 120646,
        "finalsigmabolditalic": 120647,
        "sigmabolditalic": 120648,
        "taubolditalic": 120649,
        "upsilonbolditalic": 120650,
        "phibolditalic": 120651,
        "chibolditalic": 120652,
        "psibolditalic": 120653,
        "omegabolditalic": 120654,
        "bolditalicpartialdiff": 120655,
        "epsilonsymbolbolditalic": 120656,
        "thetasymbolbolditalic": 120657,
        "kappasymbolbolditalic": 120658,
        "phisymbolbolditalic": 120659,
        "rhosymbolbolditalic": 120660,
        "pisymbolbolditalic": 120661,
        "Alphaboldsans": 120662,
        "Betaboldsans": 120663,
        "Gammaboldsans": 120664,
        "Deltaboldsans": 120665,
        "Epsilonboldsans": 120666,
        "Zetaboldsans": 120667,
        "Etaboldsans": 120668,
        "Thetaboldsans": 120669,
        "Iotaboldsans": 120670,
        "Kappaboldsans": 120671,
        "Lamdaboldsans": 120672,
        "Muboldsans": 120673,
        "Nuboldsans": 120674,
        "Xiboldsans": 120675,
        "Omicronboldsans": 120676,
        "Piboldsans": 120677,
        "Rhoboldsans": 120678,
        "Thetasymbolboldsans": 120679,
        "Sigmaboldsans": 120680,
        "Tauboldsans": 120681,
        "Upsilonboldsans": 120682,
        "Phiboldsans": 120683,
        "Chiboldsans": 120684,
        "Psiboldsans": 120685,
        "Omegaboldsans": 120686,
        "boldsansnabla": 120687,
        "alphaboldsans": 120688,
        "betaboldsans": 120689,
        "gammaboldsans": 120690,
        "deltaboldsans": 120691,
        "epsilonboldsans": 120692,
        "zetaboldsans": 120693,
        "etaboldsans": 120694,
        "thetaboldsans": 120695,
        "iotaboldsans": 120696,
        "kappaboldsans": 120697,
        "lamdaboldsans": 120698,
        "muboldsans": 120699,
        "nuboldsans": 120700,
        "xiboldsans": 120701,
        "omicronboldsans": 120702,
        "piboldsans": 120703,
        "rhoboldsans": 120704,
        "finalsigmaboldsans": 120705,
        "sigmaboldsans": 120706,
        "tauboldsans": 120707,
        "upsilonboldsans": 120708,
        "phiboldsans": 120709,
        "chiboldsans": 120710,
        "psiboldsans": 120711,
        "omegaboldsans": 120712,
        "boldsanspartialdiff": 120713,
        "epsilonsymbolboldsans": 120714,
        "thetasymbolboldsans": 120715,
        "kappasymbolboldsans": 120716,
        "phisymbolboldsans": 120717,
        "rhosymbolboldsans": 120718,
        "pisymbolboldsans": 120719,
        "Alphabolditalicsans": 120720,
        "Betabolditalicsans": 120721,
        "Gammabolditalicsans": 120722,
        "Deltabolditalicsans": 120723,
        "Epsilonbolditalicsans": 120724,
        "Zetabolditalicsans": 120725,
        "Etabolditalicsans": 120726,
        "Thetabolditalicsans": 120727,
        "Iotabolditalicsans": 120728,
        "Kappabolditalicsans": 120729,
        "Lamdabolditalicsans": 120730,
        "Mubolditalicsans": 120731,
        "Nubolditalicsans": 120732,
        "Xibolditalicsans": 120733,
        "Omicronbolditalicsans": 120734,
        "Pibolditalicsans": 120735,
        "Rhobolditalicsans": 120736,
        "Thetasymbolbolditalicsans": 120737,
        "Sigmabolditalicsans": 120738,
        "Taubolditalicsans": 120739,
        "Upsilonbolditalicsans": 120740,
        "Phibolditalicsans": 120741,
        "Chibolditalicsans": 120742,
        "Psibolditalicsans": 120743,
        "Omegabolditalicsans": 120744,
        "bolditalicsansnabla": 120745,
        "alphabolditalicsans": 120746,
        "betabolditalicsans": 120747,
        "gammabolditalicsans": 120748,
        "deltabolditalicsans": 120749,
        "epsilonbolditalicsans": 120750,
        "zetabolditalicsans": 120751,
        "etabolditalicsans": 120752,
        "thetabolditalicsans": 120753,
        "iotabolditalicsans": 120754,
        "kappabolditalicsans": 120755,
        "lamdabolditalicsans": 120756,
        "mubolditalicsans": 120757,
        "nubolditalicsans": 120758,
        "xibolditalicsans": 120759,
        "omicronbolditalicsans": 120760,
        "pibolditalicsans": 120761,
        "rhobolditalicsans": 120762,
        "finalsigmabolditalicsans": 120763,
        "sigmabolditalicsans": 120764,
        "taubolditalicsans": 120765,
        "upsilonbolditalicsans": 120766,
        "phibolditalicsans": 120767,
        "chibolditalicsans": 120768,
        "psibolditalicsans": 120769,
        "omegabolditalicsans": 120770,
        "bolditalicsanspartialdiff": 120771,
        "epsilonsymbolbolditalicsans": 120772,
        "thetasymbolbolditalicsans": 120773,
        "kappasymbolbolditalicsans": 120774,
        "phisymbolbolditalicsans": 120775,
        "rhosymbolbolditalicsans": 120776,
        "pisymbolbolditalicsans": 120777,
        "Digammabold": 120778,
        "digammabold": 120779,
        "zerobold": 120782,
        "onebold": 120783,
        "twobold": 120784,
        "threebold": 120785,
        "fourbold": 120786,
        "fivebold": 120787,
        "sixbold": 120788,
        "sevenbold": 120789,
        "eightbold": 120790,
        "ninebold": 120791,
        "zerodblstruck": 120792,
        "onedblstruck": 120793,
        "twodblstruck": 120794,
        "threedblstruck": 120795,
        "fourdblstruck": 120796,
        "fivedblstruck": 120797,
        "sixdblstruck": 120798,
        "sevendblstruck": 120799,
        "eightdblstruck": 120800,
        "ninedblstruck": 120801,
        "zerosans": 120802,
        "onesans": 120803,
        "twosans": 120804,
        "threesans": 120805,
        "foursans": 120806,
        "fivesans": 120807,
        "sixsans": 120808,
        "sevensans": 120809,
        "eightsans": 120810,
        "ninesans": 120811,
        "zeroboldsans": 120812,
        "oneboldsans": 120813,
        "twoboldsans": 120814,
        "threeboldsans": 120815,
        "fourboldsans": 120816,
        "fiveboldsans": 120817,
        "sixboldsans": 120818,
        "sevenboldsans": 120819,
        "eightboldsans": 120820,
        "nineboldsans": 120821,
        "zeromono": 120822,
        "onemono": 120823,
        "twomono": 120824,
        "threemono": 120825,
        "fourmono": 120826,
        "fivemono": 120827,
        "sixmono": 120828,
        "sevenmono": 120829,
        "eightmono": 120830,
        "ninemono": 120831,
        "dominohorizontalback": 127024,
        "dominohorizontal_00_00": 127025,
        "dominohorizontal_00_01": 127026,
        "dominohorizontal_00_02": 127027,
        "dominohorizontal_00_03": 127028,
        "dominohorizontal_00_04": 127029,
        "dominohorizontal_00_05": 127030,
        "dominohorizontal_00_06": 127031,
        "dominohorizontal_01_00": 127032,
        "dominohorizontal_01_01": 127033,
        "dominohorizontal_01_02": 127034,
        "dominohorizontal_01_03": 127035,
        "dominohorizontal_01_04": 127036,
        "dominohorizontal_01_05": 127037,
        "dominohorizontal_01_06": 127038,
        "dominohorizontal_02_00": 127039,
        "dominohorizontal_02_01": 127040,
        "dominohorizontal_02_02": 127041,
        "dominohorizontal_02_03": 127042,
        "dominohorizontal_02_04": 127043,
        "dominohorizontal_02_05": 127044,
        "dominohorizontal_02_06": 127045,
        "dominohorizontal_03_00": 127046,
        "dominohorizontal_03_01": 127047,
        "dominohorizontal_03_02": 127048,
        "dominohorizontal_03_03": 127049,
        "dominohorizontal_03_04": 127050,
        "dominohorizontal_03_05": 127051,
        "dominohorizontal_03_06": 127052,
        "dominohorizontal_04_00": 127053,
        "dominohorizontal_04_01": 127054,
        "dominohorizontal_04_02": 127055,
        "dominohorizontal_04_03": 127056,
        "dominohorizontal_04_04": 127057,
        "dominohorizontal_04_05": 127058,
        "dominohorizontal_04_06": 127059,
        "dominohorizontal_05_00": 127060,
        "dominohorizontal_05_01": 127061,
        "dominohorizontal_05_02": 127062,
        "dominohorizontal_05_03": 127063,
        "dominohorizontal_05_04": 127064,
        "dominohorizontal_05_05": 127065,
        "dominohorizontal_05_06": 127066,
        "dominohorizontal_06_00": 127067,
        "dominohorizontal_06_01": 127068,
        "dominohorizontal_06_02": 127069,
        "dominohorizontal_06_03": 127070,
        "dominohorizontal_06_04": 127071,
        "dominohorizontal_06_05": 127072,
        "dominohorizontal_06_06": 127073,
        "dominoverticalback": 127074,
        "dominovertical_00_00": 127075,
        "dominovertical_00_01": 127076,
        "dominovertical_00_02": 127077,
        "dominovertical_00_03": 127078,
        "dominovertical_00_04": 127079,
        "dominovertical_00_05": 127080,
        "dominovertical_00_06": 127081,
        "dominovertical_01_00": 127082,
        "dominovertical_01_01": 127083,
        "dominovertical_01_02": 127084,
        "dominovertical_01_03": 127085,
        "dominovertical_01_04": 127086,
        "dominovertical_01_05": 127087,
        "dominovertical_01_06": 127088,
        "dominovertical_02_00": 127089,
        "dominovertical_02_01": 127090,
        "dominovertical_02_02": 127091,
        "dominovertical_02_03": 127092,
        "dominovertical_02_04": 127093,
        "dominovertical_02_05": 127094,
        "dominovertical_02_06": 127095,
        "dominovertical_03_00": 127096,
        "dominovertical_03_01": 127097,
        "dominovertical_03_02": 127098,
        "dominovertical_03_03": 127099,
        "dominovertical_03_04": 127100,
        "dominovertical_03_05": 127101,
        "dominovertical_03_06": 127102,
        "dominovertical_04_00": 127103,
        "dominovertical_04_01": 127104,
        "dominovertical_04_02": 127105,
        "dominovertical_04_03": 127106,
        "dominovertical_04_04": 127107,
        "dominovertical_04_05": 127108,
        "dominovertical_04_06": 127109,
        "dominovertical_05_00": 127110,
        "dominovertical_05_01": 127111,
        "dominovertical_05_02": 127112,
        "dominovertical_05_03": 127113,
        "dominovertical_05_04": 127114,
        "dominovertical_05_05": 127115,
        "dominovertical_05_06": 127116,
        "dominovertical_06_00": 127117,
        "dominovertical_06_01": 127118,
        "dominovertical_06_02": 127119,
        "dominovertical_06_03": 127120,
        "dominovertical_06_04": 127121,
        "dominovertical_06_05": 127122,
        "dominovertical_06_06": 127123,
        "cards:back": 127136,
        "cards:aceofspades": 127137,
        "cards:twoofspades": 127138,
        "cards:threeofspades": 127139,
        "cards:fourofspades": 127140,
        "cards:fiveofspades": 127141,
        "cards:sixofspades": 127142,
        "cards:sevenofspades": 127143,
        "cards:eightofspades": 127144,
        "cards:nineofspades": 127145,
        "cards:tenofspades": 127146,
        "cards:jackofspades": 127147,
        "cards:knightofspades": 127148,
        "cards:queenofspades": 127149,
        "cards:kingofspades": 127150,
        "cards:aceofhearts": 127153,
        "cards:twoofhearts": 127154,
        "cards:threeofhearts": 127155,
        "cards:fourofhearts": 127156,
        "cards:fiveofhearts": 127157,
        "cards:sixofhearts": 127158,
        "cards:sevenofhearts": 127159,
        "cards:eightofhearts": 127160,
        "cards:nineofhearts": 127161,
        "cards:tenofhearts": 127162,
        "cards:jackofhearts": 127163,
        "cards:knightofhearts": 127164,
        "cards:queenofhearts": 127165,
        "cards:kingofhearts": 127166,
        "cards:redjoker": 127167,
        "cards:aceofdiamonds": 127169,
        "cards:twoofdiamonds": 127170,
        "cards:threeofdiamonds": 127171,
        "cards:fourofdiamonds": 127172,
        "cards:fiveofdiamonds": 127173,
        "cards:sixofdiamonds": 127174,
        "cards:sevenofdiamonds": 127175,
        "cards:eightofdiamonds": 127176,
        "cards:nineofdiamonds": 127177,
        "cards:tenofdiamonds": 127178,
        "cards:jackofdiamonds": 127179,
        "cards:knightofdiamonds": 127180,
        "cards:queenofdiamonds": 127181,
        "cards:kingofdiamonds": 127182,
        "cards:blackjoker": 127183,
        "cards:aceofclubs": 127185,
        "cards:twoofclubs": 127186,
        "cards:threeofclubs": 127187,
        "cards:fourofclubs": 127188,
        "cards:fiveofclubs": 127189,
        "cards:sixofclubs": 127190,
        "cards:sevenofclubs": 127191,
        "cards:eightofclubs": 127192,
        "cards:nineofclubs": 127193,
        "cards:tenofclubs": 127194,
        "cards:jackofclubs": 127195,
        "cards:knightofclubs": 127196,
        "cards:queenofclubs": 127197,
        "cards:kingofclubs": 127198,
        "cards:whitejoker": 127199,
        "cards:fool": 127200,
        "cards:trump1": 127201,
        "cards:trump2": 127202,
        "cards:trump3": 127203,
        "cards:trump4": 127204,
        "cards:trump5": 127205,
        "cards:trump6": 127206,
        "cards:trump7": 127207,
        "cards:trump8": 127208,
        "cards:trump9": 127209,
        "cards:trump10": 127210,
        "cards:trump11": 127211,
        "cards:trump12": 127212,
        "cards:trump13": 127213,
        "cards:trump14": 127214,
        "cards:trump15": 127215,
        "cards:trump16": 127216,
        "cards:trump17": 127217,
        "cards:trump18": 127218,
        "cards:trump19": 127219,
        "cards:trump20": 127220,
        "cards:trump21": 127221,
        "zerofullstop": 127232,
        "zerocomma": 127233,
        "onecomma": 127234,
        "twocomma": 127235,
        "threecomma": 127236,
        "fourcomma": 127237,
        "fivecomma": 127238,
        "sixcomma": 127239,
        "sevencomma": 127240,
        "eightcomma": 127241,
        "ninecomma": 127242,
        "dingbatSAns-serifzerocircle": 127243,
        "dingbatSAns-serifzerocircleblack": 127244,
        "Aparens": 127248,
        "Bparens": 127249,
        "Cparens": 127250,
        "Dparens": 127251,
        "Eparens": 127252,
        "Fparens": 127253,
        "Gparens": 127254,
        "Hparens": 127255,
        "Iparens": 127256,
        "Jparens": 127257,
        "Kparens": 127258,
        "Lparens": 127259,
        "Mparens": 127260,
        "Nparens": 127261,
        "Oparens": 127262,
        "Pparens": 127263,
        "Qparens": 127264,
        "Rparens": 127265,
        "Sparens": 127266,
        "Tparens": 127267,
        "Uparens": 127268,
        "Vparens": 127269,
        "Wparens": 127270,
        "Xparens": 127271,
        "Yparens": 127272,
        "Zparens": 127273,
        "Sshell": 127274,
        "Citaliccircle": 127275,
        "Ritaliccircle": 127276,
        "CDcircle": 127277,
        "WZcircle": 127278,
        "copyleftsymbol": 127279,
        "Asquare": 127280,
        "Bsquare": 127281,
        "Csquare": 127282,
        "Dsquare": 127283,
        "Esquare": 127284,
        "Fsquare": 127285,
        "Gsquare": 127286,
        "Hsquare": 127287,
        "Isquare": 127288,
        "Jsquare": 127289,
        "Ksquare": 127290,
        "Lsquare": 127291,
        "Msquare": 127292,
        "Nsquare": 127293,
        "Osquare": 127294,
        "Psquare": 127295,
        "Qsquare": 127296,
        "Rsquare": 127297,
        "Ssquare": 127298,
        "Tsquare": 127299,
        "Usquare": 127300,
        "Vsquare": 127301,
        "Wsquare": 127302,
        "Xsquare": 127303,
        "Ysquare": 127304,
        "Zsquare": 127305,
        "HVsquare": 127306,
        "MVsquare": 127307,
        "SDsquare": 127308,
        "SSsquare": 127309,
        "PPVsquare": 127310,
        "wcsquare": 127311,
        "Acircleblack": 127312,
        "Bcircleblack": 127313,
        "Ccircleblack": 127314,
        "Dcircleblack": 127315,
        "Ecircleblack": 127316,
        "Fcircleblack": 127317,
        "Gcircleblack": 127318,
        "Hcircleblack": 127319,
        "Icircleblack": 127320,
        "Jcircleblack": 127321,
        "Kcircleblack": 127322,
        "Lcircleblack": 127323,
        "Mcircleblack": 127324,
        "Ncircleblack": 127325,
        "Ocircleblack": 127326,
        "Pcircleblack": 127327,
        "Qcircleblack": 127328,
        "Rcircleblack": 127329,
        "Scircleblack": 127330,
        "Tcircleblack": 127331,
        "Ucircleblack": 127332,
        "Vcircleblack": 127333,
        "Wcircleblack": 127334,
        "Xcircleblack": 127335,
        "Ycircleblack": 127336,
        "Zcircleblack": 127337,
        "raisedmcsign": 127338,
        "raisedmdsign": 127339,
        "raisedmrsign": 127340,
        "Asquareblack": 127344,
        "Bsquareblack": 127345,
        "Csquareblack": 127346,
        "Dsquareblack": 127347,
        "Esquareblack": 127348,
        "Fsquareblack": 127349,
        "Gsquareblack": 127350,
        "Hsquareblack": 127351,
        "Isquareblack": 127352,
        "Jsquareblack": 127353,
        "Ksquareblack": 127354,
        "Lsquareblack": 127355,
        "Msquareblack": 127356,
        "Nsquareblack": 127357,
        "Osquareblack": 127358,
        "Psquareblack": 127359,
        "Qsquareblack": 127360,
        "Rsquareblack": 127361,
        "Ssquareblack": 127362,
        "Tsquareblack": 127363,
        "Usquareblack": 127364,
        "Vsquareblack": 127365,
        "Wsquareblack": 127366,
        "Xsquareblack": 127367,
        "Ysquareblack": 127368,
        "Zsquareblack": 127369,
        "Pcrosssquareblack": 127370,
        "ICsquareblack": 127371,
        "PAsquareblack": 127372,
        "SAsquareblack": 127373,
        "absquareblack": 127374,
        "wcsquareblack": 127375,
        "squaredj": 127376,
        "clsquare": 127377,
        "coolsquare": 127378,
        "freesquare": 127379,
        "idsquare": 127380,
        "newsquare": 127381,
        "ngsquare": 127382,
        "oksquare": 127383,
        "sossquare": 127384,
        "upwithexclamationmarksquare": 127385,
        "vssquare": 127386,
        "threedsquare": 127387,
        "secondscreensquare": 127388,
        "twoksquare": 127389,
        "fourksquare": 127390,
        "eightksquare": 127391,
        "fivepointonesquare": 127392,
        "sevenpointonesquare": 127393,
        "twenty-twopointtwosquare": 127394,
        "sixtypsquare": 127395,
        "onehundredtwentypsquare": 127396,
        "dsquare": 127397,
        "hcsquare": 127398,
        "hdrsquare": 127399,
        "hi-ressquare": 127400,
        "losslesssquare": 127401,
        "shvsquare": 127402,
        "uhdsquare": 127403,
        "vodsquare": 127404,
        "regionalindicatorsymbollettera": 127462,
        "regionalindicatorsymbolletterb": 127463,
        "regionalindicatorsymbolletterc": 127464,
        "regionalindicatorsymbolletterd": 127465,
        "regionalindicatorsymbollettere": 127466,
        "regionalindicatorsymbolletterf": 127467,
        "regionalindicatorsymbolletterg": 127468,
        "regionalindicatorsymbolletterh": 127469,
        "regionalindicatorsymbolletteri": 127470,
        "regionalindicatorsymbolletterj": 127471,
        "regionalindicatorsymbolletterk": 127472,
        "regionalindicatorsymbolletterl": 127473,
        "regionalindicatorsymbolletterm": 127474,
        "regionalindicatorsymbollettern": 127475,
        "regionalindicatorsymbollettero": 127476,
        "regionalindicatorsymbolletterp": 127477,
        "regionalindicatorsymbolletterq": 127478,
        "regionalindicatorsymbolletterr": 127479,
        "regionalindicatorsymbolletters": 127480,
        "regionalindicatorsymbollettert": 127481,
        "regionalindicatorsymbolletteru": 127482,
        "regionalindicatorsymbolletterv": 127483,
        "regionalindicatorsymbolletterw": 127484,
        "regionalindicatorsymbolletterx": 127485,
        "regionalindicatorsymbollettery": 127486,
        "regionalindicatorsymbolletterz": 127487,
        "cyclone": 127744,
        "foggy": 127745,
        "closedUmbrella": 127746,
        "nightStars": 127747,
        "sunriseOverMountains": 127748,
        "sunrise": 127749,
        "cityscapeAtDusk": 127750,
        "sunsetOverBuildings": 127751,
        "rainbow": 127752,
        "bridgeAtNight": 127753,
        "waterWave": 127754,
        "volcano": 127755,
        "milkyWay": 127756,
        "earthGlobeEuropeAfrica": 127757,
        "earthGlobeAmericas": 127758,
        "earthGlobeAsiaAustralia": 127759,
        "globeMeridians": 127760,
        "newMoon": 127761,
        "waxingCrescentMoon": 127762,
        "firstQuarterMoon": 127763,
        "waxingGibbousMoon": 127764,
        "fullMoon": 127765,
        "waningGibbousMoon": 127766,
        "lastQuarterMoon": 127767,
        "waningCrescentMoon": 127768,
        "crescentMoon": 127769,
        "newMoonFace": 127770,
        "firstQuarterMoonFace": 127771,
        "lastQuarterMoonFace": 127772,
        "fullMoonFace": 127773,
        "sunFace": 127774,
        "glowingStar": 127775,
        "shootingStar": 127776,
        "thermometer": 127777,
        "blackDroplet": 127778,
        "whiteSun": 127779,
        "whiteSunSmallCloud": 127780,
        "whiteSunBehindCloud": 127781,
        "whiteSunBehindCloudRain": 127782,
        "cloudRain": 127783,
        "cloudSnow": 127784,
        "cloudLightning": 127785,
        "cloudTornado": 127786,
        "fog": 127787,
        "windBlowingFace": 127788,
        "hotDog": 127789,
        "taco": 127790,
        "burrito": 127791,
        "chestnut": 127792,
        "seedling": 127793,
        "evergreenTree": 127794,
        "deciduousTree": 127795,
        "palmTree": 127796,
        "cactus": 127797,
        "hotPepper": 127798,
        "tulip": 127799,
        "cherryBlossom": 127800,
        "rose": 127801,
        "hibiscus": 127802,
        "sunflower": 127803,
        "blossom": 127804,
        "earOfMaize": 127805,
        "earOfRice": 127806,
        "herb": 127807,
        "fourLeafClover": 127808,
        "mapleLeaf": 127809,
        "fallenLeaf": 127810,
        "leafFlutteringInWind": 127811,
        "mushroom": 127812,
        "tomato": 127813,
        "aubergine": 127814,
        "grapes": 127815,
        "melon": 127816,
        "watermelon": 127817,
        "tangerine": 127818,
        "lemon": 127819,
        "banana": 127820,
        "pineapple": 127821,
        "redApple": 127822,
        "greenApple": 127823,
        "pear": 127824,
        "peach": 127825,
        "cherries": 127826,
        "strawberry": 127827,
        "hamburger": 127828,
        "sliceOfPizza": 127829,
        "meatOnBone": 127830,
        "poultryLeg": 127831,
        "riceCracker": 127832,
        "riceBall": 127833,
        "cookedRice": 127834,
        "curryAndRice": 127835,
        "steamingBowl": 127836,
        "spaghetti": 127837,
        "bread": 127838,
        "frenchFries": 127839,
        "roastedSweetPotato": 127840,
        "dango": 127841,
        "oden": 127842,
        "sushi": 127843,
        "friedShrimp": 127844,
        "fishCakeSwirlDesign": 127845,
        "softIceCream": 127846,
        "shavedIce": 127847,
        "iceCream": 127848,
        "doughnut": 127849,
        "cookie": 127850,
        "chocolateBar": 127851,
        "candy": 127852,
        "lollipop": 127853,
        "custard": 127854,
        "honeyPot": 127855,
        "shortcake": 127856,
        "bentoBox": 127857,
        "potOfFood": 127858,
        "cooking": 127859,
        "forkKnife": 127860,
        "teacupOutHandle": 127861,
        "sakeBottleAndCup": 127862,
        "wineGlass": 127863,
        "cocktailGlass": 127864,
        "tropicalDrink": 127865,
        "beerMug": 127866,
        "clinkingBeerMugs": 127867,
        "babyBottle": 127868,
        "forkKnifePlate": 127869,
        "bottlePoppingCork": 127870,
        "popcorn": 127871,
        "ribbon": 127872,
        "wrappedPresent": 127873,
        "birthdayCake": 127874,
        "jackOLantern": 127875,
        "christmasTree": 127876,
        "fatherChristmas": 127877,
        "fireworks": 127878,
        "fireworkSparkler": 127879,
        "balloon": 127880,
        "partyPopper": 127881,
        "confettiBall": 127882,
        "tanabataTree": 127883,
        "crossedFlags": 127884,
        "pineDecoration": 127885,
        "japaneseDolls": 127886,
        "carpStreamer": 127887,
        "windChime": 127888,
        "moonViewingCeremony": 127889,
        "schoolSatchel": 127890,
        "graduationCap": 127891,
        "heartTipOnTheLeft": 127892,
        "bouquetOfFlowers": 127893,
        "militaryMedal": 127894,
        "reminderRibbon": 127895,
        "musicalKeyboardJacks": 127896,
        "studioMicrophone": 127897,
        "levelSlider": 127898,
        "controlKnobs": 127899,
        "beamedAscendingMusicalNotes": 127900,
        "beamedDescendingMusicalNotes": 127901,
        "filmFrames": 127902,
        "admissionTickets": 127903,
        "carouselHorse": 127904,
        "ferrisWheel": 127905,
        "rollerCoaster": 127906,
        "fishingPoleAndFish": 127907,
        "microphone": 127908,
        "movieCamera": 127909,
        "cinema": 127910,
        "headphone": 127911,
        "artistPalette": 127912,
        "topHat": 127913,
        "circusTent": 127914,
        "ticket": 127915,
        "clapperBoard": 127916,
        "performingArts": 127917,
        "videoGame": 127918,
        "directHit": 127919,
        "slotMachine": 127920,
        "billiards": 127921,
        "gameDie": 127922,
        "bowling": 127923,
        "flowerPlayingCards": 127924,
        "musicalNote": 127925,
        "multipleMusicalNotes": 127926,
        "saxophone": 127927,
        "guitar": 127928,
        "musicalKeyboard": 127929,
        "trumpet": 127930,
        "violin": 127931,
        "musicalScore": 127932,
        "runningShirtSash": 127933,
        "tennisRacquetAndBall": 127934,
        "skiAndSkiBoot": 127935,
        "basketballAndHoop": 127936,
        "chequeredFlag": 127937,
        "snowboarder": 127938,
        "runner": 127939,
        "surfer": 127940,
        "sportsMedal": 127941,
        "trophy": 127942,
        "horseRacing": 127943,
        "americanFootball": 127944,
        "rugbyFootball": 127945,
        "swimmer": 127946,
        "weightLifter": 127947,
        "golfer": 127948,
        "racingMotorcycle": 127949,
        "racingCar": 127950,
        "cricketBatAndBall": 127951,
        "volleyball": 127952,
        "fieldHockeyStickAndBall": 127953,
        "iceHockeyStickAndPuck": 127954,
        "tableTennisPaddleAndBall": 127955,
        "snowcappedMountain": 127956,
        "camping": 127957,
        "beachUmbrella": 127958,
        "buildingConstruction": 127959,
        "houseBuildings": 127960,
        "cityscape": 127961,
        "derelictHouseBuilding": 127962,
        "classicalBuilding": 127963,
        "desert": 127964,
        "desertIsland": 127965,
        "nationalPark": 127966,
        "stadium": 127967,
        "houseBuilding": 127968,
        "houseGarden": 127969,
        "officeBuilding": 127970,
        "japanesePostOffice": 127971,
        "europeanPostOffice": 127972,
        "hospital": 127973,
        "bank": 127974,
        "automatedTellerMachine": 127975,
        "hotel": 127976,
        "loveHotel": 127977,
        "convenienceStore": 127978,
        "school": 127979,
        "departmentStore": 127980,
        "factory": 127981,
        "izakayaLantern": 127982,
        "japaneseCastle": 127983,
        "europeanCastle": 127984,
        "whitePennant": 127985,
        "blackPennant": 127986,
        "wavingWhiteFlag": 127987,
        "wavingBlackFlag": 127988,
        "rosette": 127989,
        "blackRosette": 127990,
        "label": 127991,
        "badmintonRacquetAndShuttlecock": 127992,
        "bowAndArrow": 127993,
        "amphora": 127994,
        "emojiModifierFitzpatrickType-1-2": 127995,
        "emojiModifierFitzpatrickType-3": 127996,
        "emojiModifierFitzpatrickType-4": 127997,
        "emojiModifierFitzpatrickType-5": 127998,
        "emojiModifierFitzpatrickType-6": 127999,
        "rat": 128000,
        "mouse": 128001,
        "ox": 128002,
        "waterBuffalo": 128003,
        "cow": 128004,
        "tiger": 128005,
        "leopard": 128006,
        "rabbit": 128007,
        "cat": 128008,
        "dragon": 128009,
        "crocodile": 128010,
        "whale": 128011,
        "snail": 128012,
        "snake": 128013,
        "horse": 128014,
        "ram": 128015,
        "goat": 128016,
        "sheep": 128017,
        "monkey": 128018,
        "rooster": 128019,
        "chicken": 128020,
        "dog": 128021,
        "pig": 128022,
        "boar": 128023,
        "elephant": 128024,
        "octopus": 128025,
        "spiralShell": 128026,
        "bug": 128027,
        "ant": 128028,
        "honeybee": 128029,
        "ladyBeetle": 128030,
        "fish": 128031,
        "tropicalFish": 128032,
        "blowfish": 128033,
        "turtle": 128034,
        "hatchingChick": 128035,
        "babyChick": 128036,
        "front-facingBabyChick": 128037,
        "bird": 128038,
        "penguin": 128039,
        "koala": 128040,
        "poodle": 128041,
        "dromedaryCamel": 128042,
        "bactrianCamel": 128043,
        "dolphin": 128044,
        "mouseFace": 128045,
        "cowFace": 128046,
        "tigerFace": 128047,
        "rabbitFace": 128048,
        "catFace": 128049,
        "dragonFace": 128050,
        "spoutingWhale": 128051,
        "horseFace": 128052,
        "monkeyFace": 128053,
        "dogFace": 128054,
        "pigFace": 128055,
        "frogFace": 128056,
        "hamsterFace": 128057,
        "wolfFace": 128058,
        "bearFace": 128059,
        "pandaFace": 128060,
        "pigNose": 128061,
        "pawPrints": 128062,
        "chipmunk": 128063,
        "eyes": 128064,
        "eye": 128065,
        "ear": 128066,
        "nose": 128067,
        "mouth": 128068,
        "tongue": 128069,
        "whiteUpPointingBackhandIndex": 128070,
        "whiteDownPointingBackhandIndex": 128071,
        "whiteLeftPointingBackhandIndex": 128072,
        "whiteRightPointingBackhandIndex": 128073,
        "fistedHandSign": 128074,
        "wavingHandSign": 128075,
        "okHandSign": 128076,
        "thumbsUpSign": 128077,
        "thumbsDownSign": 128078,
        "clappingHandsSign": 128079,
        "openHandsSign": 128080,
        "crown": 128081,
        "womansHat": 128082,
        "eyeglasses": 128083,
        "necktie": 128084,
        "t-shirt": 128085,
        "jeans": 128086,
        "dress": 128087,
        "kimono": 128088,
        "bikini": 128089,
        "womansClothes": 128090,
        "purse": 128091,
        "handbag": 128092,
        "pouch": 128093,
        "mansShoe": 128094,
        "athleticShoe": 128095,
        "high-heeledShoe": 128096,
        "womansSandal": 128097,
        "womansBoots": 128098,
        "footprints": 128099,
        "bustInSilhouette": 128100,
        "bustsInSilhouette": 128101,
        "boy": 128102,
        "girl": 128103,
        "man": 128104,
        "woman": 128105,
        "family": 128106,
        "manAndWomanHoldingHands": 128107,
        "twoMenHoldingHands": 128108,
        "twoWomenHoldingHands": 128109,
        "policeOfficer": 128110,
        "womanBunnyEars": 128111,
        "brideVeil": 128112,
        "personBlondHair": 128113,
        "manGuaPiMao": 128114,
        "manTurban": 128115,
        "olderMan": 128116,
        "olderWoman": 128117,
        "misc:baby": 128118,
        "constructionWorker": 128119,
        "princess": 128120,
        "japaneseOgre": 128121,
        "japaneseGoblin": 128122,
        "ghost": 128123,
        "babyAngel": 128124,
        "extraterrestrialAlien": 128125,
        "alienMonster": 128126,
        "imp": 128127,
        "skull": 128128,
        "inmationDeskPerson": 128129,
        "guardsman": 128130,
        "dancer": 128131,
        "lipstick": 128132,
        "nailPolish": 128133,
        "faceMassage": 128134,
        "haircut": 128135,
        "barberPole": 128136,
        "syringe": 128137,
        "pill": 128138,
        "kissMark": 128139,
        "loveLetter": 128140,
        "misc:ring": 128141,
        "gemStone": 128142,
        "kiss": 128143,
        "bouquet": 128144,
        "coupleHeart": 128145,
        "wedding": 128146,
        "beatingHeart": 128147,
        "brokenHeart": 128148,
        "twoHearts": 128149,
        "sparklingHeart": 128150,
        "growingHeart": 128151,
        "heartArrow": 128152,
        "blueHeart": 128153,
        "greenHeart": 128154,
        "yellowHeart": 128155,
        "purpleHeart": 128156,
        "heartRibbon": 128157,
        "revolvingHearts": 128158,
        "heartDecoration": 128159,
        "diamondShapeADotInside": 128160,
        "electricLightBulb": 128161,
        "anger": 128162,
        "bomb": 128163,
        "sleeping": 128164,
        "collision": 128165,
        "splashingSweat": 128166,
        "droplet": 128167,
        "misc:dash": 128168,
        "pileOfPoo": 128169,
        "flexedBiceps": 128170,
        "dizzy": 128171,
        "speechBalloon": 128172,
        "thoughtBalloon": 128173,
        "whiteFlower": 128174,
        "hundredPoints": 128175,
        "moneyBag": 128176,
        "currencyExchange": 128177,
        "heavyDollarSign": 128178,
        "creditCard": 128179,
        "banknoteYenSign": 128180,
        "banknoteDollarSign": 128181,
        "banknoteEuroSign": 128182,
        "banknotePoundSign": 128183,
        "moneyWings": 128184,
        "chartUpwardsTrendAndYenSign": 128185,
        "seat": 128186,
        "personalComputer": 128187,
        "briefcase": 128188,
        "minidisc": 128189,
        "floppyDisk": 128190,
        "opticalDisc": 128191,
        "dvd": 128192,
        "fileFolder": 128193,
        "openFileFolder": 128194,
        "pageCurl": 128195,
        "pageFacingUp": 128196,
        "calendar": 128197,
        "tear-offCalendar": 128198,
        "cardIndex": 128199,
        "chartUpwardsTrend": 128200,
        "chartDownwardsTrend": 128201,
        "barChart": 128202,
        "clipboard": 128203,
        "pushpin": 128204,
        "roundPushpin": 128205,
        "paperclip": 128206,
        "straightRuler": 128207,
        "triangularRuler": 128208,
        "bookmarkTabs": 128209,
        "ledger": 128210,
        "notebook": 128211,
        "notebookDecorativeCover": 128212,
        "closedBook": 128213,
        "openBook": 128214,
        "greenBook": 128215,
        "blueBook": 128216,
        "orangeBook": 128217,
        "books": 128218,
        "nameBadge": 128219,
        "scroll": 128220,
        "memo": 128221,
        "telephoneReceiver": 128222,
        "pager": 128223,
        "faxMachine": 128224,
        "satelliteAntenna": 128225,
        "publicAddressLoudspeaker": 128226,
        "cheeringMegaphone": 128227,
        "outboxTray": 128228,
        "inboxTray": 128229,
        "package": 128230,
        "e-mail": 128231,
        "incomingEnvelope": 128232,
        "envelopeDownwardsArrowAbove": 128233,
        "closedMailboxLoweredFlag": 128234,
        "closedMailboxRaisedFlag": 128235,
        "openMailboxRaisedFlag": 128236,
        "openMailboxLoweredFlag": 128237,
        "postbox": 128238,
        "postalHorn": 128239,
        "newspaper": 128240,
        "mobilePhone": 128241,
        "mobilePhoneRightwardsArrowAtLeft": 128242,
        "vibrationMode": 128243,
        "mobilePhoneOff": 128244,
        "noMobilePhones": 128245,
        "antennaBars": 128246,
        "camera": 128247,
        "cameraFlash": 128248,
        "videoCamera": 128249,
        "television": 128250,
        "radio": 128251,
        "videocassette": 128252,
        "filmProjector": 128253,
        "portableStereo": 128254,
        "prayerBeads": 128255,
        "twistedRightwardsArrows": 128256,
        "clockwiseRightwardsAndLeftwardsOpenCircleArrows": 128257,
        "clockwiseRightwardsAndLeftwardsOpenCircleArrowsCircledOneOverlay": 128258,
        "clockwiseDownwardsAndUpwardsOpenCircleArrows": 128259,
        "anticlockwiseDownwardsAndUpwardsOpenCircleArrows": 128260,
        "lowBrightness": 128261,
        "highBrightness": 128262,
        "speakerCancellationStroke": 128263,
        "speaker": 128264,
        "speakerOneSoundWave": 128265,
        "speakerThreeSoundWaves": 128266,
        "battery": 128267,
        "electricPlug": 128268,
        "left-pointingMagnifyingGlass": 128269,
        "right-pointingMagnifyingGlass": 128270,
        "lockInkPen": 128271,
        "closedLockKey": 128272,
        "key": 128273,
        "lock": 128274,
        "openLock": 128275,
        "bellCancellationStroke": 128277,
        "bookmark": 128278,
        "link": 128279,
        "radioButton": 128280,
        "backLeftwardsArrowAbove": 128281,
        "endLeftwardsArrowAbove": 128282,
        "onExclamationMarkLeftRightArrowAbove": 128283,
        "soonRightwardsArrowAbove": 128284,
        "topUpwardsArrowAbove": 128285,
        "noOneUnderEighteen": 128286,
        "keycapTen": 128287,
        "inputLatinCapitalLetters": 128288,
        "inputLatinSmallLetters": 128289,
        "inputNumbers": 128290,
        "inputS": 128291,
        "inputLatinLetters": 128292,
        "fire": 128293,
        "electricTorch": 128294,
        "wrench": 128295,
        "hammer": 128296,
        "nutAndBolt": 128297,
        "hocho": 128298,
        "pistol": 128299,
        "microscope": 128300,
        "telescope": 128301,
        "crystalBall": 128302,
        "sixPointedStarMiddleDot": 128303,
        "japaneseBeginner": 128304,
        "tridentEmblem": 128305,
        "blackSquareButton": 128306,
        "whiteSquareButton": 128307,
        "largeRedCircle": 128308,
        "largeBlueCircle": 128309,
        "largeOrangeDiamond": 128310,
        "largeBlueDiamond": 128311,
        "smallOrangeDiamond": 128312,
        "smallBlueDiamond": 128313,
        "redTriangleUp": 128314,
        "redTriangleDOwn": 128315,
        "smallRedTriangleUp": 128316,
        "smallRedTriangleDOwn": 128317,
        "lowerRightShadowedWhiteCircle": 128318,
        "upperRightShadowedWhiteCircle": 128319,
        "circledCrossPommee": 128320,
        "crossPommeeHalf-circleBelow": 128321,
        "crossPommee": 128322,
        "notchedLeftSemicircleThreeDots": 128323,
        "notchedRightSemicircleThreeDots": 128324,
        "marksChapter": 128325,
        "whiteLatinCross": 128326,
        "heavyLatinCross": 128327,
        "celticCross": 128328,
        "om": 128329,
        "doveOfPeace": 128330,
        "kaaba": 128331,
        "mosque": 128332,
        "synagogue": 128333,
        "menorahNineBranches": 128334,
        "bowlOfHygieia": 128335,
        "clockFaceOneOclock": 128336,
        "clockFaceTwoOclock": 128337,
        "clockFaceThreeOclock": 128338,
        "clockFaceFourOclock": 128339,
        "clockFaceFiveOclock": 128340,
        "clockFaceSixOclock": 128341,
        "clockFaceSevenOclock": 128342,
        "clockFaceEightOclock": 128343,
        "clockFaceNineOclock": 128344,
        "clockFaceTenOclock": 128345,
        "clockFaceElevenOclock": 128346,
        "clockFaceTwelveOclock": 128347,
        "clockFaceOne-thirty": 128348,
        "clockFaceTwo-thirty": 128349,
        "clockFaceThree-thirty": 128350,
        "clockFaceFour-thirty": 128351,
        "clockFaceFive-thirty": 128352,
        "clockFaceSix-thirty": 128353,
        "clockFaceSeven-thirty": 128354,
        "clockFaceEight-thirty": 128355,
        "clockFaceNine-thirty": 128356,
        "clockFaceTen-thirty": 128357,
        "clockFaceEleven-thirty": 128358,
        "clockFaceTwelve-thirty": 128359,
        "rightSpeaker": 128360,
        "rightSpeakerOneSoundWave": 128361,
        "rightSpeakerThreeSoundWaves": 128362,
        "bullhorn": 128363,
        "bullhornSoundWaves": 128364,
        "ringingBell": 128365,
        "book": 128366,
        "candle": 128367,
        "mantelpieceClock": 128368,
        "blackSkullAndCrossbones": 128369,
        "noPiracy": 128370,
        "hole": 128371,
        "manInBusinessSuitLevitating": 128372,
        "sleuthOrSpy": 128373,
        "darkSunglasses": 128374,
        "spider": 128375,
        "spiderWeb": 128376,
        "joystick": 128377,
        "manDancing": 128378,
        "leftHandTelephoneReceiver": 128379,
        "telephoneReceiverPage": 128380,
        "rightHandTelephoneReceiver": 128381,
        "whiteTouchtoneTelephone": 128382,
        "blackTouchtoneTelephone": 128383,
        "telephoneOnTopOfModem": 128384,
        "clamshellMobilePhone": 128385,
        "backOfEnvelope": 128386,
        "stampedEnvelope": 128387,
        "envelopeLightning": 128388,
        "flyingEnvelope": 128389,
        "penOverStampedEnvelope": 128390,
        "linkedPaperclips": 128391,
        "blackPushpin": 128392,
        "lowerLeftPencil": 128393,
        "lowerLeftBallpointPen": 128394,
        "lowerLeftFountainPen": 128395,
        "lowerLeftPaintbrush": 128396,
        "lowerLeftCrayon": 128397,
        "leftWritingHand": 128398,
        "turnedOkHandSign": 128399,
        "raisedHandFingersSplayed": 128400,
        "reversedRaisedHandFingersSplayed": 128401,
        "reversedThumbsUpSign": 128402,
        "reversedThumbsDownSign": 128403,
        "reversedVictoryHand": 128404,
        "reversedHandMiddleFingerExtended": 128405,
        "raisedHandPartBetweenMiddleAndRingFingers": 128406,
        "whiteDownPointingLeftHandIndex": 128407,
        "sidewaysWhiteLeftPointingIndex": 128408,
        "sidewaysWhiteRightPointingIndex": 128409,
        "sidewaysBlackLeftPointingIndex": 128410,
        "sidewaysBlackRightPointingIndex": 128411,
        "blackLeftPointingBackhandIndex": 128412,
        "blackRightPointingBackhandIndex": 128413,
        "sidewaysWhiteUpPointingIndex": 128414,
        "sidewaysWhiteDownPointingIndex": 128415,
        "sidewaysBlackUpPointingIndex": 128416,
        "sidewaysBlackDownPointingIndex": 128417,
        "blackUpPointingBackhandIndex": 128418,
        "blackDownPointingBackhandIndex": 128419,
        "blackHeart": 128420,
        "desktopComputer": 128421,
        "keyboardAndMouse": 128422,
        "threeNetworkedComputers": 128423,
        "printer": 128424,
        "pocketCalculator": 128425,
        "blackHardShellFloppyDisk": 128426,
        "whiteHardShellFloppyDisk": 128427,
        "softShellFloppyDisk": 128428,
        "tapeCartridge": 128429,
        "wiredKeyboard": 128430,
        "oneButtonMouse": 128431,
        "twoButtonMouse": 128432,
        "threeButtonMouse": 128433,
        "trackball": 128434,
        "oldPersonalComputer": 128435,
        "hardDisk": 128436,
        "screen": 128437,
        "printerIcon": 128438,
        "faxIcon": 128439,
        "opticalDiscIcon": 128440,
        "documentText": 128441,
        "documentTextAndPicture": 128442,
        "documentPicture": 128443,
        "framePicture": 128444,
        "frameTiles": 128445,
        "frameAnX": 128446,
        "blackFolder": 128447,
        "folder": 128448,
        "openFolder": 128449,
        "cardIndexDividers": 128450,
        "cardFileBox": 128451,
        "fileCabinet": 128452,
        "emptyNote": 128453,
        "emptyNotePage": 128454,
        "emptyNotePad": 128455,
        "note": 128456,
        "notePage": 128457,
        "notePad": 128458,
        "emptyDocument": 128459,
        "emptyPage": 128460,
        "emptyPages": 128461,
        "document": 128462,
        "page": 128463,
        "pages": 128464,
        "wastebasket": 128465,
        "spiralNotePad": 128466,
        "spiralCalendarPad": 128467,
        "desktopWindow": 128468,
        "minimize": 128469,
        "maximize": 128470,
        "overlap": 128471,
        "clockwiseRightAndLeftSemicircleArrows": 128472,
        "cancellationX": 128473,
        "increaseFontSize": 128474,
        "decreaseFontSize": 128475,
        "compression": 128476,
        "oldKey": 128477,
        "rolled-upNewspaper": 128478,
        "pageCircledText": 128479,
        "stockChart": 128480,
        "daggerKnife": 128481,
        "lips": 128482,
        "speakingHeadInSilhouette": 128483,
        "threeRaysAbove": 128484,
        "threeRaysBelow": 128485,
        "threeRaysLeft": 128486,
        "threeRaysRight": 128487,
        "leftSpeechBubble": 128488,
        "rightSpeechBubble": 128489,
        "twoSpeechBubbles": 128490,
        "threeSpeechBubbles": 128491,
        "leftThoughtBubble": 128492,
        "rightThoughtBubble": 128493,
        "leftAngerBubble": 128494,
        "rightAngerBubble": 128495,
        "moodBubble": 128496,
        "lightningMoodBubble": 128497,
        "lightningMood": 128498,
        "ballotBoxBallot": 128499,
        "ballotScriptX": 128500,
        "ballotBoxScriptX": 128501,
        "ballotBoldScriptX": 128502,
        "ballotBoxBoldScriptX": 128503,
        "lightCheckMark": 128504,
        "ballotBoxBoldCheck": 128505,
        "worldMap": 128506,
        "mountFuji": 128507,
        "tokyoTower": 128508,
        "statueOfLiberty": 128509,
        "silhouetteOfJapan": 128510,
        "moyai": 128511,
        "grinningFace": 128512,
        "grinningFaceWithSmilingEyes": 128513,
        "faceWithTearsOfJoy": 128514,
        "smilingFaceWithOpenMouth": 128515,
        "smilingFaceWithOpenMouthAndSmilingEyes": 128516,
        "smilingFaceWithOpenMouthAndColdSweat": 128517,
        "smilingFaceWithOpenMouthAndTightlyClosedEyes": 128518,
        "smilingFaceWithHalo": 128519,
        "smilingFaceWithHorns": 128520,
        "winkingFace": 128521,
        "smilingFaceWithSmilingEyes": 128522,
        "faceSavouringDeliciousFood": 128523,
        "relievedFace": 128524,
        "smilingFaceWithHeartShapedEyes": 128525,
        "smilingFaceWithSunglasses": 128526,
        "smirkingFace": 128527,
        "neutralFace": 128528,
        "expressionlessFace": 128529,
        "unamusedFace": 128530,
        "faceWithColdSweat": 128531,
        "pensiveFace": 128532,
        "confusedFace": 128533,
        "confoundedFace": 128534,
        "kissingFace": 128535,
        "faceThrowingAKiss": 128536,
        "kissingFaceWithSmilingEyes": 128537,
        "kissingFaceWithClosedEyes": 128538,
        "faceWithStuckOutTongue": 128539,
        "faceWithStuckOutTongueAndWinkingEye": 128540,
        "faceWithStuckOutTongueAndTightlyClosedEyes": 128541,
        "disappointedFace": 128542,
        "worriedFace": 128543,
        "angryFace": 128544,
        "poutingFace": 128545,
        "cryingFace": 128546,
        "perseveringFace": 128547,
        "faceWithLookOfTriumph": 128548,
        "disappointedButRelievedFace": 128549,
        "frowningFaceWithOpenMouth": 128550,
        "anguishedFace": 128551,
        "fearfulFace": 128552,
        "wearyFace": 128553,
        "sleepyFace": 128554,
        "tiredFace": 128555,
        "grimacingFace": 128556,
        "loudlyCryingFace": 128557,
        "faceWithOpenMouth": 128558,
        "hushedFace": 128559,
        "faceWithOpenMouthAndColdSweat": 128560,
        "faceScreamingInFear": 128561,
        "astonishedFace": 128562,
        "flushedFace": 128563,
        "sleepingFace": 128564,
        "dizzyFace": 128565,
        "faceWithoutMouth": 128566,
        "faceWithMedicalMask": 128567,
        "grinningCatFaceWithSmilingEyes": 128568,
        "catFaceWithTearsOfJoy": 128569,
        "smilingCatFaceWithOpenMouth": 128570,
        "smilingCatFaceWithHeartShapedEyes": 128571,
        "catFaceWithWrySmile": 128572,
        "kissingCatFaceWithClosedEyes": 128573,
        "poutingCatFace": 128574,
        "cryingCatFace": 128575,
        "wearyCatFace": 128576,
        "slightlyFrowningFace": 128577,
        "slightlySmilingFace": 128578,
        "upsideDownFace": 128579,
        "faceWithRollingEyes": 128580,
        "faceWithNoGoodGesture": 128581,
        "faceWithOkGesture": 128582,
        "personBowingDeeply": 128583,
        "seeNoEvilMonkey": 128584,
        "hearNoEvilMonkey": 128585,
        "speakNoEvilMonkey": 128586,
        "happyPersonRaisingOneHand": 128587,
        "personRaisingBothHandsInCelebration": 128588,
        "personFrowning": 128589,
        "personWithPoutingFace": 128590,
        "personWithFoldedHands": 128591,
        "rocket": 128640,
        "helicopter": 128641,
        "steamLocomotive": 128642,
        "railwayCar": 128643,
        "highSpeedTrain": 128644,
        "highSpeedTrainWithBulletNose": 128645,
        "train": 128646,
        "metro": 128647,
        "lightRail": 128648,
        "station": 128649,
        "tram": 128650,
        "tramCar": 128651,
        "bus": 128652,
        "oncomingBus": 128653,
        "trolleybus": 128654,
        "busStop": 128655,
        "minibus": 128656,
        "ambulance": 128657,
        "fireEngine": 128658,
        "policeCar": 128659,
        "oncomingPoliceCar": 128660,
        "taxi": 128661,
        "oncomingTaxi": 128662,
        "automobile": 128663,
        "oncomingAutomobile": 128664,
        "recreationalVehicle": 128665,
        "deliveryTruck": 128666,
        "articulatedLorry": 128667,
        "tractor": 128668,
        "monorail": 128669,
        "mountainRailway": 128670,
        "suspensionRailway": 128671,
        "mountainCableway": 128672,
        "aerialTramway": 128673,
        "ship": 128674,
        "rowboat": 128675,
        "speedboat": 128676,
        "horizontalTrafficLight": 128677,
        "verticalTrafficLight": 128678,
        "constructionSign": 128679,
        "policeCarsRevolvingLight": 128680,
        "triangularFlagOnPost": 128681,
        "door": 128682,
        "noEntrySign": 128683,
        "smoking": 128684,
        "noSmoking": 128685,
        "putLitterInItsPlace": 128686,
        "doNotLitter": 128687,
        "potableWater": 128688,
        "nonPotableWater": 128689,
        "bicycle": 128690,
        "noBicycles": 128691,
        "bicyclist": 128692,
        "mountainBicyclist": 128693,
        "pedestrian": 128694,
        "noPedestrians": 128695,
        "childrenCrossing": 128696,
        "mens": 128697,
        "womens": 128698,
        "restroom": 128699,
        "trns:baby": 128700,
        "toilet": 128701,
        "waterCloset": 128702,
        "shower": 128703,
        "bath": 128704,
        "bathtub": 128705,
        "passportControl": 128706,
        "customs": 128707,
        "baggageClaim": 128708,
        "leftLuggage": 128709,
        "triangleWithRoundedCorners": 128710,
        "prohibitedSign": 128711,
        "circledInformationSource": 128712,
        "boys": 128713,
        "girls": 128714,
        "couchAndLamp": 128715,
        "sleepingAccommodation": 128716,
        "shoppingBags": 128717,
        "bellhopBell": 128718,
        "bed": 128719,
        "placeOfWorship": 128720,
        "octagonalSign": 128721,
        "shoppingTrolley": 128722,
        "stupa": 128723,
        "pagoda": 128724,
        "hinduTemple": 128725,
        "hammerAndWrench": 128736,
        "shield": 128737,
        "oilDrum": 128738,
        "motorway": 128739,
        "railwayTrack": 128740,
        "motorBoat": 128741,
        "upPointingMilitaryAirplane": 128742,
        "upPointingAirplane": 128743,
        "upPointingSmallAirplane": 128744,
        "smallAirplane": 128745,
        "northeastPointingAirplane": 128746,
        "airplaneDeparture": 128747,
        "airplaneArriving": 128748,
        "satellite": 128752,
        "oncomingFireEngine": 128753,
        "dieselLocomotive": 128754,
        "passengerShip": 128755,
        "scooter": 128756,
        "motorScooter": 128757,
        "canoe": 128758,
        "sled": 128759,
        "flyingSaucer": 128760,
        "skateboard": 128761,
        "autoRickshaw": 128762,
    }

    UNICODE_TO_NAME: typing.Dict[int, str] = {v: k for k, v in NAME_TO_UNICODE.items()}
