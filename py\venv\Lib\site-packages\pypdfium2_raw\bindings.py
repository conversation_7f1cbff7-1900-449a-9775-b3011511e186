R"""
Auto-generated by:
ctypesgen -l pdfium --runtime-libdirs . --no-system-libsearch --no-load-library --no-macro-guards --no-symbol-guards --symbol-rules 'if_needed=\w+_$|\w+_t$|_\w+' --headers fpdf_annot.h fpdf_attachment.h fpdf_catalog.h fpdf_dataavail.h fpdf_doc.h fpdf_edit.h fpdf_ext.h fpdf_flatten.h fpdf_formfill.h fpdf_fwlevent.h fpdf_javascript.h fpdf_ppo.h fpdf_progressive.h fpdf_save.h fpdf_searchex.h fpdf_signature.h fpdf_structtree.h fpdf_sysfontinfo.h fpdf_text.h fpdf_thumbnail.h fpdf_transformpage.h fpdfview.h -o '~/work/pypdfium2/pypdfium2/data/bindings/bindings.py'
"""

import ctypes
from ctypes import *


# -- Begin loader template --

import sys
import ctypes
import ctypes.util
import pathlib

def _find_library(name, dirs, search_sys):
    
    if sys.platform in ("win32", "cygwin", "msys"):
        patterns = ["{}.dll", "lib{}.dll", "{}"]
    elif sys.platform == "darwin":
        patterns = ["lib{}.dylib", "{}.dylib", "lib{}.so", "{}.so", "{}"]
    else:  # assume unix pattern or plain name
        patterns = ["lib{}.so", "{}.so", "{}"]
    
    for dir in dirs:
        dir = pathlib.Path(dir)
        if not dir.is_absolute():
            dir = (pathlib.Path(__file__).parent / dir).resolve(strict=False)
        for pat in patterns:
            libpath = dir / pat.format(name)
            if libpath.is_file():
                return str(libpath)
    
    libpath = ctypes.util.find_library(name) if search_sys else None
    if not libpath:
        raise ImportError(f"Could not find library '{name}' (dirs={dirs}, search_sys={search_sys})")
    
    return libpath

_libs_info, _libs = {}, {}

def _register_library(name, dllclass, **kwargs):
    libpath = _find_library(name, **kwargs)
    _libs_info[name] = {**kwargs, "path": libpath}
    _libs[name] = dllclass(libpath)

# -- End loader template --


# Load library 'pdfium'

_register_library(
    name = 'pdfium',
    dllclass = ctypes.CDLL,
    dirs = ['.'],
    search_sys = False,
)


# -- Begin header members --

# ./fpdfview.h: 59
enum_anon_2 = c_int

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_UNKNOWN = (-1)

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_FILL = 0

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_STROKE = 1

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_FILL_STROKE = 2

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_INVISIBLE = 3

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_FILL_CLIP = 4

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_STROKE_CLIP = 5

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_FILL_STROKE_CLIP = 6

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_CLIP = 7

# ./fpdfview.h: 59
FPDF_TEXTRENDERMODE_LAST = FPDF_TEXTRENDERMODE_CLIP

# ./fpdfview.h: 59
FPDF_TEXT_RENDERMODE = enum_anon_2

# ./fpdfview.h: 62
class struct_fpdf_action_t__ (Structure):
    pass

# ./fpdfview.h: 62
FPDF_ACTION = POINTER(struct_fpdf_action_t__)

# ./fpdfview.h: 63
class struct_fpdf_annotation_t__ (Structure):
    pass

# ./fpdfview.h: 63
FPDF_ANNOTATION = POINTER(struct_fpdf_annotation_t__)

# ./fpdfview.h: 64
class struct_fpdf_attachment_t__ (Structure):
    pass

# ./fpdfview.h: 64
FPDF_ATTACHMENT = POINTER(struct_fpdf_attachment_t__)

# ./fpdfview.h: 65
class struct_fpdf_avail_t__ (Structure):
    pass

# ./fpdfview.h: 65
FPDF_AVAIL = POINTER(struct_fpdf_avail_t__)

# ./fpdfview.h: 66
class struct_fpdf_bitmap_t__ (Structure):
    pass

# ./fpdfview.h: 66
FPDF_BITMAP = POINTER(struct_fpdf_bitmap_t__)

# ./fpdfview.h: 67
class struct_fpdf_bookmark_t__ (Structure):
    pass

# ./fpdfview.h: 67
FPDF_BOOKMARK = POINTER(struct_fpdf_bookmark_t__)

# ./fpdfview.h: 68
class struct_fpdf_clippath_t__ (Structure):
    pass

# ./fpdfview.h: 68
FPDF_CLIPPATH = POINTER(struct_fpdf_clippath_t__)

# ./fpdfview.h: 69
class struct_fpdf_dest_t__ (Structure):
    pass

# ./fpdfview.h: 69
FPDF_DEST = POINTER(struct_fpdf_dest_t__)

# ./fpdfview.h: 70
class struct_fpdf_document_t__ (Structure):
    pass

# ./fpdfview.h: 70
FPDF_DOCUMENT = POINTER(struct_fpdf_document_t__)

# ./fpdfview.h: 71
class struct_fpdf_font_t__ (Structure):
    pass

# ./fpdfview.h: 71
FPDF_FONT = POINTER(struct_fpdf_font_t__)

# ./fpdfview.h: 72
class struct_fpdf_form_handle_t__ (Structure):
    pass

# ./fpdfview.h: 72
FPDF_FORMHANDLE = POINTER(struct_fpdf_form_handle_t__)

# ./fpdfview.h: 73
class struct_fpdf_glyphpath_t__ (Structure):
    pass

# ./fpdfview.h: 73
FPDF_GLYPHPATH = POINTER(struct_fpdf_glyphpath_t__)

# ./fpdfview.h: 74
class struct_fpdf_javascript_action_t (Structure):
    pass

# ./fpdfview.h: 74
FPDF_JAVASCRIPT_ACTION = POINTER(struct_fpdf_javascript_action_t)

# ./fpdfview.h: 75
class struct_fpdf_link_t__ (Structure):
    pass

# ./fpdfview.h: 75
FPDF_LINK = POINTER(struct_fpdf_link_t__)

# ./fpdfview.h: 76
class struct_fpdf_page_t__ (Structure):
    pass

# ./fpdfview.h: 76
FPDF_PAGE = POINTER(struct_fpdf_page_t__)

# ./fpdfview.h: 77
class struct_fpdf_pagelink_t__ (Structure):
    pass

# ./fpdfview.h: 77
FPDF_PAGELINK = POINTER(struct_fpdf_pagelink_t__)

# ./fpdfview.h: 78
class struct_fpdf_pageobject_t__ (Structure):
    pass

# ./fpdfview.h: 78
FPDF_PAGEOBJECT = POINTER(struct_fpdf_pageobject_t__)

# ./fpdfview.h: 79
class struct_fpdf_pageobjectmark_t__ (Structure):
    pass

# ./fpdfview.h: 79
FPDF_PAGEOBJECTMARK = POINTER(struct_fpdf_pageobjectmark_t__)

# ./fpdfview.h: 80
class struct_fpdf_pagerange_t__ (Structure):
    pass

# ./fpdfview.h: 80
FPDF_PAGERANGE = POINTER(struct_fpdf_pagerange_t__)

# ./fpdfview.h: 81
class struct_fpdf_pathsegment_t (Structure):
    pass

# ./fpdfview.h: 81
FPDF_PATHSEGMENT = POINTER(struct_fpdf_pathsegment_t)

# ./fpdfview.h: 82
class struct_fpdf_schhandle_t__ (Structure):
    pass

# ./fpdfview.h: 82
FPDF_SCHHANDLE = POINTER(struct_fpdf_schhandle_t__)

# ./fpdfview.h: 83
class struct_fpdf_signature_t__ (Structure):
    pass

# ./fpdfview.h: 83
FPDF_SIGNATURE = POINTER(struct_fpdf_signature_t__)

# ./fpdfview.h: 84
FPDF_SKIA_CANVAS = POINTER(None)

# ./fpdfview.h: 85
class struct_fpdf_structelement_t__ (Structure):
    pass

# ./fpdfview.h: 85
FPDF_STRUCTELEMENT = POINTER(struct_fpdf_structelement_t__)

# ./fpdfview.h: 86
class struct_fpdf_structelement_attr_t__ (Structure):
    pass

# ./fpdfview.h: 86
FPDF_STRUCTELEMENT_ATTR = POINTER(struct_fpdf_structelement_attr_t__)

# ./fpdfview.h: 87
class struct_fpdf_structelement_attr_value_t__ (Structure):
    pass

# ./fpdfview.h: 87
FPDF_STRUCTELEMENT_ATTR_VALUE = POINTER(struct_fpdf_structelement_attr_value_t__)

# ./fpdfview.h: 89
class struct_fpdf_structtree_t__ (Structure):
    pass

# ./fpdfview.h: 89
FPDF_STRUCTTREE = POINTER(struct_fpdf_structtree_t__)

# ./fpdfview.h: 90
class struct_fpdf_textpage_t__ (Structure):
    pass

# ./fpdfview.h: 90
FPDF_TEXTPAGE = POINTER(struct_fpdf_textpage_t__)

# ./fpdfview.h: 91
class struct_fpdf_widget_t__ (Structure):
    pass

# ./fpdfview.h: 91
FPDF_WIDGET = POINTER(struct_fpdf_widget_t__)

# ./fpdfview.h: 92
class struct_fpdf_xobject_t__ (Structure):
    pass

# ./fpdfview.h: 92
FPDF_XOBJECT = POINTER(struct_fpdf_xobject_t__)

# ./fpdfview.h: 95
FPDF_BOOL = c_int

# ./fpdfview.h: 96
FPDF_RESULT = c_int

# ./fpdfview.h: 97
FPDF_DWORD = c_ulong

# ./fpdfview.h: 98
FS_FLOAT = c_float

# ./fpdfview.h: 106
enum__FPDF_DUPLEXTYPE_ = c_int

# ./fpdfview.h: 106
DuplexUndefined = 0

# ./fpdfview.h: 106
Simplex = (DuplexUndefined + 1)

# ./fpdfview.h: 106
DuplexFlipShortEdge = (Simplex + 1)

# ./fpdfview.h: 106
DuplexFlipLongEdge = (DuplexFlipShortEdge + 1)

# ./fpdfview.h: 106
FPDF_DUPLEXTYPE = enum__FPDF_DUPLEXTYPE_

# ./fpdfview.h: 109
FPDF_WCHAR = c_ushort

# ./fpdfview.h: 115
FPDF_BYTESTRING = POINTER(c_char)

# ./fpdfview.h: 119
FPDF_WIDESTRING = POINTER(FPDF_WCHAR)

# ./fpdfview.h: 127
class struct_FPDF_BSTR_ (Structure):
    __slots__ = ['str', 'len']

struct_FPDF_BSTR_._fields_ = [
    ('str', POINTER(c_char)),
    ('len', c_int),
]

# ./fpdfview.h: 127
FPDF_BSTR = struct_FPDF_BSTR_

# ./fpdfview.h: 136
FPDF_STRING = POINTER(c_char)

# ./fpdfview.h: 153
class struct__FS_MATRIX_ (Structure):
    __slots__ = ['a', 'b', 'c', 'd', 'e', 'f']

struct__FS_MATRIX_._fields_ = [
    ('a', c_float),
    ('b', c_float),
    ('c', c_float),
    ('d', c_float),
    ('e', c_float),
    ('f', c_float),
]

# ./fpdfview.h: 153
FS_MATRIX = struct__FS_MATRIX_

# ./fpdfview.h: 156
class struct__FS_RECTF_ (Structure):
    __slots__ = ['left', 'top', 'right', 'bottom']

struct__FS_RECTF_._fields_ = [
    ('left', c_float),
    ('top', c_float),
    ('right', c_float),
    ('bottom', c_float),
]

# ./fpdfview.h: 165
FS_LPRECTF = POINTER(struct__FS_RECTF_)

# ./fpdfview.h: 165
FS_RECTF = struct__FS_RECTF_

# ./fpdfview.h: 168
FS_LPCRECTF = POINTER(FS_RECTF)

# ./fpdfview.h: 171
class struct_FS_SIZEF_ (Structure):
    __slots__ = ['width', 'height']

struct_FS_SIZEF_._fields_ = [
    ('width', c_float),
    ('height', c_float),
]

# ./fpdfview.h: 174
FS_LPSIZEF = POINTER(struct_FS_SIZEF_)

# ./fpdfview.h: 174
FS_SIZEF = struct_FS_SIZEF_

# ./fpdfview.h: 177
FS_LPCSIZEF = POINTER(FS_SIZEF)

# ./fpdfview.h: 180
class struct_FS_POINTF_ (Structure):
    __slots__ = ['x', 'y']

struct_FS_POINTF_._fields_ = [
    ('x', c_float),
    ('y', c_float),
]

# ./fpdfview.h: 183
FS_LPPOINTF = POINTER(struct_FS_POINTF_)

# ./fpdfview.h: 183
FS_POINTF = struct_FS_POINTF_

# ./fpdfview.h: 186
FS_LPCPOINTF = POINTER(FS_POINTF)

# ./fpdfview.h: 197
class struct__FS_QUADPOINTSF (Structure):
    __slots__ = ['x1', 'y1', 'x2', 'y2', 'x3', 'y3', 'x4', 'y4']

struct__FS_QUADPOINTSF._fields_ = [
    ('x1', FS_FLOAT),
    ('y1', FS_FLOAT),
    ('x2', FS_FLOAT),
    ('y2', FS_FLOAT),
    ('x3', FS_FLOAT),
    ('y3', FS_FLOAT),
    ('x4', FS_FLOAT),
    ('y4', FS_FLOAT),
]

# ./fpdfview.h: 197
FS_QUADPOINTSF = struct__FS_QUADPOINTSF

# ./fpdfview.h: 200
FPDF_ANNOTATION_SUBTYPE = c_int

# ./fpdfview.h: 201
FPDF_ANNOT_APPEARANCEMODE = c_int

# ./fpdfview.h: 204
FPDF_OBJECT_TYPE = c_int

# ./fpdfview.h: 244
enum_anon_3 = c_int

# ./fpdfview.h: 244
FPDF_RENDERERTYPE_AGG = 0

# ./fpdfview.h: 244
FPDF_RENDERERTYPE_SKIA = 1

# ./fpdfview.h: 244
FPDF_RENDERER_TYPE = enum_anon_3

# ./fpdfview.h: 283
class struct_FPDF_LIBRARY_CONFIG_ (Structure):
    __slots__ = ['version', 'm_pUserFontPaths', 'm_pIsolate', 'm_v8EmbedderSlot', 'm_pPlatform', 'm_RendererType']

struct_FPDF_LIBRARY_CONFIG_._fields_ = [
    ('version', c_int),
    ('m_pUserFontPaths', POINTER(POINTER(c_char))),
    ('m_pIsolate', POINTER(None)),
    ('m_v8EmbedderSlot', c_uint),
    ('m_pPlatform', POINTER(None)),
    ('m_RendererType', FPDF_RENDERER_TYPE),
]

# ./fpdfview.h: 283
FPDF_LIBRARY_CONFIG = struct_FPDF_LIBRARY_CONFIG_

# ./fpdfview.h: 295
FPDF_InitLibraryWithConfig = _libs['pdfium']['FPDF_InitLibraryWithConfig']
FPDF_InitLibraryWithConfig.argtypes = [POINTER(FPDF_LIBRARY_CONFIG)]
FPDF_InitLibraryWithConfig.restype = None

# ./fpdfview.h: 308
FPDF_InitLibrary = _libs['pdfium']['FPDF_InitLibrary']
FPDF_InitLibrary.argtypes = []
FPDF_InitLibrary.restype = None

# ./fpdfview.h: 324
FPDF_DestroyLibrary = _libs['pdfium']['FPDF_DestroyLibrary']
FPDF_DestroyLibrary.argtypes = []
FPDF_DestroyLibrary.restype = None

# ./fpdfview.h: 337
FPDF_SetSandBoxPolicy = _libs['pdfium']['FPDF_SetSandBoxPolicy']
FPDF_SetSandBoxPolicy.argtypes = [FPDF_DWORD, FPDF_BOOL]
FPDF_SetSandBoxPolicy.restype = None

# ./fpdfview.h: 391
FPDF_LoadDocument = _libs['pdfium']['FPDF_LoadDocument']
FPDF_LoadDocument.argtypes = [FPDF_STRING, FPDF_BYTESTRING]
FPDF_LoadDocument.restype = FPDF_DOCUMENT

# ./fpdfview.h: 415
FPDF_LoadMemDocument = _libs['pdfium']['FPDF_LoadMemDocument']
FPDF_LoadMemDocument.argtypes = [POINTER(None), c_int, FPDF_BYTESTRING]
FPDF_LoadMemDocument.restype = FPDF_DOCUMENT

# ./fpdfview.h: 440
FPDF_LoadMemDocument64 = _libs['pdfium']['FPDF_LoadMemDocument64']
FPDF_LoadMemDocument64.argtypes = [POINTER(None), c_size_t, FPDF_BYTESTRING]
FPDF_LoadMemDocument64.restype = FPDF_DOCUMENT

# ./fpdfview.h: 464
class struct_anon_4 (Structure):
    __slots__ = ['m_FileLen', 'm_GetBlock', 'm_Param']

struct_anon_4._fields_ = [
    ('m_FileLen', c_ulong),
    ('m_GetBlock', CFUNCTYPE(c_int, POINTER(None), c_ulong, POINTER(c_ubyte), c_ulong)),
    ('m_Param', POINTER(None)),
]

# ./fpdfview.h: 464
FPDF_FILEACCESS = struct_anon_4

# ./fpdfview.h: 544
class struct_FPDF_FILEHANDLER_ (Structure):
    __slots__ = ['clientData', 'Release', 'GetSize', 'ReadBlock', 'WriteBlock', 'Flush', 'Truncate']

struct_FPDF_FILEHANDLER_._fields_ = [
    ('clientData', POINTER(None)),
    ('Release', CFUNCTYPE(None, POINTER(None))),
    ('GetSize', CFUNCTYPE(FPDF_DWORD, POINTER(None))),
    ('ReadBlock', CFUNCTYPE(FPDF_RESULT, POINTER(None), FPDF_DWORD, POINTER(None), FPDF_DWORD)),
    ('WriteBlock', CFUNCTYPE(FPDF_RESULT, POINTER(None), FPDF_DWORD, POINTER(None), FPDF_DWORD)),
    ('Flush', CFUNCTYPE(FPDF_RESULT, POINTER(None))),
    ('Truncate', CFUNCTYPE(FPDF_RESULT, POINTER(None), FPDF_DWORD)),
]

# ./fpdfview.h: 544
FPDF_FILEHANDLER = struct_FPDF_FILEHANDLER_

# ./fpdfview.h: 567
FPDF_LoadCustomDocument = _libs['pdfium']['FPDF_LoadCustomDocument']
FPDF_LoadCustomDocument.argtypes = [POINTER(FPDF_FILEACCESS), FPDF_BYTESTRING]
FPDF_LoadCustomDocument.restype = FPDF_DOCUMENT

# ./fpdfview.h: 580
FPDF_GetFileVersion = _libs['pdfium']['FPDF_GetFileVersion']
FPDF_GetFileVersion.argtypes = [FPDF_DOCUMENT, POINTER(c_int)]
FPDF_GetFileVersion.restype = FPDF_BOOL

# ./fpdfview.h: 605
FPDF_GetLastError = _libs['pdfium']['FPDF_GetLastError']
FPDF_GetLastError.argtypes = []
FPDF_GetLastError.restype = c_ulong

# ./fpdfview.h: 620
FPDF_DocumentHasValidCrossReferenceTable = _libs['pdfium']['FPDF_DocumentHasValidCrossReferenceTable']
FPDF_DocumentHasValidCrossReferenceTable.argtypes = [FPDF_DOCUMENT]
FPDF_DocumentHasValidCrossReferenceTable.restype = FPDF_BOOL

# ./fpdfview.h: 637
FPDF_GetTrailerEnds = _libs['pdfium']['FPDF_GetTrailerEnds']
FPDF_GetTrailerEnds.argtypes = [FPDF_DOCUMENT, POINTER(c_uint), c_ulong]
FPDF_GetTrailerEnds.restype = c_ulong

# ./fpdfview.h: 650
FPDF_GetDocPermissions = _libs['pdfium']['FPDF_GetDocPermissions']
FPDF_GetDocPermissions.argtypes = [FPDF_DOCUMENT]
FPDF_GetDocPermissions.restype = c_ulong

# ./fpdfview.h: 662
FPDF_GetDocUserPermissions = _libs['pdfium']['FPDF_GetDocUserPermissions']
FPDF_GetDocUserPermissions.argtypes = [FPDF_DOCUMENT]
FPDF_GetDocUserPermissions.restype = c_ulong

# ./fpdfview.h: 673
FPDF_GetSecurityHandlerRevision = _libs['pdfium']['FPDF_GetSecurityHandlerRevision']
FPDF_GetSecurityHandlerRevision.argtypes = [FPDF_DOCUMENT]
FPDF_GetSecurityHandlerRevision.restype = c_int

# ./fpdfview.h: 681
FPDF_GetPageCount = _libs['pdfium']['FPDF_GetPageCount']
FPDF_GetPageCount.argtypes = [FPDF_DOCUMENT]
FPDF_GetPageCount.restype = c_int

# ./fpdfview.h: 693
FPDF_LoadPage = _libs['pdfium']['FPDF_LoadPage']
FPDF_LoadPage.argtypes = [FPDF_DOCUMENT, c_int]
FPDF_LoadPage.restype = FPDF_PAGE

# ./fpdfview.h: 704
FPDF_GetPageWidthF = _libs['pdfium']['FPDF_GetPageWidthF']
FPDF_GetPageWidthF.argtypes = [FPDF_PAGE]
FPDF_GetPageWidthF.restype = c_float

# ./fpdfview.h: 716
FPDF_GetPageWidth = _libs['pdfium']['FPDF_GetPageWidth']
FPDF_GetPageWidth.argtypes = [FPDF_PAGE]
FPDF_GetPageWidth.restype = c_double

# ./fpdfview.h: 726
FPDF_GetPageHeightF = _libs['pdfium']['FPDF_GetPageHeightF']
FPDF_GetPageHeightF.argtypes = [FPDF_PAGE]
FPDF_GetPageHeightF.restype = c_float

# ./fpdfview.h: 738
FPDF_GetPageHeight = _libs['pdfium']['FPDF_GetPageHeight']
FPDF_GetPageHeight.argtypes = [FPDF_PAGE]
FPDF_GetPageHeight.restype = c_double

# ./fpdfview.h: 750
FPDF_GetPageBoundingBox = _libs['pdfium']['FPDF_GetPageBoundingBox']
FPDF_GetPageBoundingBox.argtypes = [FPDF_PAGE, POINTER(FS_RECTF)]
FPDF_GetPageBoundingBox.restype = FPDF_BOOL

# ./fpdfview.h: 764
FPDF_GetPageSizeByIndexF = _libs['pdfium']['FPDF_GetPageSizeByIndexF']
FPDF_GetPageSizeByIndexF.argtypes = [FPDF_DOCUMENT, c_int, POINTER(FS_SIZEF)]
FPDF_GetPageSizeByIndexF.restype = FPDF_BOOL

# ./fpdfview.h: 782
FPDF_GetPageSizeByIndex = _libs['pdfium']['FPDF_GetPageSizeByIndex']
FPDF_GetPageSizeByIndex.argtypes = [FPDF_DOCUMENT, c_int, POINTER(c_double), POINTER(c_double)]
FPDF_GetPageSizeByIndex.restype = c_int

# ./fpdfview.h: 830
class struct_FPDF_COLORSCHEME_ (Structure):
    __slots__ = ['path_fill_color', 'path_stroke_color', 'text_fill_color', 'text_stroke_color']

struct_FPDF_COLORSCHEME_._fields_ = [
    ('path_fill_color', FPDF_DWORD),
    ('path_stroke_color', FPDF_DWORD),
    ('text_fill_color', FPDF_DWORD),
    ('text_stroke_color', FPDF_DWORD),
]

# ./fpdfview.h: 830
FPDF_COLORSCHEME = struct_FPDF_COLORSCHEME_

# ./fpdfview.h: 891
FPDF_RenderPageBitmap = _libs['pdfium']['FPDF_RenderPageBitmap']
FPDF_RenderPageBitmap.argtypes = [FPDF_BITMAP, FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_int]
FPDF_RenderPageBitmap.restype = None

# ./fpdfview.h: 919
FPDF_RenderPageBitmapWithMatrix = _libs['pdfium']['FPDF_RenderPageBitmapWithMatrix']
FPDF_RenderPageBitmapWithMatrix.argtypes = [FPDF_BITMAP, FPDF_PAGE, POINTER(FS_MATRIX), POINTER(FS_RECTF), c_int]
FPDF_RenderPageBitmapWithMatrix.restype = None

# ./fpdfview.h: 948
FPDF_ClosePage = _libs['pdfium']['FPDF_ClosePage']
FPDF_ClosePage.argtypes = [FPDF_PAGE]
FPDF_ClosePage.restype = None

# ./fpdfview.h: 956
FPDF_CloseDocument = _libs['pdfium']['FPDF_CloseDocument']
FPDF_CloseDocument.argtypes = [FPDF_DOCUMENT]
FPDF_CloseDocument.restype = None

# ./fpdfview.h: 999
FPDF_DeviceToPage = _libs['pdfium']['FPDF_DeviceToPage']
FPDF_DeviceToPage.argtypes = [FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_int, c_int, POINTER(c_double), POINTER(c_double)]
FPDF_DeviceToPage.restype = FPDF_BOOL

# ./fpdfview.h: 1036
FPDF_PageToDevice = _libs['pdfium']['FPDF_PageToDevice']
FPDF_PageToDevice.argtypes = [FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_double, c_double, POINTER(c_int), POINTER(c_int)]
FPDF_PageToDevice.restype = FPDF_BOOL

# ./fpdfview.h: 1077
FPDFBitmap_Create = _libs['pdfium']['FPDFBitmap_Create']
FPDFBitmap_Create.argtypes = [c_int, c_int, c_int]
FPDFBitmap_Create.restype = FPDF_BITMAP

# ./fpdfview.h: 1126
FPDFBitmap_CreateEx = _libs['pdfium']['FPDFBitmap_CreateEx']
FPDFBitmap_CreateEx.argtypes = [c_int, c_int, c_int, POINTER(None), c_int]
FPDFBitmap_CreateEx.restype = FPDF_BITMAP

# ./fpdfview.h: 1142
FPDFBitmap_GetFormat = _libs['pdfium']['FPDFBitmap_GetFormat']
FPDFBitmap_GetFormat.argtypes = [FPDF_BITMAP]
FPDFBitmap_GetFormat.restype = c_int

# ./fpdfview.h: 1168
FPDFBitmap_FillRect = _libs['pdfium']['FPDFBitmap_FillRect']
FPDFBitmap_FillRect.argtypes = [FPDF_BITMAP, c_int, c_int, c_int, c_int, FPDF_DWORD]
FPDFBitmap_FillRect.restype = FPDF_BOOL

# ./fpdfview.h: 1190
FPDFBitmap_GetBuffer = _libs['pdfium']['FPDFBitmap_GetBuffer']
FPDFBitmap_GetBuffer.argtypes = [FPDF_BITMAP]
FPDFBitmap_GetBuffer.restype = POINTER(None)

# ./fpdfview.h: 1199
FPDFBitmap_GetWidth = _libs['pdfium']['FPDFBitmap_GetWidth']
FPDFBitmap_GetWidth.argtypes = [FPDF_BITMAP]
FPDFBitmap_GetWidth.restype = c_int

# ./fpdfview.h: 1208
FPDFBitmap_GetHeight = _libs['pdfium']['FPDFBitmap_GetHeight']
FPDFBitmap_GetHeight.argtypes = [FPDF_BITMAP]
FPDFBitmap_GetHeight.restype = c_int

# ./fpdfview.h: 1219
FPDFBitmap_GetStride = _libs['pdfium']['FPDFBitmap_GetStride']
FPDFBitmap_GetStride.argtypes = [FPDF_BITMAP]
FPDFBitmap_GetStride.restype = c_int

# ./fpdfview.h: 1231
FPDFBitmap_Destroy = _libs['pdfium']['FPDFBitmap_Destroy']
FPDFBitmap_Destroy.argtypes = [FPDF_BITMAP]
FPDFBitmap_Destroy.restype = None

# ./fpdfview.h: 1240
FPDF_VIEWERREF_GetPrintScaling = _libs['pdfium']['FPDF_VIEWERREF_GetPrintScaling']
FPDF_VIEWERREF_GetPrintScaling.argtypes = [FPDF_DOCUMENT]
FPDF_VIEWERREF_GetPrintScaling.restype = FPDF_BOOL

# ./fpdfview.h: 1249
FPDF_VIEWERREF_GetNumCopies = _libs['pdfium']['FPDF_VIEWERREF_GetNumCopies']
FPDF_VIEWERREF_GetNumCopies.argtypes = [FPDF_DOCUMENT]
FPDF_VIEWERREF_GetNumCopies.restype = c_int

# ./fpdfview.h: 1258
FPDF_VIEWERREF_GetPrintPageRange = _libs['pdfium']['FPDF_VIEWERREF_GetPrintPageRange']
FPDF_VIEWERREF_GetPrintPageRange.argtypes = [FPDF_DOCUMENT]
FPDF_VIEWERREF_GetPrintPageRange.restype = FPDF_PAGERANGE

# ./fpdfview.h: 1268
FPDF_VIEWERREF_GetPrintPageRangeCount = _libs['pdfium']['FPDF_VIEWERREF_GetPrintPageRangeCount']
FPDF_VIEWERREF_GetPrintPageRangeCount.argtypes = [FPDF_PAGERANGE]
FPDF_VIEWERREF_GetPrintPageRangeCount.restype = c_size_t

# ./fpdfview.h: 1280
FPDF_VIEWERREF_GetPrintPageRangeElement = _libs['pdfium']['FPDF_VIEWERREF_GetPrintPageRangeElement']
FPDF_VIEWERREF_GetPrintPageRangeElement.argtypes = [FPDF_PAGERANGE, c_size_t]
FPDF_VIEWERREF_GetPrintPageRangeElement.restype = c_int

# ./fpdfview.h: 1290
FPDF_VIEWERREF_GetDuplex = _libs['pdfium']['FPDF_VIEWERREF_GetDuplex']
FPDF_VIEWERREF_GetDuplex.argtypes = [FPDF_DOCUMENT]
FPDF_VIEWERREF_GetDuplex.restype = FPDF_DUPLEXTYPE

# ./fpdfview.h: 1308
FPDF_VIEWERREF_GetName = _libs['pdfium']['FPDF_VIEWERREF_GetName']
FPDF_VIEWERREF_GetName.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING, POINTER(c_char), c_ulong]
FPDF_VIEWERREF_GetName.restype = c_ulong

# ./fpdfview.h: 1320
FPDF_CountNamedDests = _libs['pdfium']['FPDF_CountNamedDests']
FPDF_CountNamedDests.argtypes = [FPDF_DOCUMENT]
FPDF_CountNamedDests.restype = FPDF_DWORD

# ./fpdfview.h: 1330
FPDF_GetNamedDestByName = _libs['pdfium']['FPDF_GetNamedDestByName']
FPDF_GetNamedDestByName.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING]
FPDF_GetNamedDestByName.restype = FPDF_DEST

# ./fpdfview.h: 1353
FPDF_GetNamedDest = _libs['pdfium']['FPDF_GetNamedDest']
FPDF_GetNamedDest.argtypes = [FPDF_DOCUMENT, c_int, POINTER(None), POINTER(c_long)]
FPDF_GetNamedDest.restype = FPDF_DEST

# ./fpdfview.h: 1365
FPDF_GetXFAPacketCount = _libs['pdfium']['FPDF_GetXFAPacketCount']
FPDF_GetXFAPacketCount.argtypes = [FPDF_DOCUMENT]
FPDF_GetXFAPacketCount.restype = c_int

# ./fpdfview.h: 1383
FPDF_GetXFAPacketName = _libs['pdfium']['FPDF_GetXFAPacketName']
FPDF_GetXFAPacketName.argtypes = [FPDF_DOCUMENT, c_int, POINTER(None), c_ulong]
FPDF_GetXFAPacketName.restype = c_ulong

# ./fpdfview.h: 1410
FPDF_GetXFAPacketContent = _libs['pdfium']['FPDF_GetXFAPacketContent']
FPDF_GetXFAPacketContent.argtypes = [FPDF_DOCUMENT, c_int, POINTER(None), c_ulong, POINTER(c_ulong)]
FPDF_GetXFAPacketContent.restype = FPDF_BOOL

# ./fpdf_formfill.h: 52
class struct__IPDF_JsPlatform (Structure):
    __slots__ = ['version', 'app_alert', 'app_beep', 'app_response', 'Doc_getFilePath', 'Doc_mail', 'Doc_print', 'Doc_submitForm', 'Doc_gotoPage', 'Field_browse', 'm_pFormfillinfo', 'm_isolate', 'm_v8EmbedderSlot']

struct__IPDF_JsPlatform._fields_ = [
    ('version', c_int),
    ('app_alert', CFUNCTYPE(c_int, POINTER(struct__IPDF_JsPlatform), FPDF_WIDESTRING, FPDF_WIDESTRING, c_int, c_int)),
    ('app_beep', CFUNCTYPE(None, POINTER(struct__IPDF_JsPlatform), c_int)),
    ('app_response', CFUNCTYPE(c_int, POINTER(struct__IPDF_JsPlatform), FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_BOOL, POINTER(None), c_int)),
    ('Doc_getFilePath', CFUNCTYPE(c_int, POINTER(struct__IPDF_JsPlatform), POINTER(None), c_int)),
    ('Doc_mail', CFUNCTYPE(None, POINTER(struct__IPDF_JsPlatform), POINTER(None), c_int, FPDF_BOOL, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING)),
    ('Doc_print', CFUNCTYPE(None, POINTER(struct__IPDF_JsPlatform), FPDF_BOOL, c_int, c_int, FPDF_BOOL, FPDF_BOOL, FPDF_BOOL, FPDF_BOOL, FPDF_BOOL)),
    ('Doc_submitForm', CFUNCTYPE(None, POINTER(struct__IPDF_JsPlatform), POINTER(None), c_int, FPDF_WIDESTRING)),
    ('Doc_gotoPage', CFUNCTYPE(None, POINTER(struct__IPDF_JsPlatform), c_int)),
    ('Field_browse', CFUNCTYPE(c_int, POINTER(struct__IPDF_JsPlatform), POINTER(None), c_int)),
    ('m_pFormfillinfo', POINTER(None)),
    ('m_isolate', POINTER(None)),
    ('m_v8EmbedderSlot', c_uint),
]

# ./fpdf_formfill.h: 300
IPDF_JSPLATFORM = struct__IPDF_JsPlatform

# ./fpdf_formfill.h: 316
TimerCallback = CFUNCTYPE(None, c_int)

# ./fpdf_formfill.h: 328
class struct__FPDF_SYSTEMTIME (Structure):
    __slots__ = ['wYear', 'wMonth', 'wDayOfWeek', 'wDay', 'wHour', 'wMinute', 'wSecond', 'wMilliseconds']

struct__FPDF_SYSTEMTIME._fields_ = [
    ('wYear', c_ushort),
    ('wMonth', c_ushort),
    ('wDayOfWeek', c_ushort),
    ('wDay', c_ushort),
    ('wHour', c_ushort),
    ('wMinute', c_ushort),
    ('wSecond', c_ushort),
    ('wMilliseconds', c_ushort),
]

# ./fpdf_formfill.h: 328
FPDF_SYSTEMTIME = struct__FPDF_SYSTEMTIME

# ./fpdf_formfill.h: 350
class struct__FPDF_FORMFILLINFO (Structure):
    __slots__ = ['version', 'Release', 'FFI_Invalidate', 'FFI_OutputSelectedRect', 'FFI_SetCursor', 'FFI_SetTimer', 'FFI_KillTimer', 'FFI_GetLocalTime', 'FFI_OnChange', 'FFI_GetPage', 'FFI_GetCurrentPage', 'FFI_GetRotation', 'FFI_ExecuteNamedAction', 'FFI_SetTextFieldFocus', 'FFI_DoURIAction', 'FFI_DoGoToAction', 'm_pJsPlatform', 'xfa_disabled', 'FFI_DisplayCaret', 'FFI_GetCurrentPageIndex', 'FFI_SetCurrentPage', 'FFI_GotoURL', 'FFI_GetPageViewRect', 'FFI_PageEvent', 'FFI_PopupMenu', 'FFI_OpenFile', 'FFI_EmailTo', 'FFI_UploadTo', 'FFI_GetPlatform', 'FFI_GetLanguage', 'FFI_DownloadFromURL', 'FFI_PostRequestURL', 'FFI_PutRequestURL', 'FFI_OnFocusChange', 'FFI_DoURIActionWithKeyboardModifier']

struct__FPDF_FORMFILLINFO._fields_ = [
    ('version', c_int),
    ('Release', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO))),
    ('FFI_Invalidate', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE, c_double, c_double, c_double, c_double)),
    ('FFI_OutputSelectedRect', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE, c_double, c_double, c_double, c_double)),
    ('FFI_SetCursor', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), c_int)),
    ('FFI_SetTimer', CFUNCTYPE(c_int, POINTER(struct__FPDF_FORMFILLINFO), c_int, TimerCallback)),
    ('FFI_KillTimer', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), c_int)),
    ('FFI_GetLocalTime', CFUNCTYPE(FPDF_SYSTEMTIME, POINTER(struct__FPDF_FORMFILLINFO))),
    ('FFI_OnChange', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO))),
    ('FFI_GetPage', CFUNCTYPE(FPDF_PAGE, POINTER(struct__FPDF_FORMFILLINFO), FPDF_DOCUMENT, c_int)),
    ('FFI_GetCurrentPage', CFUNCTYPE(FPDF_PAGE, POINTER(struct__FPDF_FORMFILLINFO), FPDF_DOCUMENT)),
    ('FFI_GetRotation', CFUNCTYPE(c_int, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE)),
    ('FFI_ExecuteNamedAction', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_BYTESTRING)),
    ('FFI_SetTextFieldFocus', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_WIDESTRING, FPDF_DWORD, FPDF_BOOL)),
    ('FFI_DoURIAction', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_BYTESTRING)),
    ('FFI_DoGoToAction', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), c_int, c_int, POINTER(c_float), c_int)),
    ('m_pJsPlatform', POINTER(IPDF_JSPLATFORM)),
    ('xfa_disabled', FPDF_BOOL),
    ('FFI_DisplayCaret', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE, FPDF_BOOL, c_double, c_double, c_double, c_double)),
    ('FFI_GetCurrentPageIndex', CFUNCTYPE(c_int, POINTER(struct__FPDF_FORMFILLINFO), FPDF_DOCUMENT)),
    ('FFI_SetCurrentPage', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_DOCUMENT, c_int)),
    ('FFI_GotoURL', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_DOCUMENT, FPDF_WIDESTRING)),
    ('FFI_GetPageViewRect', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE, POINTER(c_double), POINTER(c_double), POINTER(c_double), POINTER(c_double))),
    ('FFI_PageEvent', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), c_int, FPDF_DWORD)),
    ('FFI_PopupMenu', CFUNCTYPE(FPDF_BOOL, POINTER(struct__FPDF_FORMFILLINFO), FPDF_PAGE, FPDF_WIDGET, c_int, c_float, c_float)),
    ('FFI_OpenFile', CFUNCTYPE(POINTER(FPDF_FILEHANDLER), POINTER(struct__FPDF_FORMFILLINFO), c_int, FPDF_WIDESTRING, POINTER(c_char))),
    ('FFI_EmailTo', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), POINTER(FPDF_FILEHANDLER), FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING)),
    ('FFI_UploadTo', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), POINTER(FPDF_FILEHANDLER), c_int, FPDF_WIDESTRING)),
    ('FFI_GetPlatform', CFUNCTYPE(c_int, POINTER(struct__FPDF_FORMFILLINFO), POINTER(None), c_int)),
    ('FFI_GetLanguage', CFUNCTYPE(c_int, POINTER(struct__FPDF_FORMFILLINFO), POINTER(None), c_int)),
    ('FFI_DownloadFromURL', CFUNCTYPE(POINTER(FPDF_FILEHANDLER), POINTER(struct__FPDF_FORMFILLINFO), FPDF_WIDESTRING)),
    ('FFI_PostRequestURL', CFUNCTYPE(FPDF_BOOL, POINTER(struct__FPDF_FORMFILLINFO), FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING, POINTER(FPDF_BSTR))),
    ('FFI_PutRequestURL', CFUNCTYPE(FPDF_BOOL, POINTER(struct__FPDF_FORMFILLINFO), FPDF_WIDESTRING, FPDF_WIDESTRING, FPDF_WIDESTRING)),
    ('FFI_OnFocusChange', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_ANNOTATION, c_int)),
    ('FFI_DoURIActionWithKeyboardModifier', CFUNCTYPE(None, POINTER(struct__FPDF_FORMFILLINFO), FPDF_BYTESTRING, c_int)),
]

# ./fpdf_formfill.h: 1044
FPDF_FORMFILLINFO = struct__FPDF_FORMFILLINFO

# ./fpdf_formfill.h: 1058
FPDFDOC_InitFormFillEnvironment = _libs['pdfium']['FPDFDOC_InitFormFillEnvironment']
FPDFDOC_InitFormFillEnvironment.argtypes = [FPDF_DOCUMENT, POINTER(FPDF_FORMFILLINFO)]
FPDFDOC_InitFormFillEnvironment.restype = FPDF_FORMHANDLE

# ./fpdf_formfill.h: 1071
FPDFDOC_ExitFormFillEnvironment = _libs['pdfium']['FPDFDOC_ExitFormFillEnvironment']
FPDFDOC_ExitFormFillEnvironment.argtypes = [FPDF_FORMHANDLE]
FPDFDOC_ExitFormFillEnvironment.restype = None

# ./fpdf_formfill.h: 1082
FORM_OnAfterLoadPage = _libs['pdfium']['FORM_OnAfterLoadPage']
FORM_OnAfterLoadPage.argtypes = [FPDF_PAGE, FPDF_FORMHANDLE]
FORM_OnAfterLoadPage.restype = None

# ./fpdf_formfill.h: 1094
FORM_OnBeforeClosePage = _libs['pdfium']['FORM_OnBeforeClosePage']
FORM_OnBeforeClosePage.argtypes = [FPDF_PAGE, FPDF_FORMHANDLE]
FORM_OnBeforeClosePage.restype = None

# ./fpdf_formfill.h: 1110
FORM_DoDocumentJSAction = _libs['pdfium']['FORM_DoDocumentJSAction']
FORM_DoDocumentJSAction.argtypes = [FPDF_FORMHANDLE]
FORM_DoDocumentJSAction.restype = None

# ./fpdf_formfill.h: 1124
FORM_DoDocumentOpenAction = _libs['pdfium']['FORM_DoDocumentOpenAction']
FORM_DoDocumentOpenAction.argtypes = [FPDF_FORMHANDLE]
FORM_DoDocumentOpenAction.restype = None

# ./fpdf_formfill.h: 1151
FORM_DoDocumentAAction = _libs['pdfium']['FORM_DoDocumentAAction']
FORM_DoDocumentAAction.argtypes = [FPDF_FORMHANDLE, c_int]
FORM_DoDocumentAAction.restype = None

# ./fpdf_formfill.h: 1174
FORM_DoPageAAction = _libs['pdfium']['FORM_DoPageAAction']
FORM_DoPageAAction.argtypes = [FPDF_PAGE, FPDF_FORMHANDLE, c_int]
FORM_DoPageAAction.restype = None

# ./fpdf_formfill.h: 1191
FORM_OnMouseMove = _libs['pdfium']['FORM_OnMouseMove']
FORM_OnMouseMove.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnMouseMove.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1220
FORM_OnMouseWheel = _libs['pdfium']['FORM_OnMouseWheel']
FORM_OnMouseWheel.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, POINTER(FS_POINTF), c_int, c_int]
FORM_OnMouseWheel.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1243
FORM_OnFocus = _libs['pdfium']['FORM_OnFocus']
FORM_OnFocus.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnFocus.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1263
FORM_OnLButtonDown = _libs['pdfium']['FORM_OnLButtonDown']
FORM_OnLButtonDown.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnLButtonDown.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1274
FORM_OnRButtonDown = _libs['pdfium']['FORM_OnRButtonDown']
FORM_OnRButtonDown.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnRButtonDown.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1291
FORM_OnLButtonUp = _libs['pdfium']['FORM_OnLButtonUp']
FORM_OnLButtonUp.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnLButtonUp.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1302
FORM_OnRButtonUp = _libs['pdfium']['FORM_OnRButtonUp']
FORM_OnRButtonUp.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnRButtonUp.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1323
FORM_OnLButtonDoubleClick = _libs['pdfium']['FORM_OnLButtonDoubleClick']
FORM_OnLButtonDoubleClick.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_double, c_double]
FORM_OnLButtonDoubleClick.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1341
FORM_OnKeyDown = _libs['pdfium']['FORM_OnKeyDown']
FORM_OnKeyDown.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_int]
FORM_OnKeyDown.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1361
FORM_OnKeyUp = _libs['pdfium']['FORM_OnKeyUp']
FORM_OnKeyUp.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_int]
FORM_OnKeyUp.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1378
FORM_OnChar = _libs['pdfium']['FORM_OnChar']
FORM_OnChar.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, c_int]
FORM_OnChar.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1399
FORM_GetFocusedText = _libs['pdfium']['FORM_GetFocusedText']
FORM_GetFocusedText.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, POINTER(None), c_ulong]
FORM_GetFocusedText.restype = c_ulong

# ./fpdf_formfill.h: 1420
FORM_GetSelectedText = _libs['pdfium']['FORM_GetSelectedText']
FORM_GetSelectedText.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, POINTER(None), c_ulong]
FORM_GetSelectedText.restype = c_ulong

# ./fpdf_formfill.h: 1441
FORM_ReplaceAndKeepSelection = _libs['pdfium']['FORM_ReplaceAndKeepSelection']
FORM_ReplaceAndKeepSelection.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, FPDF_WIDESTRING]
FORM_ReplaceAndKeepSelection.restype = None

# ./fpdf_formfill.h: 1459
FORM_ReplaceSelection = _libs['pdfium']['FORM_ReplaceSelection']
FORM_ReplaceSelection.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, FPDF_WIDESTRING]
FORM_ReplaceSelection.restype = None

# ./fpdf_formfill.h: 1474
FORM_SelectAllText = _libs['pdfium']['FORM_SelectAllText']
FORM_SelectAllText.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE]
FORM_SelectAllText.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1485
FORM_CanUndo = _libs['pdfium']['FORM_CanUndo']
FORM_CanUndo.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE]
FORM_CanUndo.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1497
FORM_CanRedo = _libs['pdfium']['FORM_CanRedo']
FORM_CanRedo.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE]
FORM_CanRedo.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1508
FORM_Undo = _libs['pdfium']['FORM_Undo']
FORM_Undo.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE]
FORM_Undo.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1519
FORM_Redo = _libs['pdfium']['FORM_Redo']
FORM_Redo.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE]
FORM_Redo.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1532
FORM_ForceToKillFocus = _libs['pdfium']['FORM_ForceToKillFocus']
FORM_ForceToKillFocus.argtypes = [FPDF_FORMHANDLE]
FORM_ForceToKillFocus.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1555
FORM_GetFocusedAnnot = _libs['pdfium']['FORM_GetFocusedAnnot']
FORM_GetFocusedAnnot.argtypes = [FPDF_FORMHANDLE, POINTER(c_int), POINTER(FPDF_ANNOTATION)]
FORM_GetFocusedAnnot.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1572
FORM_SetFocusedAnnot = _libs['pdfium']['FORM_SetFocusedAnnot']
FORM_SetFocusedAnnot.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FORM_SetFocusedAnnot.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1626
FPDFPage_HasFormFieldAtPoint = _libs['pdfium']['FPDFPage_HasFormFieldAtPoint']
FPDFPage_HasFormFieldAtPoint.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_double, c_double]
FPDFPage_HasFormFieldAtPoint.restype = c_int

# ./fpdf_formfill.h: 1643
FPDFPage_FormFieldZOrderAtPoint = _libs['pdfium']['FPDFPage_FormFieldZOrderAtPoint']
FPDFPage_FormFieldZOrderAtPoint.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_double, c_double]
FPDFPage_FormFieldZOrderAtPoint.restype = c_int

# ./fpdf_formfill.h: 1669
FPDF_SetFormFieldHighlightColor = _libs['pdfium']['FPDF_SetFormFieldHighlightColor']
FPDF_SetFormFieldHighlightColor.argtypes = [FPDF_FORMHANDLE, c_int, c_ulong]
FPDF_SetFormFieldHighlightColor.restype = None

# ./fpdf_formfill.h: 1686
FPDF_SetFormFieldHighlightAlpha = _libs['pdfium']['FPDF_SetFormFieldHighlightAlpha']
FPDF_SetFormFieldHighlightAlpha.argtypes = [FPDF_FORMHANDLE, c_ubyte]
FPDF_SetFormFieldHighlightAlpha.restype = None

# ./fpdf_formfill.h: 1699
FPDF_RemoveFormFieldHighlight = _libs['pdfium']['FPDF_RemoveFormFieldHighlight']
FPDF_RemoveFormFieldHighlight.argtypes = [FPDF_FORMHANDLE]
FPDF_RemoveFormFieldHighlight.restype = None

# ./fpdf_formfill.h: 1736
FPDF_FFLDraw = _libs['pdfium']['FPDF_FFLDraw']
FPDF_FFLDraw.argtypes = [FPDF_FORMHANDLE, FPDF_BITMAP, FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_int]
FPDF_FFLDraw.restype = None

# ./fpdf_formfill.h: 1767
FPDF_GetFormType = _libs['pdfium']['FPDF_GetFormType']
FPDF_GetFormType.argtypes = [FPDF_DOCUMENT]
FPDF_GetFormType.restype = c_int

# ./fpdf_formfill.h: 1791
FORM_SetIndexSelected = _libs['pdfium']['FORM_SetIndexSelected']
FORM_SetIndexSelected.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int, FPDF_BOOL]
FORM_SetIndexSelected.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1814
FORM_IsIndexSelected = _libs['pdfium']['FORM_IsIndexSelected']
FORM_IsIndexSelected.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, c_int]
FORM_IsIndexSelected.restype = FPDF_BOOL

# ./fpdf_formfill.h: 1824
FPDF_LoadXFA = _libs['pdfium']['FPDF_LoadXFA']
FPDF_LoadXFA.argtypes = [FPDF_DOCUMENT]
FPDF_LoadXFA.restype = FPDF_BOOL

# ./fpdf_annot.h: 98
enum_FPDFANNOT_COLORTYPE = c_int

# ./fpdf_annot.h: 98
FPDFANNOT_COLORTYPE_Color = 0

# ./fpdf_annot.h: 98
FPDFANNOT_COLORTYPE_InteriorColor = (FPDFANNOT_COLORTYPE_Color + 1)

# ./fpdf_annot.h: 98
FPDFANNOT_COLORTYPE = enum_FPDFANNOT_COLORTYPE

# ./fpdf_annot.h: 121
FPDFAnnot_IsSupportedSubtype = _libs['pdfium']['FPDFAnnot_IsSupportedSubtype']
FPDFAnnot_IsSupportedSubtype.argtypes = [FPDF_ANNOTATION_SUBTYPE]
FPDFAnnot_IsSupportedSubtype.restype = FPDF_BOOL

# ./fpdf_annot.h: 134
FPDFPage_CreateAnnot = _libs['pdfium']['FPDFPage_CreateAnnot']
FPDFPage_CreateAnnot.argtypes = [FPDF_PAGE, FPDF_ANNOTATION_SUBTYPE]
FPDFPage_CreateAnnot.restype = FPDF_ANNOTATION

# ./fpdf_annot.h: 142
FPDFPage_GetAnnotCount = _libs['pdfium']['FPDFPage_GetAnnotCount']
FPDFPage_GetAnnotCount.argtypes = [FPDF_PAGE]
FPDFPage_GetAnnotCount.restype = c_int

# ./fpdf_annot.h: 152
FPDFPage_GetAnnot = _libs['pdfium']['FPDFPage_GetAnnot']
FPDFPage_GetAnnot.argtypes = [FPDF_PAGE, c_int]
FPDFPage_GetAnnot.restype = FPDF_ANNOTATION

# ./fpdf_annot.h: 163
FPDFPage_GetAnnotIndex = _libs['pdfium']['FPDFPage_GetAnnotIndex']
FPDFPage_GetAnnotIndex.argtypes = [FPDF_PAGE, FPDF_ANNOTATION]
FPDFPage_GetAnnotIndex.restype = c_int

# ./fpdf_annot.h: 172
FPDFPage_CloseAnnot = _libs['pdfium']['FPDFPage_CloseAnnot']
FPDFPage_CloseAnnot.argtypes = [FPDF_ANNOTATION]
FPDFPage_CloseAnnot.restype = None

# ./fpdf_annot.h: 181
FPDFPage_RemoveAnnot = _libs['pdfium']['FPDFPage_RemoveAnnot']
FPDFPage_RemoveAnnot.argtypes = [FPDF_PAGE, c_int]
FPDFPage_RemoveAnnot.restype = FPDF_BOOL

# ./fpdf_annot.h: 191
FPDFAnnot_GetSubtype = _libs['pdfium']['FPDFAnnot_GetSubtype']
FPDFAnnot_GetSubtype.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetSubtype.restype = FPDF_ANNOTATION_SUBTYPE

# ./fpdf_annot.h: 202
FPDFAnnot_IsObjectSupportedSubtype = _libs['pdfium']['FPDFAnnot_IsObjectSupportedSubtype']
FPDFAnnot_IsObjectSupportedSubtype.argtypes = [FPDF_ANNOTATION_SUBTYPE]
FPDFAnnot_IsObjectSupportedSubtype.restype = FPDF_BOOL

# ./fpdf_annot.h: 216
FPDFAnnot_UpdateObject = _libs['pdfium']['FPDFAnnot_UpdateObject']
FPDFAnnot_UpdateObject.argtypes = [FPDF_ANNOTATION, FPDF_PAGEOBJECT]
FPDFAnnot_UpdateObject.restype = FPDF_BOOL

# ./fpdf_annot.h: 231
FPDFAnnot_AddInkStroke = _libs['pdfium']['FPDFAnnot_AddInkStroke']
FPDFAnnot_AddInkStroke.argtypes = [FPDF_ANNOTATION, POINTER(FS_POINTF), c_size_t]
FPDFAnnot_AddInkStroke.restype = c_int

# ./fpdf_annot.h: 244
FPDFAnnot_RemoveInkList = _libs['pdfium']['FPDFAnnot_RemoveInkList']
FPDFAnnot_RemoveInkList.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_RemoveInkList.restype = FPDF_BOOL

# ./fpdf_annot.h: 258
FPDFAnnot_AppendObject = _libs['pdfium']['FPDFAnnot_AppendObject']
FPDFAnnot_AppendObject.argtypes = [FPDF_ANNOTATION, FPDF_PAGEOBJECT]
FPDFAnnot_AppendObject.restype = FPDF_BOOL

# ./fpdf_annot.h: 267
FPDFAnnot_GetObjectCount = _libs['pdfium']['FPDFAnnot_GetObjectCount']
FPDFAnnot_GetObjectCount.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetObjectCount.restype = c_int

# ./fpdf_annot.h: 277
FPDFAnnot_GetObject = _libs['pdfium']['FPDFAnnot_GetObject']
FPDFAnnot_GetObject.argtypes = [FPDF_ANNOTATION, c_int]
FPDFAnnot_GetObject.restype = FPDF_PAGEOBJECT

# ./fpdf_annot.h: 287
FPDFAnnot_RemoveObject = _libs['pdfium']['FPDFAnnot_RemoveObject']
FPDFAnnot_RemoveObject.argtypes = [FPDF_ANNOTATION, c_int]
FPDFAnnot_RemoveObject.restype = FPDF_BOOL

# ./fpdf_annot.h: 300
FPDFAnnot_SetColor = _libs['pdfium']['FPDFAnnot_SetColor']
FPDFAnnot_SetColor.argtypes = [FPDF_ANNOTATION, FPDFANNOT_COLORTYPE, c_uint, c_uint, c_uint, c_uint]
FPDFAnnot_SetColor.restype = FPDF_BOOL

# ./fpdf_annot.h: 319
FPDFAnnot_GetColor = _libs['pdfium']['FPDFAnnot_GetColor']
FPDFAnnot_GetColor.argtypes = [FPDF_ANNOTATION, FPDFANNOT_COLORTYPE, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFAnnot_GetColor.restype = FPDF_BOOL

# ./fpdf_annot.h: 339
FPDFAnnot_HasAttachmentPoints = _libs['pdfium']['FPDFAnnot_HasAttachmentPoints']
FPDFAnnot_HasAttachmentPoints.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_HasAttachmentPoints.restype = FPDF_BOOL

# ./fpdf_annot.h: 355
FPDFAnnot_SetAttachmentPoints = _libs['pdfium']['FPDFAnnot_SetAttachmentPoints']
FPDFAnnot_SetAttachmentPoints.argtypes = [FPDF_ANNOTATION, c_size_t, POINTER(FS_QUADPOINTSF)]
FPDFAnnot_SetAttachmentPoints.restype = FPDF_BOOL

# ./fpdf_annot.h: 370
FPDFAnnot_AppendAttachmentPoints = _libs['pdfium']['FPDFAnnot_AppendAttachmentPoints']
FPDFAnnot_AppendAttachmentPoints.argtypes = [FPDF_ANNOTATION, POINTER(FS_QUADPOINTSF)]
FPDFAnnot_AppendAttachmentPoints.restype = FPDF_BOOL

# ./fpdf_annot.h: 380
FPDFAnnot_CountAttachmentPoints = _libs['pdfium']['FPDFAnnot_CountAttachmentPoints']
FPDFAnnot_CountAttachmentPoints.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_CountAttachmentPoints.restype = c_size_t

# ./fpdf_annot.h: 391
FPDFAnnot_GetAttachmentPoints = _libs['pdfium']['FPDFAnnot_GetAttachmentPoints']
FPDFAnnot_GetAttachmentPoints.argtypes = [FPDF_ANNOTATION, c_size_t, POINTER(FS_QUADPOINTSF)]
FPDFAnnot_GetAttachmentPoints.restype = FPDF_BOOL

# ./fpdf_annot.h: 405
FPDFAnnot_SetRect = _libs['pdfium']['FPDFAnnot_SetRect']
FPDFAnnot_SetRect.argtypes = [FPDF_ANNOTATION, POINTER(FS_RECTF)]
FPDFAnnot_SetRect.restype = FPDF_BOOL

# ./fpdf_annot.h: 415
FPDFAnnot_GetRect = _libs['pdfium']['FPDFAnnot_GetRect']
FPDFAnnot_GetRect.argtypes = [FPDF_ANNOTATION, POINTER(FS_RECTF)]
FPDFAnnot_GetRect.restype = FPDF_BOOL

# ./fpdf_annot.h: 430
FPDFAnnot_GetVertices = _libs['pdfium']['FPDFAnnot_GetVertices']
FPDFAnnot_GetVertices.argtypes = [FPDF_ANNOTATION, POINTER(FS_POINTF), c_ulong]
FPDFAnnot_GetVertices.restype = c_ulong

# ./fpdf_annot.h: 442
FPDFAnnot_GetInkListCount = _libs['pdfium']['FPDFAnnot_GetInkListCount']
FPDFAnnot_GetInkListCount.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetInkListCount.restype = c_ulong

# ./fpdf_annot.h: 457
FPDFAnnot_GetInkListPath = _libs['pdfium']['FPDFAnnot_GetInkListPath']
FPDFAnnot_GetInkListPath.argtypes = [FPDF_ANNOTATION, c_ulong, POINTER(FS_POINTF), c_ulong]
FPDFAnnot_GetInkListPath.restype = c_ulong

# ./fpdf_annot.h: 471
FPDFAnnot_GetLine = _libs['pdfium']['FPDFAnnot_GetLine']
FPDFAnnot_GetLine.argtypes = [FPDF_ANNOTATION, POINTER(FS_POINTF), POINTER(FS_POINTF)]
FPDFAnnot_GetLine.restype = FPDF_BOOL

# ./fpdf_annot.h: 487
FPDFAnnot_SetBorder = _libs['pdfium']['FPDFAnnot_SetBorder']
FPDFAnnot_SetBorder.argtypes = [FPDF_ANNOTATION, c_float, c_float, c_float]
FPDFAnnot_SetBorder.restype = FPDF_BOOL

# ./fpdf_annot.h: 503
FPDFAnnot_GetBorder = _libs['pdfium']['FPDFAnnot_GetBorder']
FPDFAnnot_GetBorder.argtypes = [FPDF_ANNOTATION, POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFAnnot_GetBorder.restype = FPDF_BOOL

# ./fpdf_annot.h: 527
FPDFAnnot_GetFormAdditionalActionJavaScript = _libs['pdfium']['FPDFAnnot_GetFormAdditionalActionJavaScript']
FPDFAnnot_GetFormAdditionalActionJavaScript.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, c_int, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetFormAdditionalActionJavaScript.restype = c_ulong

# ./fpdf_annot.h: 540
FPDFAnnot_HasKey = _libs['pdfium']['FPDFAnnot_HasKey']
FPDFAnnot_HasKey.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING]
FPDFAnnot_HasKey.restype = FPDF_BOOL

# ./fpdf_annot.h: 551
FPDFAnnot_GetValueType = _libs['pdfium']['FPDFAnnot_GetValueType']
FPDFAnnot_GetValueType.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING]
FPDFAnnot_GetValueType.restype = FPDF_OBJECT_TYPE

# ./fpdf_annot.h: 564
FPDFAnnot_SetStringValue = _libs['pdfium']['FPDFAnnot_SetStringValue']
FPDFAnnot_SetStringValue.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING, FPDF_WIDESTRING]
FPDFAnnot_SetStringValue.restype = FPDF_BOOL

# ./fpdf_annot.h: 584
FPDFAnnot_GetStringValue = _libs['pdfium']['FPDFAnnot_GetStringValue']
FPDFAnnot_GetStringValue.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetStringValue.restype = c_ulong

# ./fpdf_annot.h: 601
FPDFAnnot_GetNumberValue = _libs['pdfium']['FPDFAnnot_GetNumberValue']
FPDFAnnot_GetNumberValue.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING, POINTER(c_float)]
FPDFAnnot_GetNumberValue.restype = FPDF_BOOL

# ./fpdf_annot.h: 618
FPDFAnnot_SetAP = _libs['pdfium']['FPDFAnnot_SetAP']
FPDFAnnot_SetAP.argtypes = [FPDF_ANNOTATION, FPDF_ANNOT_APPEARANCEMODE, FPDF_WIDESTRING]
FPDFAnnot_SetAP.restype = FPDF_BOOL

# ./fpdf_annot.h: 640
FPDFAnnot_GetAP = _libs['pdfium']['FPDFAnnot_GetAP']
FPDFAnnot_GetAP.argtypes = [FPDF_ANNOTATION, FPDF_ANNOT_APPEARANCEMODE, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetAP.restype = c_ulong

# ./fpdf_annot.h: 656
FPDFAnnot_GetLinkedAnnot = _libs['pdfium']['FPDFAnnot_GetLinkedAnnot']
FPDFAnnot_GetLinkedAnnot.argtypes = [FPDF_ANNOTATION, FPDF_BYTESTRING]
FPDFAnnot_GetLinkedAnnot.restype = FPDF_ANNOTATION

# ./fpdf_annot.h: 664
FPDFAnnot_GetFlags = _libs['pdfium']['FPDFAnnot_GetFlags']
FPDFAnnot_GetFlags.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetFlags.restype = c_int

# ./fpdf_annot.h: 673
FPDFAnnot_SetFlags = _libs['pdfium']['FPDFAnnot_SetFlags']
FPDFAnnot_SetFlags.argtypes = [FPDF_ANNOTATION, c_int]
FPDFAnnot_SetFlags.restype = FPDF_BOOL

# ./fpdf_annot.h: 685
FPDFAnnot_GetFormFieldFlags = _libs['pdfium']['FPDFAnnot_GetFormFieldFlags']
FPDFAnnot_GetFormFieldFlags.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_GetFormFieldFlags.restype = c_int

# ./fpdf_annot.h: 702
FPDFAnnot_GetFormFieldAtPoint = _libs['pdfium']['FPDFAnnot_GetFormFieldAtPoint']
FPDFAnnot_GetFormFieldAtPoint.argtypes = [FPDF_FORMHANDLE, FPDF_PAGE, POINTER(FS_POINTF)]
FPDFAnnot_GetFormFieldAtPoint.restype = FPDF_ANNOTATION

# ./fpdf_annot.h: 720
FPDFAnnot_GetFormFieldName = _libs['pdfium']['FPDFAnnot_GetFormFieldName']
FPDFAnnot_GetFormFieldName.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetFormFieldName.restype = c_ulong

# ./fpdf_annot.h: 740
FPDFAnnot_GetFormFieldAlternateName = _libs['pdfium']['FPDFAnnot_GetFormFieldAlternateName']
FPDFAnnot_GetFormFieldAlternateName.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetFormFieldAlternateName.restype = c_ulong

# ./fpdf_annot.h: 756
FPDFAnnot_GetFormFieldType = _libs['pdfium']['FPDFAnnot_GetFormFieldType']
FPDFAnnot_GetFormFieldType.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_GetFormFieldType.restype = c_int

# ./fpdf_annot.h: 772
FPDFAnnot_GetFormFieldValue = _libs['pdfium']['FPDFAnnot_GetFormFieldValue']
FPDFAnnot_GetFormFieldValue.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetFormFieldValue.restype = c_ulong

# ./fpdf_annot.h: 787
FPDFAnnot_GetOptionCount = _libs['pdfium']['FPDFAnnot_GetOptionCount']
FPDFAnnot_GetOptionCount.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_GetOptionCount.restype = c_int

# ./fpdf_annot.h: 809
FPDFAnnot_GetOptionLabel = _libs['pdfium']['FPDFAnnot_GetOptionLabel']
FPDFAnnot_GetOptionLabel.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, c_int, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetOptionLabel.restype = c_ulong

# ./fpdf_annot.h: 827
FPDFAnnot_IsOptionSelected = _libs['pdfium']['FPDFAnnot_IsOptionSelected']
FPDFAnnot_IsOptionSelected.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, c_int]
FPDFAnnot_IsOptionSelected.restype = FPDF_BOOL

# ./fpdf_annot.h: 844
FPDFAnnot_GetFontSize = _libs['pdfium']['FPDFAnnot_GetFontSize']
FPDFAnnot_GetFontSize.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(c_float)]
FPDFAnnot_GetFontSize.restype = FPDF_BOOL

# ./fpdf_annot.h: 859
FPDFAnnot_GetFontColor = _libs['pdfium']['FPDFAnnot_GetFontColor']
FPDFAnnot_GetFontColor.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFAnnot_GetFontColor.restype = FPDF_BOOL

# ./fpdf_annot.h: 874
FPDFAnnot_IsChecked = _libs['pdfium']['FPDFAnnot_IsChecked']
FPDFAnnot_IsChecked.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_IsChecked.restype = FPDF_BOOL

# ./fpdf_annot.h: 889
FPDFAnnot_SetFocusableSubtypes = _libs['pdfium']['FPDFAnnot_SetFocusableSubtypes']
FPDFAnnot_SetFocusableSubtypes.argtypes = [FPDF_FORMHANDLE, POINTER(FPDF_ANNOTATION_SUBTYPE), c_size_t]
FPDFAnnot_SetFocusableSubtypes.restype = FPDF_BOOL

# ./fpdf_annot.h: 902
FPDFAnnot_GetFocusableSubtypesCount = _libs['pdfium']['FPDFAnnot_GetFocusableSubtypesCount']
FPDFAnnot_GetFocusableSubtypesCount.argtypes = [FPDF_FORMHANDLE]
FPDFAnnot_GetFocusableSubtypesCount.restype = c_int

# ./fpdf_annot.h: 918
FPDFAnnot_GetFocusableSubtypes = _libs['pdfium']['FPDFAnnot_GetFocusableSubtypes']
FPDFAnnot_GetFocusableSubtypes.argtypes = [FPDF_FORMHANDLE, POINTER(FPDF_ANNOTATION_SUBTYPE), c_size_t]
FPDFAnnot_GetFocusableSubtypes.restype = FPDF_BOOL

# ./fpdf_annot.h: 929
FPDFAnnot_GetLink = _libs['pdfium']['FPDFAnnot_GetLink']
FPDFAnnot_GetLink.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetLink.restype = FPDF_LINK

# ./fpdf_annot.h: 943
FPDFAnnot_GetFormControlCount = _libs['pdfium']['FPDFAnnot_GetFormControlCount']
FPDFAnnot_GetFormControlCount.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_GetFormControlCount.restype = c_int

# ./fpdf_annot.h: 957
FPDFAnnot_GetFormControlIndex = _libs['pdfium']['FPDFAnnot_GetFormControlIndex']
FPDFAnnot_GetFormControlIndex.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION]
FPDFAnnot_GetFormControlIndex.restype = c_int

# ./fpdf_annot.h: 974
FPDFAnnot_GetFormFieldExportValue = _libs['pdfium']['FPDFAnnot_GetFormFieldExportValue']
FPDFAnnot_GetFormFieldExportValue.argtypes = [FPDF_FORMHANDLE, FPDF_ANNOTATION, POINTER(FPDF_WCHAR), c_ulong]
FPDFAnnot_GetFormFieldExportValue.restype = c_ulong

# ./fpdf_annot.h: 986
FPDFAnnot_SetURI = _libs['pdfium']['FPDFAnnot_SetURI']
FPDFAnnot_SetURI.argtypes = [FPDF_ANNOTATION, POINTER(c_char)]
FPDFAnnot_SetURI.restype = FPDF_BOOL

# ./fpdf_annot.h: 996
FPDFAnnot_GetFileAttachment = _libs['pdfium']['FPDFAnnot_GetFileAttachment']
FPDFAnnot_GetFileAttachment.argtypes = [FPDF_ANNOTATION]
FPDFAnnot_GetFileAttachment.restype = FPDF_ATTACHMENT

# ./fpdf_annot.h: 1006
FPDFAnnot_AddFileAttachment = _libs['pdfium']['FPDFAnnot_AddFileAttachment']
FPDFAnnot_AddFileAttachment.argtypes = [FPDF_ANNOTATION, FPDF_WIDESTRING]
FPDFAnnot_AddFileAttachment.restype = FPDF_ATTACHMENT

# ./fpdf_attachment.h: 22
FPDFDoc_GetAttachmentCount = _libs['pdfium']['FPDFDoc_GetAttachmentCount']
FPDFDoc_GetAttachmentCount.argtypes = [FPDF_DOCUMENT]
FPDFDoc_GetAttachmentCount.restype = c_int

# ./fpdf_attachment.h: 35
FPDFDoc_AddAttachment = _libs['pdfium']['FPDFDoc_AddAttachment']
FPDFDoc_AddAttachment.argtypes = [FPDF_DOCUMENT, FPDF_WIDESTRING]
FPDFDoc_AddAttachment.restype = FPDF_ATTACHMENT

# ./fpdf_attachment.h: 46
FPDFDoc_GetAttachment = _libs['pdfium']['FPDFDoc_GetAttachment']
FPDFDoc_GetAttachment.argtypes = [FPDF_DOCUMENT, c_int]
FPDFDoc_GetAttachment.restype = FPDF_ATTACHMENT

# ./fpdf_attachment.h: 59
FPDFDoc_DeleteAttachment = _libs['pdfium']['FPDFDoc_DeleteAttachment']
FPDFDoc_DeleteAttachment.argtypes = [FPDF_DOCUMENT, c_int]
FPDFDoc_DeleteAttachment.restype = FPDF_BOOL

# ./fpdf_attachment.h: 72
FPDFAttachment_GetName = _libs['pdfium']['FPDFAttachment_GetName']
FPDFAttachment_GetName.argtypes = [FPDF_ATTACHMENT, POINTER(FPDF_WCHAR), c_ulong]
FPDFAttachment_GetName.restype = c_ulong

# ./fpdf_attachment.h: 84
FPDFAttachment_HasKey = _libs['pdfium']['FPDFAttachment_HasKey']
FPDFAttachment_HasKey.argtypes = [FPDF_ATTACHMENT, FPDF_BYTESTRING]
FPDFAttachment_HasKey.restype = FPDF_BOOL

# ./fpdf_attachment.h: 95
FPDFAttachment_GetValueType = _libs['pdfium']['FPDFAttachment_GetValueType']
FPDFAttachment_GetValueType.argtypes = [FPDF_ATTACHMENT, FPDF_BYTESTRING]
FPDFAttachment_GetValueType.restype = FPDF_OBJECT_TYPE

# ./fpdf_attachment.h: 108
FPDFAttachment_SetStringValue = _libs['pdfium']['FPDFAttachment_SetStringValue']
FPDFAttachment_SetStringValue.argtypes = [FPDF_ATTACHMENT, FPDF_BYTESTRING, FPDF_WIDESTRING]
FPDFAttachment_SetStringValue.restype = FPDF_BOOL

# ./fpdf_attachment.h: 129
FPDFAttachment_GetStringValue = _libs['pdfium']['FPDFAttachment_GetStringValue']
FPDFAttachment_GetStringValue.argtypes = [FPDF_ATTACHMENT, FPDF_BYTESTRING, POINTER(FPDF_WCHAR), c_ulong]
FPDFAttachment_GetStringValue.restype = c_ulong

# ./fpdf_attachment.h: 146
FPDFAttachment_SetFile = _libs['pdfium']['FPDFAttachment_SetFile']
FPDFAttachment_SetFile.argtypes = [FPDF_ATTACHMENT, FPDF_DOCUMENT, POINTER(None), c_ulong]
FPDFAttachment_SetFile.restype = FPDF_BOOL

# ./fpdf_attachment.h: 170
FPDFAttachment_GetFile = _libs['pdfium']['FPDFAttachment_GetFile']
FPDFAttachment_GetFile.argtypes = [FPDF_ATTACHMENT, POINTER(None), c_ulong, POINTER(c_ulong)]
FPDFAttachment_GetFile.restype = FPDF_BOOL

# ./fpdf_catalog.h: 26
FPDFCatalog_IsTagged = _libs['pdfium']['FPDFCatalog_IsTagged']
FPDFCatalog_IsTagged.argtypes = [FPDF_DOCUMENT]
FPDFCatalog_IsTagged.restype = FPDF_BOOL

# ./fpdf_catalog.h: 36
FPDFCatalog_SetLanguage = _libs['pdfium']['FPDFCatalog_SetLanguage']
FPDFCatalog_SetLanguage.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING]
FPDFCatalog_SetLanguage.restype = FPDF_BOOL

# ./fpdf_dataavail.h: 33
class struct__FX_FILEAVAIL (Structure):
    __slots__ = ['version', 'IsDataAvail']

struct__FX_FILEAVAIL._fields_ = [
    ('version', c_int),
    ('IsDataAvail', CFUNCTYPE(FPDF_BOOL, POINTER(struct__FX_FILEAVAIL), c_size_t, c_size_t)),
]

# ./fpdf_dataavail.h: 52
FX_FILEAVAIL = struct__FX_FILEAVAIL

# ./fpdf_dataavail.h: 62
FPDFAvail_Create = _libs['pdfium']['FPDFAvail_Create']
FPDFAvail_Create.argtypes = [POINTER(FX_FILEAVAIL), POINTER(FPDF_FILEACCESS)]
FPDFAvail_Create.restype = FPDF_AVAIL

# ./fpdf_dataavail.h: 68
FPDFAvail_Destroy = _libs['pdfium']['FPDFAvail_Destroy']
FPDFAvail_Destroy.argtypes = [FPDF_AVAIL]
FPDFAvail_Destroy.restype = None

# ./fpdf_dataavail.h: 71
class struct__FX_DOWNLOADHINTS (Structure):
    __slots__ = ['version', 'AddSegment']

struct__FX_DOWNLOADHINTS._fields_ = [
    ('version', c_int),
    ('AddSegment', CFUNCTYPE(None, POINTER(struct__FX_DOWNLOADHINTS), c_size_t, c_size_t)),
]

# ./fpdf_dataavail.h: 90
FX_DOWNLOADHINTS = struct__FX_DOWNLOADHINTS

# ./fpdf_dataavail.h: 109
FPDFAvail_IsDocAvail = _libs['pdfium']['FPDFAvail_IsDocAvail']
FPDFAvail_IsDocAvail.argtypes = [FPDF_AVAIL, POINTER(FX_DOWNLOADHINTS)]
FPDFAvail_IsDocAvail.restype = c_int

# ./fpdf_dataavail.h: 124
FPDFAvail_GetDocument = _libs['pdfium']['FPDFAvail_GetDocument']
FPDFAvail_GetDocument.argtypes = [FPDF_AVAIL, FPDF_BYTESTRING]
FPDFAvail_GetDocument.restype = FPDF_DOCUMENT

# ./fpdf_dataavail.h: 135
FPDFAvail_GetFirstPageNum = _libs['pdfium']['FPDFAvail_GetFirstPageNum']
FPDFAvail_GetFirstPageNum.argtypes = [FPDF_DOCUMENT]
FPDFAvail_GetFirstPageNum.restype = c_int

# ./fpdf_dataavail.h: 157
FPDFAvail_IsPageAvail = _libs['pdfium']['FPDFAvail_IsPageAvail']
FPDFAvail_IsPageAvail.argtypes = [FPDF_AVAIL, c_int, POINTER(FX_DOWNLOADHINTS)]
FPDFAvail_IsPageAvail.restype = c_int

# ./fpdf_dataavail.h: 182
FPDFAvail_IsFormAvail = _libs['pdfium']['FPDFAvail_IsFormAvail']
FPDFAvail_IsFormAvail.argtypes = [FPDF_AVAIL, POINTER(FX_DOWNLOADHINTS)]
FPDFAvail_IsFormAvail.restype = c_int

# ./fpdf_dataavail.h: 198
FPDFAvail_IsLinearized = _libs['pdfium']['FPDFAvail_IsLinearized']
FPDFAvail_IsLinearized.argtypes = [FPDF_AVAIL]
FPDFAvail_IsLinearized.restype = c_int

# ./fpdf_doc.h: 46
enum_anon_5 = c_int

# ./fpdf_doc.h: 46
FILEIDTYPE_PERMANENT = 0

# ./fpdf_doc.h: 46
FILEIDTYPE_CHANGING = 1

# ./fpdf_doc.h: 46
FPDF_FILEIDTYPE = enum_anon_5

# ./fpdf_doc.h: 59
FPDFBookmark_GetFirstChild = _libs['pdfium']['FPDFBookmark_GetFirstChild']
FPDFBookmark_GetFirstChild.argtypes = [FPDF_DOCUMENT, FPDF_BOOKMARK]
FPDFBookmark_GetFirstChild.restype = FPDF_BOOKMARK

# ./fpdf_doc.h: 72
FPDFBookmark_GetNextSibling = _libs['pdfium']['FPDFBookmark_GetNextSibling']
FPDFBookmark_GetNextSibling.argtypes = [FPDF_DOCUMENT, FPDF_BOOKMARK]
FPDFBookmark_GetNextSibling.restype = FPDF_BOOKMARK

# ./fpdf_doc.h: 88
FPDFBookmark_GetTitle = _libs['pdfium']['FPDFBookmark_GetTitle']
FPDFBookmark_GetTitle.argtypes = [FPDF_BOOKMARK, POINTER(None), c_ulong]
FPDFBookmark_GetTitle.restype = c_ulong

# ./fpdf_doc.h: 102
FPDFBookmark_GetCount = _libs['pdfium']['FPDFBookmark_GetCount']
FPDFBookmark_GetCount.argtypes = [FPDF_BOOKMARK]
FPDFBookmark_GetCount.restype = c_int

# ./fpdf_doc.h: 114
FPDFBookmark_Find = _libs['pdfium']['FPDFBookmark_Find']
FPDFBookmark_Find.argtypes = [FPDF_DOCUMENT, FPDF_WIDESTRING]
FPDFBookmark_Find.restype = FPDF_BOOKMARK

# ./fpdf_doc.h: 124
FPDFBookmark_GetDest = _libs['pdfium']['FPDFBookmark_GetDest']
FPDFBookmark_GetDest.argtypes = [FPDF_DOCUMENT, FPDF_BOOKMARK]
FPDFBookmark_GetDest.restype = FPDF_DEST

# ./fpdf_doc.h: 137
FPDFBookmark_GetAction = _libs['pdfium']['FPDFBookmark_GetAction']
FPDFBookmark_GetAction.argtypes = [FPDF_BOOKMARK]
FPDFBookmark_GetAction.restype = FPDF_ACTION

# ./fpdf_doc.h: 149
FPDFAction_GetType = _libs['pdfium']['FPDFAction_GetType']
FPDFAction_GetType.argtypes = [FPDF_ACTION]
FPDFAction_GetType.restype = c_ulong

# ./fpdf_doc.h: 163
FPDFAction_GetDest = _libs['pdfium']['FPDFAction_GetDest']
FPDFAction_GetDest.argtypes = [FPDF_DOCUMENT, FPDF_ACTION]
FPDFAction_GetDest.restype = FPDF_DEST

# ./fpdf_doc.h: 181
FPDFAction_GetFilePath = _libs['pdfium']['FPDFAction_GetFilePath']
FPDFAction_GetFilePath.argtypes = [FPDF_ACTION, POINTER(None), c_ulong]
FPDFAction_GetFilePath.restype = c_ulong

# ./fpdf_doc.h: 207
FPDFAction_GetURIPath = _libs['pdfium']['FPDFAction_GetURIPath']
FPDFAction_GetURIPath.argtypes = [FPDF_DOCUMENT, FPDF_ACTION, POINTER(None), c_ulong]
FPDFAction_GetURIPath.restype = c_ulong

# ./fpdf_doc.h: 218
FPDFDest_GetDestPageIndex = _libs['pdfium']['FPDFDest_GetDestPageIndex']
FPDFDest_GetDestPageIndex.argtypes = [FPDF_DOCUMENT, FPDF_DEST]
FPDFDest_GetDestPageIndex.restype = c_int

# ./fpdf_doc.h: 231
FPDFDest_GetView = _libs['pdfium']['FPDFDest_GetView']
FPDFDest_GetView.argtypes = [FPDF_DEST, POINTER(c_ulong), POINTER(FS_FLOAT)]
FPDFDest_GetView.restype = c_ulong

# ./fpdf_doc.h: 248
FPDFDest_GetLocationInPage = _libs['pdfium']['FPDFDest_GetLocationInPage']
FPDFDest_GetLocationInPage.argtypes = [FPDF_DEST, POINTER(FPDF_BOOL), POINTER(FPDF_BOOL), POINTER(FPDF_BOOL), POINTER(FS_FLOAT), POINTER(FS_FLOAT), POINTER(FS_FLOAT)]
FPDFDest_GetLocationInPage.restype = FPDF_BOOL

# ./fpdf_doc.h: 266
FPDFLink_GetLinkAtPoint = _libs['pdfium']['FPDFLink_GetLinkAtPoint']
FPDFLink_GetLinkAtPoint.argtypes = [FPDF_PAGE, c_double, c_double]
FPDFLink_GetLinkAtPoint.restype = FPDF_LINK

# ./fpdf_doc.h: 281
FPDFLink_GetLinkZOrderAtPoint = _libs['pdfium']['FPDFLink_GetLinkZOrderAtPoint']
FPDFLink_GetLinkZOrderAtPoint.argtypes = [FPDF_PAGE, c_double, c_double]
FPDFLink_GetLinkZOrderAtPoint.restype = c_int

# ./fpdf_doc.h: 293
FPDFLink_GetDest = _libs['pdfium']['FPDFLink_GetDest']
FPDFLink_GetDest.argtypes = [FPDF_DOCUMENT, FPDF_LINK]
FPDFLink_GetDest.restype = FPDF_DEST

# ./fpdf_doc.h: 303
FPDFLink_GetAction = _libs['pdfium']['FPDFLink_GetAction']
FPDFLink_GetAction.argtypes = [FPDF_LINK]
FPDFLink_GetAction.restype = FPDF_ACTION

# ./fpdf_doc.h: 313
FPDFLink_Enumerate = _libs['pdfium']['FPDFLink_Enumerate']
FPDFLink_Enumerate.argtypes = [FPDF_PAGE, POINTER(c_int), POINTER(FPDF_LINK)]
FPDFLink_Enumerate.restype = FPDF_BOOL

# ./fpdf_doc.h: 326
FPDFLink_GetAnnot = _libs['pdfium']['FPDFLink_GetAnnot']
FPDFLink_GetAnnot.argtypes = [FPDF_PAGE, FPDF_LINK]
FPDFLink_GetAnnot.restype = FPDF_ANNOTATION

# ./fpdf_doc.h: 334
FPDFLink_GetAnnotRect = _libs['pdfium']['FPDFLink_GetAnnotRect']
FPDFLink_GetAnnotRect.argtypes = [FPDF_LINK, POINTER(FS_RECTF)]
FPDFLink_GetAnnotRect.restype = FPDF_BOOL

# ./fpdf_doc.h: 342
FPDFLink_CountQuadPoints = _libs['pdfium']['FPDFLink_CountQuadPoints']
FPDFLink_CountQuadPoints.argtypes = [FPDF_LINK]
FPDFLink_CountQuadPoints.restype = c_int

# ./fpdf_doc.h: 352
FPDFLink_GetQuadPoints = _libs['pdfium']['FPDFLink_GetQuadPoints']
FPDFLink_GetQuadPoints.argtypes = [FPDF_LINK, c_int, POINTER(FS_QUADPOINTSF)]
FPDFLink_GetQuadPoints.restype = FPDF_BOOL

# ./fpdf_doc.h: 367
FPDF_GetPageAAction = _libs['pdfium']['FPDF_GetPageAAction']
FPDF_GetPageAAction.argtypes = [FPDF_PAGE, c_int]
FPDF_GetPageAAction.restype = FPDF_ACTION

# ./fpdf_doc.h: 385
FPDF_GetFileIdentifier = _libs['pdfium']['FPDF_GetFileIdentifier']
FPDF_GetFileIdentifier.argtypes = [FPDF_DOCUMENT, FPDF_FILEIDTYPE, POINTER(None), c_ulong]
FPDF_GetFileIdentifier.restype = c_ulong

# ./fpdf_doc.h: 411
FPDF_GetMetaText = _libs['pdfium']['FPDF_GetMetaText']
FPDF_GetMetaText.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING, POINTER(None), c_ulong]
FPDF_GetMetaText.restype = c_ulong

# ./fpdf_doc.h: 429
FPDF_GetPageLabel = _libs['pdfium']['FPDF_GetPageLabel']
FPDF_GetPageLabel.argtypes = [FPDF_DOCUMENT, c_int, POINTER(None), c_ulong]
FPDF_GetPageLabel.restype = c_ulong

# /usr/include/x86_64-linux-gnu/bits/types.h: 38
__uint8_t = c_ubyte

# /usr/include/x86_64-linux-gnu/bits/types.h: 40
__uint16_t = c_ushort

# /usr/include/x86_64-linux-gnu/bits/types.h: 42
__uint32_t = c_uint

# /usr/include/x86_64-linux-gnu/bits/types.h: 160
__time_t = c_long

# /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h: 24
uint8_t = __uint8_t

# /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h: 25
uint16_t = __uint16_t

# /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h: 26
uint32_t = __uint32_t

# ./fpdf_edit.h: 93
class struct_FPDF_IMAGEOBJ_METADATA (Structure):
    __slots__ = ['width', 'height', 'horizontal_dpi', 'vertical_dpi', 'bits_per_pixel', 'colorspace', 'marked_content_id']

struct_FPDF_IMAGEOBJ_METADATA._fields_ = [
    ('width', c_uint),
    ('height', c_uint),
    ('horizontal_dpi', c_float),
    ('vertical_dpi', c_float),
    ('bits_per_pixel', c_uint),
    ('colorspace', c_int),
    ('marked_content_id', c_int),
]

# ./fpdf_edit.h: 93
FPDF_IMAGEOBJ_METADATA = struct_FPDF_IMAGEOBJ_METADATA

# ./fpdf_edit.h: 102
FPDF_CreateNewDocument = _libs['pdfium']['FPDF_CreateNewDocument']
FPDF_CreateNewDocument.argtypes = []
FPDF_CreateNewDocument.restype = FPDF_DOCUMENT

# ./fpdf_edit.h: 117
FPDFPage_New = _libs['pdfium']['FPDFPage_New']
FPDFPage_New.argtypes = [FPDF_DOCUMENT, c_int, c_double, c_double]
FPDFPage_New.restype = FPDF_PAGE

# ./fpdf_edit.h: 126
FPDFPage_Delete = _libs['pdfium']['FPDFPage_Delete']
FPDFPage_Delete.argtypes = [FPDF_DOCUMENT, c_int]
FPDFPage_Delete.restype = None

# ./fpdf_edit.h: 156
FPDF_MovePages = _libs['pdfium']['FPDF_MovePages']
FPDF_MovePages.argtypes = [FPDF_DOCUMENT, POINTER(c_int), c_ulong, c_int]
FPDF_MovePages.restype = FPDF_BOOL

# ./fpdf_edit.h: 170
FPDFPage_GetRotation = _libs['pdfium']['FPDFPage_GetRotation']
FPDFPage_GetRotation.argtypes = [FPDF_PAGE]
FPDFPage_GetRotation.restype = c_int

# ./fpdf_edit.h: 180
FPDFPage_SetRotation = _libs['pdfium']['FPDFPage_SetRotation']
FPDFPage_SetRotation.argtypes = [FPDF_PAGE, c_int]
FPDFPage_SetRotation.restype = None

# ./fpdf_edit.h: 188
FPDFPage_InsertObject = _libs['pdfium']['FPDFPage_InsertObject']
FPDFPage_InsertObject.argtypes = [FPDF_PAGE, FPDF_PAGEOBJECT]
FPDFPage_InsertObject.restype = None

# ./fpdf_edit.h: 203
FPDFPage_RemoveObject = _libs['pdfium']['FPDFPage_RemoveObject']
FPDFPage_RemoveObject.argtypes = [FPDF_PAGE, FPDF_PAGEOBJECT]
FPDFPage_RemoveObject.restype = FPDF_BOOL

# ./fpdf_edit.h: 210
FPDFPage_CountObjects = _libs['pdfium']['FPDFPage_CountObjects']
FPDFPage_CountObjects.argtypes = [FPDF_PAGE]
FPDFPage_CountObjects.restype = c_int

# ./fpdf_edit.h: 218
FPDFPage_GetObject = _libs['pdfium']['FPDFPage_GetObject']
FPDFPage_GetObject.argtypes = [FPDF_PAGE, c_int]
FPDFPage_GetObject.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 226
FPDFPage_HasTransparency = _libs['pdfium']['FPDFPage_HasTransparency']
FPDFPage_HasTransparency.argtypes = [FPDF_PAGE]
FPDFPage_HasTransparency.restype = FPDF_BOOL

# ./fpdf_edit.h: 236
FPDFPage_GenerateContent = _libs['pdfium']['FPDFPage_GenerateContent']
FPDFPage_GenerateContent.argtypes = [FPDF_PAGE]
FPDFPage_GenerateContent.restype = FPDF_BOOL

# ./fpdf_edit.h: 245
FPDFPageObj_Destroy = _libs['pdfium']['FPDFPageObj_Destroy']
FPDFPageObj_Destroy.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_Destroy.restype = None

# ./fpdf_edit.h: 253
FPDFPageObj_HasTransparency = _libs['pdfium']['FPDFPageObj_HasTransparency']
FPDFPageObj_HasTransparency.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_HasTransparency.restype = FPDF_BOOL

# ./fpdf_edit.h: 261
FPDFPageObj_GetType = _libs['pdfium']['FPDFPageObj_GetType']
FPDFPageObj_GetType.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetType.restype = c_int

# ./fpdf_edit.h: 277
FPDFPageObj_GetIsActive = _libs['pdfium']['FPDFPageObj_GetIsActive']
FPDFPageObj_GetIsActive.argtypes = [FPDF_PAGEOBJECT, POINTER(FPDF_BOOL)]
FPDFPageObj_GetIsActive.restype = FPDF_BOOL

# ./fpdf_edit.h: 293
FPDFPageObj_SetIsActive = _libs['pdfium']['FPDFPageObj_SetIsActive']
FPDFPageObj_SetIsActive.argtypes = [FPDF_PAGEOBJECT, FPDF_BOOL]
FPDFPageObj_SetIsActive.restype = FPDF_BOOL

# ./fpdf_edit.h: 310
FPDFPageObj_Transform = _libs['pdfium']['FPDFPageObj_Transform']
FPDFPageObj_Transform.argtypes = [FPDF_PAGEOBJECT, c_double, c_double, c_double, c_double, c_double, c_double]
FPDFPageObj_Transform.restype = None

# ./fpdf_edit.h: 331
FPDFPageObj_TransformF = _libs['pdfium']['FPDFPageObj_TransformF']
FPDFPageObj_TransformF.argtypes = [FPDF_PAGEOBJECT, POINTER(FS_MATRIX)]
FPDFPageObj_TransformF.restype = FPDF_BOOL

# ./fpdf_edit.h: 351
FPDFPageObj_GetMatrix = _libs['pdfium']['FPDFPageObj_GetMatrix']
FPDFPageObj_GetMatrix.argtypes = [FPDF_PAGEOBJECT, POINTER(FS_MATRIX)]
FPDFPageObj_GetMatrix.restype = FPDF_BOOL

# ./fpdf_edit.h: 366
FPDFPageObj_SetMatrix = _libs['pdfium']['FPDFPageObj_SetMatrix']
FPDFPageObj_SetMatrix.argtypes = [FPDF_PAGEOBJECT, POINTER(FS_MATRIX)]
FPDFPageObj_SetMatrix.restype = FPDF_BOOL

# ./fpdf_edit.h: 382
FPDFPage_TransformAnnots = _libs['pdfium']['FPDFPage_TransformAnnots']
FPDFPage_TransformAnnots.argtypes = [FPDF_PAGE, c_double, c_double, c_double, c_double, c_double, c_double]
FPDFPage_TransformAnnots.restype = None

# ./fpdf_edit.h: 396
FPDFPageObj_NewImageObj = _libs['pdfium']['FPDFPageObj_NewImageObj']
FPDFPageObj_NewImageObj.argtypes = [FPDF_DOCUMENT]
FPDFPageObj_NewImageObj.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 405
FPDFPageObj_GetMarkedContentID = _libs['pdfium']['FPDFPageObj_GetMarkedContentID']
FPDFPageObj_GetMarkedContentID.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetMarkedContentID.restype = c_int

# ./fpdf_edit.h: 415
FPDFPageObj_CountMarks = _libs['pdfium']['FPDFPageObj_CountMarks']
FPDFPageObj_CountMarks.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_CountMarks.restype = c_int

# ./fpdf_edit.h: 428
FPDFPageObj_GetMark = _libs['pdfium']['FPDFPageObj_GetMark']
FPDFPageObj_GetMark.argtypes = [FPDF_PAGEOBJECT, c_ulong]
FPDFPageObj_GetMark.restype = FPDF_PAGEOBJECTMARK

# ./fpdf_edit.h: 441
FPDFPageObj_AddMark = _libs['pdfium']['FPDFPageObj_AddMark']
FPDFPageObj_AddMark.argtypes = [FPDF_PAGEOBJECT, FPDF_BYTESTRING]
FPDFPageObj_AddMark.restype = FPDF_PAGEOBJECTMARK

# ./fpdf_edit.h: 452
FPDFPageObj_RemoveMark = _libs['pdfium']['FPDFPageObj_RemoveMark']
FPDFPageObj_RemoveMark.argtypes = [FPDF_PAGEOBJECT, FPDF_PAGEOBJECTMARK]
FPDFPageObj_RemoveMark.restype = FPDF_BOOL

# ./fpdf_edit.h: 469
FPDFPageObjMark_GetName = _libs['pdfium']['FPDFPageObjMark_GetName']
FPDFPageObjMark_GetName.argtypes = [FPDF_PAGEOBJECTMARK, POINTER(FPDF_WCHAR), c_ulong, POINTER(c_ulong)]
FPDFPageObjMark_GetName.restype = FPDF_BOOL

# ./fpdf_edit.h: 482
FPDFPageObjMark_CountParams = _libs['pdfium']['FPDFPageObjMark_CountParams']
FPDFPageObjMark_CountParams.argtypes = [FPDF_PAGEOBJECTMARK]
FPDFPageObjMark_CountParams.restype = c_int

# ./fpdf_edit.h: 500
FPDFPageObjMark_GetParamKey = _libs['pdfium']['FPDFPageObjMark_GetParamKey']
FPDFPageObjMark_GetParamKey.argtypes = [FPDF_PAGEOBJECTMARK, c_ulong, POINTER(FPDF_WCHAR), c_ulong, POINTER(c_ulong)]
FPDFPageObjMark_GetParamKey.restype = FPDF_BOOL

# ./fpdf_edit.h: 514
FPDFPageObjMark_GetParamValueType = _libs['pdfium']['FPDFPageObjMark_GetParamValueType']
FPDFPageObjMark_GetParamValueType.argtypes = [FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING]
FPDFPageObjMark_GetParamValueType.restype = FPDF_OBJECT_TYPE

# ./fpdf_edit.h: 529
FPDFPageObjMark_GetParamIntValue = _libs['pdfium']['FPDFPageObjMark_GetParamIntValue']
FPDFPageObjMark_GetParamIntValue.argtypes = [FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, POINTER(c_int)]
FPDFPageObjMark_GetParamIntValue.restype = FPDF_BOOL

# ./fpdf_edit.h: 549
FPDFPageObjMark_GetParamStringValue = _libs['pdfium']['FPDFPageObjMark_GetParamStringValue']
FPDFPageObjMark_GetParamStringValue.argtypes = [FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, POINTER(FPDF_WCHAR), c_ulong, POINTER(c_ulong)]
FPDFPageObjMark_GetParamStringValue.restype = FPDF_BOOL

# ./fpdf_edit.h: 571
FPDFPageObjMark_GetParamBlobValue = _libs['pdfium']['FPDFPageObjMark_GetParamBlobValue']
FPDFPageObjMark_GetParamBlobValue.argtypes = [FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, POINTER(c_ubyte), c_ulong, POINTER(c_ulong)]
FPDFPageObjMark_GetParamBlobValue.restype = FPDF_BOOL

# ./fpdf_edit.h: 590
FPDFPageObjMark_SetIntParam = _libs['pdfium']['FPDFPageObjMark_SetIntParam']
FPDFPageObjMark_SetIntParam.argtypes = [FPDF_DOCUMENT, FPDF_PAGEOBJECT, FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, c_int]
FPDFPageObjMark_SetIntParam.restype = FPDF_BOOL

# ./fpdf_edit.h: 609
FPDFPageObjMark_SetStringParam = _libs['pdfium']['FPDFPageObjMark_SetStringParam']
FPDFPageObjMark_SetStringParam.argtypes = [FPDF_DOCUMENT, FPDF_PAGEOBJECT, FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, FPDF_BYTESTRING]
FPDFPageObjMark_SetStringParam.restype = FPDF_BOOL

# ./fpdf_edit.h: 629
FPDFPageObjMark_SetBlobParam = _libs['pdfium']['FPDFPageObjMark_SetBlobParam']
FPDFPageObjMark_SetBlobParam.argtypes = [FPDF_DOCUMENT, FPDF_PAGEOBJECT, FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING, POINTER(c_ubyte), c_ulong]
FPDFPageObjMark_SetBlobParam.restype = FPDF_BOOL

# ./fpdf_edit.h: 645
FPDFPageObjMark_RemoveParam = _libs['pdfium']['FPDFPageObjMark_RemoveParam']
FPDFPageObjMark_RemoveParam.argtypes = [FPDF_PAGEOBJECT, FPDF_PAGEOBJECTMARK, FPDF_BYTESTRING]
FPDFPageObjMark_RemoveParam.restype = FPDF_BOOL

# ./fpdf_edit.h: 664
FPDFImageObj_LoadJpegFile = _libs['pdfium']['FPDFImageObj_LoadJpegFile']
FPDFImageObj_LoadJpegFile.argtypes = [POINTER(FPDF_PAGE), c_int, FPDF_PAGEOBJECT, POINTER(FPDF_FILEACCESS)]
FPDFImageObj_LoadJpegFile.restype = FPDF_BOOL

# ./fpdf_edit.h: 686
FPDFImageObj_LoadJpegFileInline = _libs['pdfium']['FPDFImageObj_LoadJpegFileInline']
FPDFImageObj_LoadJpegFileInline.argtypes = [POINTER(FPDF_PAGE), c_int, FPDF_PAGEOBJECT, POINTER(FPDF_FILEACCESS)]
FPDFImageObj_LoadJpegFileInline.restype = FPDF_BOOL

# ./fpdf_edit.h: 710
FPDFImageObj_SetMatrix = _libs['pdfium']['FPDFImageObj_SetMatrix']
FPDFImageObj_SetMatrix.argtypes = [FPDF_PAGEOBJECT, c_double, c_double, c_double, c_double, c_double, c_double]
FPDFImageObj_SetMatrix.restype = FPDF_BOOL

# ./fpdf_edit.h: 727
FPDFImageObj_SetBitmap = _libs['pdfium']['FPDFImageObj_SetBitmap']
FPDFImageObj_SetBitmap.argtypes = [POINTER(FPDF_PAGE), c_int, FPDF_PAGEOBJECT, FPDF_BITMAP]
FPDFImageObj_SetBitmap.restype = FPDF_BOOL

# ./fpdf_edit.h: 742
FPDFImageObj_GetBitmap = _libs['pdfium']['FPDFImageObj_GetBitmap']
FPDFImageObj_GetBitmap.argtypes = [FPDF_PAGEOBJECT]
FPDFImageObj_GetBitmap.restype = FPDF_BITMAP

# ./fpdf_edit.h: 758
FPDFImageObj_GetRenderedBitmap = _libs['pdfium']['FPDFImageObj_GetRenderedBitmap']
FPDFImageObj_GetRenderedBitmap.argtypes = [FPDF_DOCUMENT, FPDF_PAGE, FPDF_PAGEOBJECT]
FPDFImageObj_GetRenderedBitmap.restype = FPDF_BITMAP

# ./fpdf_edit.h: 773
FPDFImageObj_GetImageDataDecoded = _libs['pdfium']['FPDFImageObj_GetImageDataDecoded']
FPDFImageObj_GetImageDataDecoded.argtypes = [FPDF_PAGEOBJECT, POINTER(None), c_ulong]
FPDFImageObj_GetImageDataDecoded.restype = c_ulong

# ./fpdf_edit.h: 787
FPDFImageObj_GetImageDataRaw = _libs['pdfium']['FPDFImageObj_GetImageDataRaw']
FPDFImageObj_GetImageDataRaw.argtypes = [FPDF_PAGEOBJECT, POINTER(None), c_ulong]
FPDFImageObj_GetImageDataRaw.restype = c_ulong

# ./fpdf_edit.h: 797
FPDFImageObj_GetImageFilterCount = _libs['pdfium']['FPDFImageObj_GetImageFilterCount']
FPDFImageObj_GetImageFilterCount.argtypes = [FPDF_PAGEOBJECT]
FPDFImageObj_GetImageFilterCount.restype = c_int

# ./fpdf_edit.h: 811
FPDFImageObj_GetImageFilter = _libs['pdfium']['FPDFImageObj_GetImageFilter']
FPDFImageObj_GetImageFilter.argtypes = [FPDF_PAGEOBJECT, c_int, POINTER(None), c_ulong]
FPDFImageObj_GetImageFilter.restype = c_ulong

# ./fpdf_edit.h: 828
FPDFImageObj_GetImageMetadata = _libs['pdfium']['FPDFImageObj_GetImageMetadata']
FPDFImageObj_GetImageMetadata.argtypes = [FPDF_PAGEOBJECT, FPDF_PAGE, POINTER(FPDF_IMAGEOBJ_METADATA)]
FPDFImageObj_GetImageMetadata.restype = FPDF_BOOL

# ./fpdf_edit.h: 841
FPDFImageObj_GetImagePixelSize = _libs['pdfium']['FPDFImageObj_GetImagePixelSize']
FPDFImageObj_GetImagePixelSize.argtypes = [FPDF_PAGEOBJECT, POINTER(c_uint), POINTER(c_uint)]
FPDFImageObj_GetImagePixelSize.restype = FPDF_BOOL

# ./fpdf_edit.h: 864
FPDFImageObj_GetIccProfileDataDecoded = _libs['pdfium']['FPDFImageObj_GetIccProfileDataDecoded']
FPDFImageObj_GetIccProfileDataDecoded.argtypes = [FPDF_PAGEOBJECT, FPDF_PAGE, POINTER(uint8_t), c_size_t, POINTER(c_size_t)]
FPDFImageObj_GetIccProfileDataDecoded.restype = FPDF_BOOL

# ./fpdf_edit.h: 876
FPDFPageObj_CreateNewPath = _libs['pdfium']['FPDFPageObj_CreateNewPath']
FPDFPageObj_CreateNewPath.argtypes = [c_float, c_float]
FPDFPageObj_CreateNewPath.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 887
FPDFPageObj_CreateNewRect = _libs['pdfium']['FPDFPageObj_CreateNewRect']
FPDFPageObj_CreateNewRect.argtypes = [c_float, c_float, c_float, c_float]
FPDFPageObj_CreateNewRect.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 902
FPDFPageObj_GetBounds = _libs['pdfium']['FPDFPageObj_GetBounds']
FPDFPageObj_GetBounds.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPageObj_GetBounds.restype = FPDF_BOOL

# ./fpdf_edit.h: 924
FPDFPageObj_GetRotatedBounds = _libs['pdfium']['FPDFPageObj_GetRotatedBounds']
FPDFPageObj_GetRotatedBounds.argtypes = [FPDF_PAGEOBJECT, POINTER(FS_QUADPOINTSF)]
FPDFPageObj_GetRotatedBounds.restype = FPDF_BOOL

# ./fpdf_edit.h: 936
FPDFPageObj_SetBlendMode = _libs['pdfium']['FPDFPageObj_SetBlendMode']
FPDFPageObj_SetBlendMode.argtypes = [FPDF_PAGEOBJECT, FPDF_BYTESTRING]
FPDFPageObj_SetBlendMode.restype = None

# ./fpdf_edit.h: 949
FPDFPageObj_SetStrokeColor = _libs['pdfium']['FPDFPageObj_SetStrokeColor']
FPDFPageObj_SetStrokeColor.argtypes = [FPDF_PAGEOBJECT, c_uint, c_uint, c_uint, c_uint]
FPDFPageObj_SetStrokeColor.restype = FPDF_BOOL

# ./fpdf_edit.h: 965
FPDFPageObj_GetStrokeColor = _libs['pdfium']['FPDFPageObj_GetStrokeColor']
FPDFPageObj_GetStrokeColor.argtypes = [FPDF_PAGEOBJECT, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFPageObj_GetStrokeColor.restype = FPDF_BOOL

# ./fpdf_edit.h: 978
FPDFPageObj_SetStrokeWidth = _libs['pdfium']['FPDFPageObj_SetStrokeWidth']
FPDFPageObj_SetStrokeWidth.argtypes = [FPDF_PAGEOBJECT, c_float]
FPDFPageObj_SetStrokeWidth.restype = FPDF_BOOL

# ./fpdf_edit.h: 987
FPDFPageObj_GetStrokeWidth = _libs['pdfium']['FPDFPageObj_GetStrokeWidth']
FPDFPageObj_GetStrokeWidth.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float)]
FPDFPageObj_GetStrokeWidth.restype = FPDF_BOOL

# ./fpdf_edit.h: 997
FPDFPageObj_GetLineJoin = _libs['pdfium']['FPDFPageObj_GetLineJoin']
FPDFPageObj_GetLineJoin.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetLineJoin.restype = c_int

# ./fpdf_edit.h: 1007
FPDFPageObj_SetLineJoin = _libs['pdfium']['FPDFPageObj_SetLineJoin']
FPDFPageObj_SetLineJoin.argtypes = [FPDF_PAGEOBJECT, c_int]
FPDFPageObj_SetLineJoin.restype = FPDF_BOOL

# ./fpdf_edit.h: 1017
FPDFPageObj_GetLineCap = _libs['pdfium']['FPDFPageObj_GetLineCap']
FPDFPageObj_GetLineCap.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetLineCap.restype = c_int

# ./fpdf_edit.h: 1027
FPDFPageObj_SetLineCap = _libs['pdfium']['FPDFPageObj_SetLineCap']
FPDFPageObj_SetLineCap.argtypes = [FPDF_PAGEOBJECT, c_int]
FPDFPageObj_SetLineCap.restype = FPDF_BOOL

# ./fpdf_edit.h: 1039
FPDFPageObj_SetFillColor = _libs['pdfium']['FPDFPageObj_SetFillColor']
FPDFPageObj_SetFillColor.argtypes = [FPDF_PAGEOBJECT, c_uint, c_uint, c_uint, c_uint]
FPDFPageObj_SetFillColor.restype = FPDF_BOOL

# ./fpdf_edit.h: 1055
FPDFPageObj_GetFillColor = _libs['pdfium']['FPDFPageObj_GetFillColor']
FPDFPageObj_GetFillColor.argtypes = [FPDF_PAGEOBJECT, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFPageObj_GetFillColor.restype = FPDF_BOOL

# ./fpdf_edit.h: 1069
FPDFPageObj_GetDashPhase = _libs['pdfium']['FPDFPageObj_GetDashPhase']
FPDFPageObj_GetDashPhase.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float)]
FPDFPageObj_GetDashPhase.restype = FPDF_BOOL

# ./fpdf_edit.h: 1079
FPDFPageObj_SetDashPhase = _libs['pdfium']['FPDFPageObj_SetDashPhase']
FPDFPageObj_SetDashPhase.argtypes = [FPDF_PAGEOBJECT, c_float]
FPDFPageObj_SetDashPhase.restype = FPDF_BOOL

# ./fpdf_edit.h: 1088
FPDFPageObj_GetDashCount = _libs['pdfium']['FPDFPageObj_GetDashCount']
FPDFPageObj_GetDashCount.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetDashCount.restype = c_int

# ./fpdf_edit.h: 1099
FPDFPageObj_GetDashArray = _libs['pdfium']['FPDFPageObj_GetDashArray']
FPDFPageObj_GetDashArray.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float), c_size_t]
FPDFPageObj_GetDashArray.restype = FPDF_BOOL

# ./fpdf_edit.h: 1113
FPDFPageObj_SetDashArray = _libs['pdfium']['FPDFPageObj_SetDashArray']
FPDFPageObj_SetDashArray.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float), c_size_t, c_float]
FPDFPageObj_SetDashArray.restype = FPDF_BOOL

# ./fpdf_edit.h: 1126
FPDFPath_CountSegments = _libs['pdfium']['FPDFPath_CountSegments']
FPDFPath_CountSegments.argtypes = [FPDF_PAGEOBJECT]
FPDFPath_CountSegments.restype = c_int

# ./fpdf_edit.h: 1135
FPDFPath_GetPathSegment = _libs['pdfium']['FPDFPath_GetPathSegment']
FPDFPath_GetPathSegment.argtypes = [FPDF_PAGEOBJECT, c_int]
FPDFPath_GetPathSegment.restype = FPDF_PATHSEGMENT

# ./fpdf_edit.h: 1145
FPDFPathSegment_GetPoint = _libs['pdfium']['FPDFPathSegment_GetPoint']
FPDFPathSegment_GetPoint.argtypes = [FPDF_PATHSEGMENT, POINTER(c_float), POINTER(c_float)]
FPDFPathSegment_GetPoint.restype = FPDF_BOOL

# ./fpdf_edit.h: 1153
FPDFPathSegment_GetType = _libs['pdfium']['FPDFPathSegment_GetType']
FPDFPathSegment_GetType.argtypes = [FPDF_PATHSEGMENT]
FPDFPathSegment_GetType.restype = c_int

# ./fpdf_edit.h: 1161
FPDFPathSegment_GetClose = _libs['pdfium']['FPDFPathSegment_GetClose']
FPDFPathSegment_GetClose.argtypes = [FPDF_PATHSEGMENT]
FPDFPathSegment_GetClose.restype = FPDF_BOOL

# ./fpdf_edit.h: 1173
FPDFPath_MoveTo = _libs['pdfium']['FPDFPath_MoveTo']
FPDFPath_MoveTo.argtypes = [FPDF_PAGEOBJECT, c_float, c_float]
FPDFPath_MoveTo.restype = FPDF_BOOL

# ./fpdf_edit.h: 1186
FPDFPath_LineTo = _libs['pdfium']['FPDFPath_LineTo']
FPDFPath_LineTo.argtypes = [FPDF_PAGEOBJECT, c_float, c_float]
FPDFPath_LineTo.restype = FPDF_BOOL

# ./fpdf_edit.h: 1201
FPDFPath_BezierTo = _libs['pdfium']['FPDFPath_BezierTo']
FPDFPath_BezierTo.argtypes = [FPDF_PAGEOBJECT, c_float, c_float, c_float, c_float, c_float, c_float]
FPDFPath_BezierTo.restype = FPDF_BOOL

# ./fpdf_edit.h: 1217
FPDFPath_Close = _libs['pdfium']['FPDFPath_Close']
FPDFPath_Close.argtypes = [FPDF_PAGEOBJECT]
FPDFPath_Close.restype = FPDF_BOOL

# ./fpdf_edit.h: 1226
FPDFPath_SetDrawMode = _libs['pdfium']['FPDFPath_SetDrawMode']
FPDFPath_SetDrawMode.argtypes = [FPDF_PAGEOBJECT, c_int, FPDF_BOOL]
FPDFPath_SetDrawMode.restype = FPDF_BOOL

# ./fpdf_edit.h: 1237
FPDFPath_GetDrawMode = _libs['pdfium']['FPDFPath_GetDrawMode']
FPDFPath_GetDrawMode.argtypes = [FPDF_PAGEOBJECT, POINTER(c_int), POINTER(FPDF_BOOL)]
FPDFPath_GetDrawMode.restype = FPDF_BOOL

# ./fpdf_edit.h: 1249
FPDFPageObj_NewTextObj = _libs['pdfium']['FPDFPageObj_NewTextObj']
FPDFPageObj_NewTextObj.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING, c_float]
FPDFPageObj_NewTextObj.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 1260
FPDFText_SetText = _libs['pdfium']['FPDFText_SetText']
FPDFText_SetText.argtypes = [FPDF_PAGEOBJECT, FPDF_WIDESTRING]
FPDFText_SetText.restype = FPDF_BOOL

# ./fpdf_edit.h: 1272
FPDFText_SetCharcodes = _libs['pdfium']['FPDFText_SetCharcodes']
FPDFText_SetCharcodes.argtypes = [FPDF_PAGEOBJECT, POINTER(uint32_t), c_size_t]
FPDFText_SetCharcodes.restype = FPDF_BOOL

# ./fpdf_edit.h: 1289
FPDFText_LoadFont = _libs['pdfium']['FPDFText_LoadFont']
FPDFText_LoadFont.argtypes = [FPDF_DOCUMENT, POINTER(uint8_t), uint32_t, c_int, FPDF_BOOL]
FPDFText_LoadFont.restype = FPDF_FONT

# ./fpdf_edit.h: 1307
FPDFText_LoadStandardFont = _libs['pdfium']['FPDFText_LoadStandardFont']
FPDFText_LoadStandardFont.argtypes = [FPDF_DOCUMENT, FPDF_BYTESTRING]
FPDFText_LoadStandardFont.restype = FPDF_FONT

# ./fpdf_edit.h: 1326
FPDFText_LoadCidType2Font = _libs['pdfium']['FPDFText_LoadCidType2Font']
FPDFText_LoadCidType2Font.argtypes = [FPDF_DOCUMENT, POINTER(uint8_t), uint32_t, FPDF_BYTESTRING, POINTER(uint8_t), uint32_t]
FPDFText_LoadCidType2Font.restype = FPDF_FONT

# ./fpdf_edit.h: 1341
FPDFTextObj_GetFontSize = _libs['pdfium']['FPDFTextObj_GetFontSize']
FPDFTextObj_GetFontSize.argtypes = [FPDF_PAGEOBJECT, POINTER(c_float)]
FPDFTextObj_GetFontSize.restype = FPDF_BOOL

# ./fpdf_edit.h: 1346
FPDFFont_Close = _libs['pdfium']['FPDFFont_Close']
FPDFFont_Close.argtypes = [FPDF_FONT]
FPDFFont_Close.restype = None

# ./fpdf_edit.h: 1356
FPDFPageObj_CreateTextObj = _libs['pdfium']['FPDFPageObj_CreateTextObj']
FPDFPageObj_CreateTextObj.argtypes = [FPDF_DOCUMENT, FPDF_FONT, c_float]
FPDFPageObj_CreateTextObj.restype = FPDF_PAGEOBJECT

# ./fpdf_edit.h: 1367
FPDFTextObj_GetTextRenderMode = _libs['pdfium']['FPDFTextObj_GetTextRenderMode']
FPDFTextObj_GetTextRenderMode.argtypes = [FPDF_PAGEOBJECT]
FPDFTextObj_GetTextRenderMode.restype = FPDF_TEXT_RENDERMODE

# ./fpdf_edit.h: 1378
FPDFTextObj_SetTextRenderMode = _libs['pdfium']['FPDFTextObj_SetTextRenderMode']
FPDFTextObj_SetTextRenderMode.argtypes = [FPDF_PAGEOBJECT, FPDF_TEXT_RENDERMODE]
FPDFTextObj_SetTextRenderMode.restype = FPDF_BOOL

# ./fpdf_edit.h: 1395
FPDFTextObj_GetText = _libs['pdfium']['FPDFTextObj_GetText']
FPDFTextObj_GetText.argtypes = [FPDF_PAGEOBJECT, FPDF_TEXTPAGE, POINTER(FPDF_WCHAR), c_ulong]
FPDFTextObj_GetText.restype = c_ulong

# ./fpdf_edit.h: 1414
FPDFTextObj_GetRenderedBitmap = _libs['pdfium']['FPDFTextObj_GetRenderedBitmap']
FPDFTextObj_GetRenderedBitmap.argtypes = [FPDF_DOCUMENT, FPDF_PAGE, FPDF_PAGEOBJECT, c_float]
FPDFTextObj_GetRenderedBitmap.restype = FPDF_BITMAP

# ./fpdf_edit.h: 1425
FPDFTextObj_GetFont = _libs['pdfium']['FPDFTextObj_GetFont']
FPDFTextObj_GetFont.argtypes = [FPDF_PAGEOBJECT]
FPDFTextObj_GetFont.restype = FPDF_FONT

# ./fpdf_edit.h: 1441
FPDFFont_GetBaseFontName = _libs['pdfium']['FPDFFont_GetBaseFontName']
FPDFFont_GetBaseFontName.argtypes = [FPDF_FONT, POINTER(c_char), c_size_t]
FPDFFont_GetBaseFontName.restype = c_size_t

# ./fpdf_edit.h: 1458
FPDFFont_GetFamilyName = _libs['pdfium']['FPDFFont_GetFamilyName']
FPDFFont_GetFamilyName.argtypes = [FPDF_FONT, POINTER(c_char), c_size_t]
FPDFFont_GetFamilyName.restype = c_size_t

# ./fpdf_edit.h: 1481
FPDFFont_GetFontData = _libs['pdfium']['FPDFFont_GetFontData']
FPDFFont_GetFontData.argtypes = [FPDF_FONT, POINTER(uint8_t), c_size_t, POINTER(c_size_t)]
FPDFFont_GetFontData.restype = FPDF_BOOL

# ./fpdf_edit.h: 1492
FPDFFont_GetIsEmbedded = _libs['pdfium']['FPDFFont_GetIsEmbedded']
FPDFFont_GetIsEmbedded.argtypes = [FPDF_FONT]
FPDFFont_GetIsEmbedded.restype = c_int

# ./fpdf_edit.h: 1501
FPDFFont_GetFlags = _libs['pdfium']['FPDFFont_GetFlags']
FPDFFont_GetFlags.argtypes = [FPDF_FONT]
FPDFFont_GetFlags.restype = c_int

# ./fpdf_edit.h: 1510
FPDFFont_GetWeight = _libs['pdfium']['FPDFFont_GetWeight']
FPDFFont_GetWeight.argtypes = [FPDF_FONT]
FPDFFont_GetWeight.restype = c_int

# ./fpdf_edit.h: 1522
FPDFFont_GetItalicAngle = _libs['pdfium']['FPDFFont_GetItalicAngle']
FPDFFont_GetItalicAngle.argtypes = [FPDF_FONT, POINTER(c_int)]
FPDFFont_GetItalicAngle.restype = FPDF_BOOL

# ./fpdf_edit.h: 1536
FPDFFont_GetAscent = _libs['pdfium']['FPDFFont_GetAscent']
FPDFFont_GetAscent.argtypes = [FPDF_FONT, c_float, POINTER(c_float)]
FPDFFont_GetAscent.restype = FPDF_BOOL

# ./fpdf_edit.h: 1551
FPDFFont_GetDescent = _libs['pdfium']['FPDFFont_GetDescent']
FPDFFont_GetDescent.argtypes = [FPDF_FONT, c_float, POINTER(c_float)]
FPDFFont_GetDescent.restype = FPDF_BOOL

# ./fpdf_edit.h: 1567
FPDFFont_GetGlyphWidth = _libs['pdfium']['FPDFFont_GetGlyphWidth']
FPDFFont_GetGlyphWidth.argtypes = [FPDF_FONT, uint32_t, c_float, POINTER(c_float)]
FPDFFont_GetGlyphWidth.restype = FPDF_BOOL

# ./fpdf_edit.h: 1580
FPDFFont_GetGlyphPath = _libs['pdfium']['FPDFFont_GetGlyphPath']
FPDFFont_GetGlyphPath.argtypes = [FPDF_FONT, uint32_t, c_float]
FPDFFont_GetGlyphPath.restype = FPDF_GLYPHPATH

# ./fpdf_edit.h: 1591
FPDFGlyphPath_CountGlyphSegments = _libs['pdfium']['FPDFGlyphPath_CountGlyphSegments']
FPDFGlyphPath_CountGlyphSegments.argtypes = [FPDF_GLYPHPATH]
FPDFGlyphPath_CountGlyphSegments.restype = c_int

# ./fpdf_edit.h: 1601
FPDFGlyphPath_GetGlyphPathSegment = _libs['pdfium']['FPDFGlyphPath_GetGlyphPathSegment']
FPDFGlyphPath_GetGlyphPathSegment.argtypes = [FPDF_GLYPHPATH, c_int]
FPDFGlyphPath_GetGlyphPathSegment.restype = FPDF_PATHSEGMENT

# ./fpdf_edit.h: 1609
FPDFFormObj_CountObjects = _libs['pdfium']['FPDFFormObj_CountObjects']
FPDFFormObj_CountObjects.argtypes = [FPDF_PAGEOBJECT]
FPDFFormObj_CountObjects.restype = c_int

# ./fpdf_edit.h: 1618
FPDFFormObj_GetObject = _libs['pdfium']['FPDFFormObj_GetObject']
FPDFFormObj_GetObject.argtypes = [FPDF_PAGEOBJECT, c_ulong]
FPDFFormObj_GetObject.restype = FPDF_PAGEOBJECT

# /usr/include/x86_64-linux-gnu/bits/types/time_t.h: 10
time_t = __time_t

# /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h: 7
class struct_tm (Structure):
    __slots__ = ['tm_sec', 'tm_min', 'tm_hour', 'tm_mday', 'tm_mon', 'tm_year', 'tm_wday', 'tm_yday', 'tm_isdst', 'tm_gmtoff', 'tm_zone']

struct_tm._fields_ = [
    ('tm_sec', c_int),
    ('tm_min', c_int),
    ('tm_hour', c_int),
    ('tm_mday', c_int),
    ('tm_mon', c_int),
    ('tm_year', c_int),
    ('tm_wday', c_int),
    ('tm_yday', c_int),
    ('tm_isdst', c_int),
    ('tm_gmtoff', c_long),
    ('tm_zone', POINTER(c_char)),
]

# ./fpdf_ext.h: 51
class struct__UNSUPPORT_INFO (Structure):
    __slots__ = ['version', 'FSDK_UnSupport_Handler']

struct__UNSUPPORT_INFO._fields_ = [
    ('version', c_int),
    ('FSDK_UnSupport_Handler', CFUNCTYPE(None, POINTER(struct__UNSUPPORT_INFO), c_int)),
]

# ./fpdf_ext.h: 62
UNSUPPORT_INFO = struct__UNSUPPORT_INFO

# ./fpdf_ext.h: 70
FSDK_SetUnSpObjProcessHandler = _libs['pdfium']['FSDK_SetUnSpObjProcessHandler']
FSDK_SetUnSpObjProcessHandler.argtypes = [POINTER(UNSUPPORT_INFO)]
FSDK_SetUnSpObjProcessHandler.restype = FPDF_BOOL

# ./fpdf_ext.h: 79
FSDK_SetTimeFunction = _libs['pdfium']['FSDK_SetTimeFunction']
FSDK_SetTimeFunction.argtypes = [CFUNCTYPE(time_t, )]
FSDK_SetTimeFunction.restype = None

# ./fpdf_ext.h: 89
FSDK_SetLocaltimeFunction = _libs['pdfium']['FSDK_SetLocaltimeFunction']
FSDK_SetLocaltimeFunction.argtypes = [CFUNCTYPE(POINTER(struct_tm), POINTER(time_t))]
FSDK_SetLocaltimeFunction.restype = None

# ./fpdf_ext.h: 113
FPDFDoc_GetPageMode = _libs['pdfium']['FPDFDoc_GetPageMode']
FPDFDoc_GetPageMode.argtypes = [FPDF_DOCUMENT]
FPDFDoc_GetPageMode.restype = c_int

# ./fpdf_flatten.h: 38
FPDFPage_Flatten = _libs['pdfium']['FPDFPage_Flatten']
FPDFPage_Flatten.argtypes = [FPDF_PAGE, c_int]
FPDFPage_Flatten.restype = c_int

# ./fpdf_fwlevent.h: 28
enum_anon_7 = c_int

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_ShiftKey = (1 << 0)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_ControlKey = (1 << 1)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_AltKey = (1 << 2)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_MetaKey = (1 << 3)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_KeyPad = (1 << 4)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_AutoRepeat = (1 << 5)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_LeftButtonDown = (1 << 6)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_MiddleButtonDown = (1 << 7)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG_RightButtonDown = (1 << 8)

# ./fpdf_fwlevent.h: 28
FWL_EVENTFLAG = enum_anon_7

# ./fpdf_fwlevent.h: 201
enum_anon_8 = c_int

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Back = 0x08

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Tab = 0x09

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NewLine = 0x0A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Clear = 0x0C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Return = 0x0D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Shift = 0x10

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Control = 0x11

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Menu = 0x12

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Pause = 0x13

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Capital = 0x14

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Kana = 0x15

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Hangul = 0x15

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Junja = 0x17

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Final = 0x18

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Hanja = 0x19

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Kanji = 0x19

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Escape = 0x1B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Convert = 0x1C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NonConvert = 0x1D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Accept = 0x1E

# ./fpdf_fwlevent.h: 201
FWL_VKEY_ModeChange = 0x1F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Space = 0x20

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Prior = 0x21

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Next = 0x22

# ./fpdf_fwlevent.h: 201
FWL_VKEY_End = 0x23

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Home = 0x24

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Left = 0x25

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Up = 0x26

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Right = 0x27

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Down = 0x28

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Select = 0x29

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Print = 0x2A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Execute = 0x2B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Snapshot = 0x2C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Insert = 0x2D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Delete = 0x2E

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Help = 0x2F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_0 = 0x30

# ./fpdf_fwlevent.h: 201
FWL_VKEY_1 = 0x31

# ./fpdf_fwlevent.h: 201
FWL_VKEY_2 = 0x32

# ./fpdf_fwlevent.h: 201
FWL_VKEY_3 = 0x33

# ./fpdf_fwlevent.h: 201
FWL_VKEY_4 = 0x34

# ./fpdf_fwlevent.h: 201
FWL_VKEY_5 = 0x35

# ./fpdf_fwlevent.h: 201
FWL_VKEY_6 = 0x36

# ./fpdf_fwlevent.h: 201
FWL_VKEY_7 = 0x37

# ./fpdf_fwlevent.h: 201
FWL_VKEY_8 = 0x38

# ./fpdf_fwlevent.h: 201
FWL_VKEY_9 = 0x39

# ./fpdf_fwlevent.h: 201
FWL_VKEY_A = 0x41

# ./fpdf_fwlevent.h: 201
FWL_VKEY_B = 0x42

# ./fpdf_fwlevent.h: 201
FWL_VKEY_C = 0x43

# ./fpdf_fwlevent.h: 201
FWL_VKEY_D = 0x44

# ./fpdf_fwlevent.h: 201
FWL_VKEY_E = 0x45

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F = 0x46

# ./fpdf_fwlevent.h: 201
FWL_VKEY_G = 0x47

# ./fpdf_fwlevent.h: 201
FWL_VKEY_H = 0x48

# ./fpdf_fwlevent.h: 201
FWL_VKEY_I = 0x49

# ./fpdf_fwlevent.h: 201
FWL_VKEY_J = 0x4A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_K = 0x4B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_L = 0x4C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_M = 0x4D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_N = 0x4E

# ./fpdf_fwlevent.h: 201
FWL_VKEY_O = 0x4F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_P = 0x50

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Q = 0x51

# ./fpdf_fwlevent.h: 201
FWL_VKEY_R = 0x52

# ./fpdf_fwlevent.h: 201
FWL_VKEY_S = 0x53

# ./fpdf_fwlevent.h: 201
FWL_VKEY_T = 0x54

# ./fpdf_fwlevent.h: 201
FWL_VKEY_U = 0x55

# ./fpdf_fwlevent.h: 201
FWL_VKEY_V = 0x56

# ./fpdf_fwlevent.h: 201
FWL_VKEY_W = 0x57

# ./fpdf_fwlevent.h: 201
FWL_VKEY_X = 0x58

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Y = 0x59

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Z = 0x5A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_LWin = 0x5B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Command = 0x5B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_RWin = 0x5C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Apps = 0x5D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Sleep = 0x5F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad0 = 0x60

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad1 = 0x61

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad2 = 0x62

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad3 = 0x63

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad4 = 0x64

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad5 = 0x65

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad6 = 0x66

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad7 = 0x67

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad8 = 0x68

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NumPad9 = 0x69

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Multiply = 0x6A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Add = 0x6B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Separator = 0x6C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Subtract = 0x6D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Decimal = 0x6E

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Divide = 0x6F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F1 = 0x70

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F2 = 0x71

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F3 = 0x72

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F4 = 0x73

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F5 = 0x74

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F6 = 0x75

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F7 = 0x76

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F8 = 0x77

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F9 = 0x78

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F10 = 0x79

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F11 = 0x7A

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F12 = 0x7B

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F13 = 0x7C

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F14 = 0x7D

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F15 = 0x7E

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F16 = 0x7F

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F17 = 0x80

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F18 = 0x81

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F19 = 0x82

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F20 = 0x83

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F21 = 0x84

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F22 = 0x85

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F23 = 0x86

# ./fpdf_fwlevent.h: 201
FWL_VKEY_F24 = 0x87

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NunLock = 0x90

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Scroll = 0x91

# ./fpdf_fwlevent.h: 201
FWL_VKEY_LShift = 0xA0

# ./fpdf_fwlevent.h: 201
FWL_VKEY_RShift = 0xA1

# ./fpdf_fwlevent.h: 201
FWL_VKEY_LControl = 0xA2

# ./fpdf_fwlevent.h: 201
FWL_VKEY_RControl = 0xA3

# ./fpdf_fwlevent.h: 201
FWL_VKEY_LMenu = 0xA4

# ./fpdf_fwlevent.h: 201
FWL_VKEY_RMenu = 0xA5

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Back = 0xA6

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Forward = 0xA7

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Refresh = 0xA8

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Stop = 0xA9

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Search = 0xAA

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Favorites = 0xAB

# ./fpdf_fwlevent.h: 201
FWL_VKEY_BROWSER_Home = 0xAC

# ./fpdf_fwlevent.h: 201
FWL_VKEY_VOLUME_Mute = 0xAD

# ./fpdf_fwlevent.h: 201
FWL_VKEY_VOLUME_Down = 0xAE

# ./fpdf_fwlevent.h: 201
FWL_VKEY_VOLUME_Up = 0xAF

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_NEXT_Track = 0xB0

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_PREV_Track = 0xB1

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_Stop = 0xB2

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_PLAY_Pause = 0xB3

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_LAUNCH_Mail = 0xB4

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_LAUNCH_MEDIA_Select = 0xB5

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_LAUNCH_APP1 = 0xB6

# ./fpdf_fwlevent.h: 201
FWL_VKEY_MEDIA_LAUNCH_APP2 = 0xB7

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_1 = 0xBA

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_Plus = 0xBB

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_Comma = 0xBC

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_Minus = 0xBD

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_Period = 0xBE

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_2 = 0xBF

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_3 = 0xC0

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_4 = 0xDB

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_5 = 0xDC

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_6 = 0xDD

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_7 = 0xDE

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_8 = 0xDF

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_102 = 0xE2

# ./fpdf_fwlevent.h: 201
FWL_VKEY_ProcessKey = 0xE5

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Packet = 0xE7

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Attn = 0xF6

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Crsel = 0xF7

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Exsel = 0xF8

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Ereof = 0xF9

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Play = 0xFA

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Zoom = 0xFB

# ./fpdf_fwlevent.h: 201
FWL_VKEY_NoName = 0xFC

# ./fpdf_fwlevent.h: 201
FWL_VKEY_PA1 = 0xFD

# ./fpdf_fwlevent.h: 201
FWL_VKEY_OEM_Clear = 0xFE

# ./fpdf_fwlevent.h: 201
FWL_VKEY_Unknown = 0

# ./fpdf_fwlevent.h: 201
FWL_VKEYCODE = enum_anon_8

# ./fpdf_javascript.h: 22
FPDFDoc_GetJavaScriptActionCount = _libs['pdfium']['FPDFDoc_GetJavaScriptActionCount']
FPDFDoc_GetJavaScriptActionCount.argtypes = [FPDF_DOCUMENT]
FPDFDoc_GetJavaScriptActionCount.restype = c_int

# ./fpdf_javascript.h: 34
FPDFDoc_GetJavaScriptAction = _libs['pdfium']['FPDFDoc_GetJavaScriptAction']
FPDFDoc_GetJavaScriptAction.argtypes = [FPDF_DOCUMENT, c_int]
FPDFDoc_GetJavaScriptAction.restype = FPDF_JAVASCRIPT_ACTION

# ./fpdf_javascript.h: 41
FPDFDoc_CloseJavaScriptAction = _libs['pdfium']['FPDFDoc_CloseJavaScriptAction']
FPDFDoc_CloseJavaScriptAction.argtypes = [FPDF_JAVASCRIPT_ACTION]
FPDFDoc_CloseJavaScriptAction.restype = None

# ./fpdf_javascript.h: 54
FPDFJavaScriptAction_GetName = _libs['pdfium']['FPDFJavaScriptAction_GetName']
FPDFJavaScriptAction_GetName.argtypes = [FPDF_JAVASCRIPT_ACTION, POINTER(FPDF_WCHAR), c_ulong]
FPDFJavaScriptAction_GetName.restype = c_ulong

# ./fpdf_javascript.h: 69
FPDFJavaScriptAction_GetScript = _libs['pdfium']['FPDFJavaScriptAction_GetScript']
FPDFJavaScriptAction_GetScript.argtypes = [FPDF_JAVASCRIPT_ACTION, POINTER(FPDF_WCHAR), c_ulong]
FPDFJavaScriptAction_GetScript.restype = c_ulong

# ./fpdf_ppo.h: 32
FPDF_ImportPagesByIndex = _libs['pdfium']['FPDF_ImportPagesByIndex']
FPDF_ImportPagesByIndex.argtypes = [FPDF_DOCUMENT, FPDF_DOCUMENT, POINTER(c_int), c_ulong, c_int]
FPDF_ImportPagesByIndex.restype = FPDF_BOOL

# ./fpdf_ppo.h: 49
FPDF_ImportPages = _libs['pdfium']['FPDF_ImportPages']
FPDF_ImportPages.argtypes = [FPDF_DOCUMENT, FPDF_DOCUMENT, FPDF_BYTESTRING, c_int]
FPDF_ImportPages.restype = FPDF_BOOL

# ./fpdf_ppo.h: 72
FPDF_ImportNPagesToOne = _libs['pdfium']['FPDF_ImportNPagesToOne']
FPDF_ImportNPagesToOne.argtypes = [FPDF_DOCUMENT, c_float, c_float, c_size_t, c_size_t]
FPDF_ImportNPagesToOne.restype = FPDF_DOCUMENT

# ./fpdf_ppo.h: 85
FPDF_NewXObjectFromPage = _libs['pdfium']['FPDF_NewXObjectFromPage']
FPDF_NewXObjectFromPage.argtypes = [FPDF_DOCUMENT, FPDF_DOCUMENT, c_int]
FPDF_NewXObjectFromPage.restype = FPDF_XOBJECT

# ./fpdf_ppo.h: 92
FPDF_CloseXObject = _libs['pdfium']['FPDF_CloseXObject']
FPDF_CloseXObject.argtypes = [FPDF_XOBJECT]
FPDF_CloseXObject.restype = None

# ./fpdf_ppo.h: 100
FPDF_NewFormObjectFromXObject = _libs['pdfium']['FPDF_NewFormObjectFromXObject']
FPDF_NewFormObjectFromXObject.argtypes = [FPDF_XOBJECT]
FPDF_NewFormObjectFromXObject.restype = FPDF_PAGEOBJECT

# ./fpdf_ppo.h: 109
FPDF_CopyViewerPreferences = _libs['pdfium']['FPDF_CopyViewerPreferences']
FPDF_CopyViewerPreferences.argtypes = [FPDF_DOCUMENT, FPDF_DOCUMENT]
FPDF_CopyViewerPreferences.restype = FPDF_BOOL

# ./fpdf_progressive.h: 25
class struct__IFSDK_PAUSE (Structure):
    __slots__ = ['version', 'NeedToPauseNow', 'user']

struct__IFSDK_PAUSE._fields_ = [
    ('version', c_int),
    ('NeedToPauseNow', CFUNCTYPE(FPDF_BOOL, POINTER(struct__IFSDK_PAUSE))),
    ('user', POINTER(None)),
]

# ./fpdf_progressive.h: 43
IFSDK_PAUSE = struct__IFSDK_PAUSE

# ./fpdf_progressive.h: 79
FPDF_RenderPageBitmapWithColorScheme_Start = _libs['pdfium']['FPDF_RenderPageBitmapWithColorScheme_Start']
FPDF_RenderPageBitmapWithColorScheme_Start.argtypes = [FPDF_BITMAP, FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_int, POINTER(FPDF_COLORSCHEME), POINTER(IFSDK_PAUSE)]
FPDF_RenderPageBitmapWithColorScheme_Start.restype = c_int

# ./fpdf_progressive.h: 117
FPDF_RenderPageBitmap_Start = _libs['pdfium']['FPDF_RenderPageBitmap_Start']
FPDF_RenderPageBitmap_Start.argtypes = [FPDF_BITMAP, FPDF_PAGE, c_int, c_int, c_int, c_int, c_int, c_int, POINTER(IFSDK_PAUSE)]
FPDF_RenderPageBitmap_Start.restype = c_int

# ./fpdf_progressive.h: 138
FPDF_RenderPage_Continue = _libs['pdfium']['FPDF_RenderPage_Continue']
FPDF_RenderPage_Continue.argtypes = [FPDF_PAGE, POINTER(IFSDK_PAUSE)]
FPDF_RenderPage_Continue.restype = c_int

# ./fpdf_progressive.h: 149
FPDF_RenderPage_Close = _libs['pdfium']['FPDF_RenderPage_Close']
FPDF_RenderPage_Close.argtypes = [FPDF_PAGE]
FPDF_RenderPage_Close.restype = None

# ./fpdf_save.h: 19
class struct_FPDF_FILEWRITE_ (Structure):
    __slots__ = ['version', 'WriteBlock']

struct_FPDF_FILEWRITE_._fields_ = [
    ('version', c_int),
    ('WriteBlock', CFUNCTYPE(c_int, POINTER(struct_FPDF_FILEWRITE_), POINTER(None), c_ulong)),
]

# ./fpdf_save.h: 42
FPDF_FILEWRITE = struct_FPDF_FILEWRITE_

# ./fpdf_save.h: 59
FPDF_SaveAsCopy = _libs['pdfium']['FPDF_SaveAsCopy']
FPDF_SaveAsCopy.argtypes = [FPDF_DOCUMENT, POINTER(FPDF_FILEWRITE), FPDF_DWORD]
FPDF_SaveAsCopy.restype = FPDF_BOOL

# ./fpdf_save.h: 76
FPDF_SaveWithVersion = _libs['pdfium']['FPDF_SaveWithVersion']
FPDF_SaveWithVersion.argtypes = [FPDF_DOCUMENT, POINTER(FPDF_FILEWRITE), FPDF_DWORD, c_int]
FPDF_SaveWithVersion.restype = FPDF_BOOL

# ./fpdf_searchex.h: 24
FPDFText_GetCharIndexFromTextIndex = _libs['pdfium']['FPDFText_GetCharIndexFromTextIndex']
FPDFText_GetCharIndexFromTextIndex.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetCharIndexFromTextIndex.restype = c_int

# ./fpdf_searchex.h: 33
FPDFText_GetTextIndexFromCharIndex = _libs['pdfium']['FPDFText_GetTextIndexFromCharIndex']
FPDFText_GetTextIndexFromCharIndex.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetTextIndexFromCharIndex.restype = c_int

# ./fpdf_signature.h: 22
FPDF_GetSignatureCount = _libs['pdfium']['FPDF_GetSignatureCount']
FPDF_GetSignatureCount.argtypes = [FPDF_DOCUMENT]
FPDF_GetSignatureCount.restype = c_int

# ./fpdf_signature.h: 35
FPDF_GetSignatureObject = _libs['pdfium']['FPDF_GetSignatureObject']
FPDF_GetSignatureObject.argtypes = [FPDF_DOCUMENT, c_int]
FPDF_GetSignatureObject.restype = FPDF_SIGNATURE

# ./fpdf_signature.h: 52
FPDFSignatureObj_GetContents = _libs['pdfium']['FPDFSignatureObj_GetContents']
FPDFSignatureObj_GetContents.argtypes = [FPDF_SIGNATURE, POINTER(None), c_ulong]
FPDFSignatureObj_GetContents.restype = c_ulong

# ./fpdf_signature.h: 74
FPDFSignatureObj_GetByteRange = _libs['pdfium']['FPDFSignatureObj_GetByteRange']
FPDFSignatureObj_GetByteRange.argtypes = [FPDF_SIGNATURE, POINTER(c_int), c_ulong]
FPDFSignatureObj_GetByteRange.restype = c_ulong

# ./fpdf_signature.h: 93
FPDFSignatureObj_GetSubFilter = _libs['pdfium']['FPDFSignatureObj_GetSubFilter']
FPDFSignatureObj_GetSubFilter.argtypes = [FPDF_SIGNATURE, POINTER(c_char), c_ulong]
FPDFSignatureObj_GetSubFilter.restype = c_ulong

# ./fpdf_signature.h: 112
FPDFSignatureObj_GetReason = _libs['pdfium']['FPDFSignatureObj_GetReason']
FPDFSignatureObj_GetReason.argtypes = [FPDF_SIGNATURE, POINTER(None), c_ulong]
FPDFSignatureObj_GetReason.restype = c_ulong

# ./fpdf_signature.h: 136
FPDFSignatureObj_GetTime = _libs['pdfium']['FPDFSignatureObj_GetTime']
FPDFSignatureObj_GetTime.argtypes = [FPDF_SIGNATURE, POINTER(c_char), c_ulong]
FPDFSignatureObj_GetTime.restype = c_ulong

# ./fpdf_signature.h: 149
FPDFSignatureObj_GetDocMDPPermission = _libs['pdfium']['FPDFSignatureObj_GetDocMDPPermission']
FPDFSignatureObj_GetDocMDPPermission.argtypes = [FPDF_SIGNATURE]
FPDFSignatureObj_GetDocMDPPermission.restype = c_uint

# ./fpdf_structtree.h: 27
FPDF_StructTree_GetForPage = _libs['pdfium']['FPDF_StructTree_GetForPage']
FPDF_StructTree_GetForPage.argtypes = [FPDF_PAGE]
FPDF_StructTree_GetForPage.restype = FPDF_STRUCTTREE

# ./fpdf_structtree.h: 37
FPDF_StructTree_Close = _libs['pdfium']['FPDF_StructTree_Close']
FPDF_StructTree_Close.argtypes = [FPDF_STRUCTTREE]
FPDF_StructTree_Close.restype = None

# ./fpdf_structtree.h: 47
FPDF_StructTree_CountChildren = _libs['pdfium']['FPDF_StructTree_CountChildren']
FPDF_StructTree_CountChildren.argtypes = [FPDF_STRUCTTREE]
FPDF_StructTree_CountChildren.restype = c_int

# ./fpdf_structtree.h: 63
FPDF_StructTree_GetChildAtIndex = _libs['pdfium']['FPDF_StructTree_GetChildAtIndex']
FPDF_StructTree_GetChildAtIndex.argtypes = [FPDF_STRUCTTREE, c_int]
FPDF_StructTree_GetChildAtIndex.restype = FPDF_STRUCTELEMENT

# ./fpdf_structtree.h: 81
FPDF_StructElement_GetAltText = _libs['pdfium']['FPDF_StructElement_GetAltText']
FPDF_StructElement_GetAltText.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetAltText.restype = c_ulong

# ./fpdf_structtree.h: 102
FPDF_StructElement_GetActualText = _libs['pdfium']['FPDF_StructElement_GetActualText']
FPDF_StructElement_GetActualText.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetActualText.restype = c_ulong

# ./fpdf_structtree.h: 122
FPDF_StructElement_GetID = _libs['pdfium']['FPDF_StructElement_GetID']
FPDF_StructElement_GetID.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetID.restype = c_ulong

# ./fpdf_structtree.h: 143
FPDF_StructElement_GetLang = _libs['pdfium']['FPDF_StructElement_GetLang']
FPDF_StructElement_GetLang.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetLang.restype = c_ulong

# ./fpdf_structtree.h: 165
FPDF_StructElement_GetStringAttribute = _libs['pdfium']['FPDF_StructElement_GetStringAttribute']
FPDF_StructElement_GetStringAttribute.argtypes = [FPDF_STRUCTELEMENT, FPDF_BYTESTRING, POINTER(None), c_ulong]
FPDF_StructElement_GetStringAttribute.restype = c_ulong

# ./fpdf_structtree.h: 182
FPDF_StructElement_GetMarkedContentID = _libs['pdfium']['FPDF_StructElement_GetMarkedContentID']
FPDF_StructElement_GetMarkedContentID.argtypes = [FPDF_STRUCTELEMENT]
FPDF_StructElement_GetMarkedContentID.restype = c_int

# ./fpdf_structtree.h: 200
FPDF_StructElement_GetType = _libs['pdfium']['FPDF_StructElement_GetType']
FPDF_StructElement_GetType.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetType.restype = c_ulong

# ./fpdf_structtree.h: 221
FPDF_StructElement_GetObjType = _libs['pdfium']['FPDF_StructElement_GetObjType']
FPDF_StructElement_GetObjType.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetObjType.restype = c_ulong

# ./fpdf_structtree.h: 241
FPDF_StructElement_GetTitle = _libs['pdfium']['FPDF_StructElement_GetTitle']
FPDF_StructElement_GetTitle.argtypes = [FPDF_STRUCTELEMENT, POINTER(None), c_ulong]
FPDF_StructElement_GetTitle.restype = c_ulong

# ./fpdf_structtree.h: 252
FPDF_StructElement_CountChildren = _libs['pdfium']['FPDF_StructElement_CountChildren']
FPDF_StructElement_CountChildren.argtypes = [FPDF_STRUCTELEMENT]
FPDF_StructElement_CountChildren.restype = c_int

# ./fpdf_structtree.h: 267
FPDF_StructElement_GetChildAtIndex = _libs['pdfium']['FPDF_StructElement_GetChildAtIndex']
FPDF_StructElement_GetChildAtIndex.argtypes = [FPDF_STRUCTELEMENT, c_int]
FPDF_StructElement_GetChildAtIndex.restype = FPDF_STRUCTELEMENT

# ./fpdf_structtree.h: 286
FPDF_StructElement_GetChildMarkedContentID = _libs['pdfium']['FPDF_StructElement_GetChildMarkedContentID']
FPDF_StructElement_GetChildMarkedContentID.argtypes = [FPDF_STRUCTELEMENT, c_int]
FPDF_StructElement_GetChildMarkedContentID.restype = c_int

# ./fpdf_structtree.h: 300
FPDF_StructElement_GetParent = _libs['pdfium']['FPDF_StructElement_GetParent']
FPDF_StructElement_GetParent.argtypes = [FPDF_STRUCTELEMENT]
FPDF_StructElement_GetParent.restype = FPDF_STRUCTELEMENT

# ./fpdf_structtree.h: 309
FPDF_StructElement_GetAttributeCount = _libs['pdfium']['FPDF_StructElement_GetAttributeCount']
FPDF_StructElement_GetAttributeCount.argtypes = [FPDF_STRUCTELEMENT]
FPDF_StructElement_GetAttributeCount.restype = c_int

# ./fpdf_structtree.h: 327
FPDF_StructElement_GetAttributeAtIndex = _libs['pdfium']['FPDF_StructElement_GetAttributeAtIndex']
FPDF_StructElement_GetAttributeAtIndex.argtypes = [FPDF_STRUCTELEMENT, c_int]
FPDF_StructElement_GetAttributeAtIndex.restype = FPDF_STRUCTELEMENT_ATTR

# ./fpdf_structtree.h: 337
FPDF_StructElement_Attr_GetCount = _libs['pdfium']['FPDF_StructElement_Attr_GetCount']
FPDF_StructElement_Attr_GetCount.argtypes = [FPDF_STRUCTELEMENT_ATTR]
FPDF_StructElement_Attr_GetCount.restype = c_int

# ./fpdf_structtree.h: 357
FPDF_StructElement_Attr_GetName = _libs['pdfium']['FPDF_StructElement_Attr_GetName']
FPDF_StructElement_Attr_GetName.argtypes = [FPDF_STRUCTELEMENT_ATTR, c_int, POINTER(None), c_ulong, POINTER(c_ulong)]
FPDF_StructElement_Attr_GetName.restype = FPDF_BOOL

# ./fpdf_structtree.h: 375
FPDF_StructElement_Attr_GetValue = _libs['pdfium']['FPDF_StructElement_Attr_GetValue']
FPDF_StructElement_Attr_GetValue.argtypes = [FPDF_STRUCTELEMENT_ATTR, FPDF_BYTESTRING]
FPDF_StructElement_Attr_GetValue.restype = FPDF_STRUCTELEMENT_ATTR_VALUE

# ./fpdf_structtree.h: 388
FPDF_StructElement_Attr_GetType = _libs['pdfium']['FPDF_StructElement_Attr_GetType']
FPDF_StructElement_Attr_GetType.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE]
FPDF_StructElement_Attr_GetType.restype = FPDF_OBJECT_TYPE

# ./fpdf_structtree.h: 403
FPDF_StructElement_Attr_GetBooleanValue = _libs['pdfium']['FPDF_StructElement_Attr_GetBooleanValue']
FPDF_StructElement_Attr_GetBooleanValue.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE, POINTER(FPDF_BOOL)]
FPDF_StructElement_Attr_GetBooleanValue.restype = FPDF_BOOL

# ./fpdf_structtree.h: 419
FPDF_StructElement_Attr_GetNumberValue = _libs['pdfium']['FPDF_StructElement_Attr_GetNumberValue']
FPDF_StructElement_Attr_GetNumberValue.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE, POINTER(c_float)]
FPDF_StructElement_Attr_GetNumberValue.restype = FPDF_BOOL

# ./fpdf_structtree.h: 441
FPDF_StructElement_Attr_GetStringValue = _libs['pdfium']['FPDF_StructElement_Attr_GetStringValue']
FPDF_StructElement_Attr_GetStringValue.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE, POINTER(None), c_ulong, POINTER(c_ulong)]
FPDF_StructElement_Attr_GetStringValue.restype = FPDF_BOOL

# ./fpdf_structtree.h: 463
FPDF_StructElement_Attr_GetBlobValue = _libs['pdfium']['FPDF_StructElement_Attr_GetBlobValue']
FPDF_StructElement_Attr_GetBlobValue.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE, POINTER(None), c_ulong, POINTER(c_ulong)]
FPDF_StructElement_Attr_GetBlobValue.restype = FPDF_BOOL

# ./fpdf_structtree.h: 476
FPDF_StructElement_Attr_CountChildren = _libs['pdfium']['FPDF_StructElement_Attr_CountChildren']
FPDF_StructElement_Attr_CountChildren.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE]
FPDF_StructElement_Attr_CountChildren.restype = c_int

# ./fpdf_structtree.h: 490
FPDF_StructElement_Attr_GetChildAtIndex = _libs['pdfium']['FPDF_StructElement_Attr_GetChildAtIndex']
FPDF_StructElement_Attr_GetChildAtIndex.argtypes = [FPDF_STRUCTELEMENT_ATTR_VALUE, c_int]
FPDF_StructElement_Attr_GetChildAtIndex.restype = FPDF_STRUCTELEMENT_ATTR_VALUE

# ./fpdf_structtree.h: 501
FPDF_StructElement_GetMarkedContentIdCount = _libs['pdfium']['FPDF_StructElement_GetMarkedContentIdCount']
FPDF_StructElement_GetMarkedContentIdCount.argtypes = [FPDF_STRUCTELEMENT]
FPDF_StructElement_GetMarkedContentIdCount.restype = c_int

# ./fpdf_structtree.h: 517
FPDF_StructElement_GetMarkedContentIdAtIndex = _libs['pdfium']['FPDF_StructElement_GetMarkedContentIdAtIndex']
FPDF_StructElement_GetMarkedContentIdAtIndex.argtypes = [FPDF_STRUCTELEMENT, c_int]
FPDF_StructElement_GetMarkedContentIdAtIndex.restype = c_int

# ./fpdf_sysfontinfo.h: 48
class struct__FPDF_SYSFONTINFO (Structure):
    __slots__ = ['version', 'Release', 'EnumFonts', 'MapFont', 'GetFont', 'GetFontData', 'GetFaceName', 'GetFontCharset', 'DeleteFont']

struct__FPDF_SYSFONTINFO._fields_ = [
    ('version', c_int),
    ('Release', CFUNCTYPE(None, POINTER(struct__FPDF_SYSFONTINFO))),
    ('EnumFonts', CFUNCTYPE(None, POINTER(struct__FPDF_SYSFONTINFO), POINTER(None))),
    ('MapFont', CFUNCTYPE(POINTER(None), POINTER(struct__FPDF_SYSFONTINFO), c_int, FPDF_BOOL, c_int, c_int, POINTER(c_char), POINTER(FPDF_BOOL))),
    ('GetFont', CFUNCTYPE(POINTER(None), POINTER(struct__FPDF_SYSFONTINFO), POINTER(c_char))),
    ('GetFontData', CFUNCTYPE(c_ulong, POINTER(struct__FPDF_SYSFONTINFO), POINTER(None), c_uint, POINTER(c_ubyte), c_ulong)),
    ('GetFaceName', CFUNCTYPE(c_ulong, POINTER(struct__FPDF_SYSFONTINFO), POINTER(None), POINTER(c_char), c_ulong)),
    ('GetFontCharset', CFUNCTYPE(c_int, POINTER(struct__FPDF_SYSFONTINFO), POINTER(None))),
    ('DeleteFont', CFUNCTYPE(None, POINTER(struct__FPDF_SYSFONTINFO), POINTER(None))),
]

# ./fpdf_sysfontinfo.h: 209
FPDF_SYSFONTINFO = struct__FPDF_SYSFONTINFO

# ./fpdf_sysfontinfo.h: 216
class struct_FPDF_CharsetFontMap_ (Structure):
    __slots__ = ['charset', 'fontname']

struct_FPDF_CharsetFontMap_._fields_ = [
    ('charset', c_int),
    ('fontname', POINTER(c_char)),
]

# ./fpdf_sysfontinfo.h: 216
FPDF_CharsetFontMap = struct_FPDF_CharsetFontMap_

# ./fpdf_sysfontinfo.h: 230
FPDF_GetDefaultTTFMap = _libs['pdfium']['FPDF_GetDefaultTTFMap']
FPDF_GetDefaultTTFMap.argtypes = []
FPDF_GetDefaultTTFMap.restype = POINTER(FPDF_CharsetFontMap)

# ./fpdf_sysfontinfo.h: 241
FPDF_GetDefaultTTFMapCount = _libs['pdfium']['FPDF_GetDefaultTTFMapCount']
FPDF_GetDefaultTTFMapCount.argtypes = []
FPDF_GetDefaultTTFMapCount.restype = c_size_t

# ./fpdf_sysfontinfo.h: 252
FPDF_GetDefaultTTFMapEntry = _libs['pdfium']['FPDF_GetDefaultTTFMapEntry']
FPDF_GetDefaultTTFMapEntry.argtypes = [c_size_t]
FPDF_GetDefaultTTFMapEntry.restype = POINTER(FPDF_CharsetFontMap)

# ./fpdf_sysfontinfo.h: 266
FPDF_AddInstalledFont = _libs['pdfium']['FPDF_AddInstalledFont']
FPDF_AddInstalledFont.argtypes = [POINTER(None), POINTER(c_char), c_int]
FPDF_AddInstalledFont.restype = None

# ./fpdf_sysfontinfo.h: 284
FPDF_SetSystemFontInfo = _libs['pdfium']['FPDF_SetSystemFontInfo']
FPDF_SetSystemFontInfo.argtypes = [POINTER(FPDF_SYSFONTINFO)]
FPDF_SetSystemFontInfo.restype = None

# ./fpdf_sysfontinfo.h: 299
FPDF_GetDefaultSystemFontInfo = _libs['pdfium']['FPDF_GetDefaultSystemFontInfo']
FPDF_GetDefaultSystemFontInfo.argtypes = []
FPDF_GetDefaultSystemFontInfo.restype = POINTER(FPDF_SYSFONTINFO)

# ./fpdf_sysfontinfo.h: 311
FPDF_FreeDefaultSystemFontInfo = _libs['pdfium']['FPDF_FreeDefaultSystemFontInfo']
FPDF_FreeDefaultSystemFontInfo.argtypes = [POINTER(FPDF_SYSFONTINFO)]
FPDF_FreeDefaultSystemFontInfo.restype = None

# ./fpdf_text.h: 31
FPDFText_LoadPage = _libs['pdfium']['FPDFText_LoadPage']
FPDFText_LoadPage.argtypes = [FPDF_PAGE]
FPDFText_LoadPage.restype = FPDF_TEXTPAGE

# ./fpdf_text.h: 42
FPDFText_ClosePage = _libs['pdfium']['FPDFText_ClosePage']
FPDFText_ClosePage.argtypes = [FPDF_TEXTPAGE]
FPDFText_ClosePage.restype = None

# ./fpdf_text.h: 60
FPDFText_CountChars = _libs['pdfium']['FPDFText_CountChars']
FPDFText_CountChars.argtypes = [FPDF_TEXTPAGE]
FPDFText_CountChars.restype = c_int

# ./fpdf_text.h: 75
FPDFText_GetUnicode = _libs['pdfium']['FPDFText_GetUnicode']
FPDFText_GetUnicode.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetUnicode.restype = c_uint

# ./fpdf_text.h: 90
FPDFText_GetTextObject = _libs['pdfium']['FPDFText_GetTextObject']
FPDFText_GetTextObject.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetTextObject.restype = FPDF_PAGEOBJECT

# ./fpdf_text.h: 105
FPDFText_IsGenerated = _libs['pdfium']['FPDFText_IsGenerated']
FPDFText_IsGenerated.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_IsGenerated.restype = c_int

# ./fpdf_text.h: 120
FPDFText_IsHyphen = _libs['pdfium']['FPDFText_IsHyphen']
FPDFText_IsHyphen.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_IsHyphen.restype = c_int

# ./fpdf_text.h: 135
FPDFText_HasUnicodeMapError = _libs['pdfium']['FPDFText_HasUnicodeMapError']
FPDFText_HasUnicodeMapError.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_HasUnicodeMapError.restype = c_int

# ./fpdf_text.h: 148
FPDFText_GetFontSize = _libs['pdfium']['FPDFText_GetFontSize']
FPDFText_GetFontSize.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetFontSize.restype = c_double

# ./fpdf_text.h: 171
FPDFText_GetFontInfo = _libs['pdfium']['FPDFText_GetFontInfo']
FPDFText_GetFontInfo.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(None), c_ulong, POINTER(c_int)]
FPDFText_GetFontInfo.restype = c_ulong

# ./fpdf_text.h: 189
FPDFText_GetFontWeight = _libs['pdfium']['FPDFText_GetFontWeight']
FPDFText_GetFontWeight.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetFontWeight.restype = c_int

# ./fpdf_text.h: 212
FPDFText_GetFillColor = _libs['pdfium']['FPDFText_GetFillColor']
FPDFText_GetFillColor.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFText_GetFillColor.restype = FPDF_BOOL

# ./fpdf_text.h: 239
FPDFText_GetStrokeColor = _libs['pdfium']['FPDFText_GetStrokeColor']
FPDFText_GetStrokeColor.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(c_uint), POINTER(c_uint), POINTER(c_uint), POINTER(c_uint)]
FPDFText_GetStrokeColor.restype = FPDF_BOOL

# ./fpdf_text.h: 258
FPDFText_GetCharAngle = _libs['pdfium']['FPDFText_GetCharAngle']
FPDFText_GetCharAngle.argtypes = [FPDF_TEXTPAGE, c_int]
FPDFText_GetCharAngle.restype = c_float

# ./fpdf_text.h: 282
FPDFText_GetCharBox = _libs['pdfium']['FPDFText_GetCharBox']
FPDFText_GetCharBox.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(c_double), POINTER(c_double), POINTER(c_double), POINTER(c_double)]
FPDFText_GetCharBox.restype = FPDF_BOOL

# ./fpdf_text.h: 307
FPDFText_GetLooseCharBox = _libs['pdfium']['FPDFText_GetLooseCharBox']
FPDFText_GetLooseCharBox.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(FS_RECTF)]
FPDFText_GetLooseCharBox.restype = FPDF_BOOL

# ./fpdf_text.h: 323
FPDFText_GetMatrix = _libs['pdfium']['FPDFText_GetMatrix']
FPDFText_GetMatrix.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(FS_MATRIX)]
FPDFText_GetMatrix.restype = FPDF_BOOL

# ./fpdf_text.h: 343
FPDFText_GetCharOrigin = _libs['pdfium']['FPDFText_GetCharOrigin']
FPDFText_GetCharOrigin.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(c_double), POINTER(c_double)]
FPDFText_GetCharOrigin.restype = FPDF_BOOL

# ./fpdf_text.h: 366
FPDFText_GetCharIndexAtPos = _libs['pdfium']['FPDFText_GetCharIndexAtPos']
FPDFText_GetCharIndexAtPos.argtypes = [FPDF_TEXTPAGE, c_double, c_double, c_double, c_double]
FPDFText_GetCharIndexAtPos.restype = c_int

# ./fpdf_text.h: 392
FPDFText_GetText = _libs['pdfium']['FPDFText_GetText']
FPDFText_GetText.argtypes = [FPDF_TEXTPAGE, c_int, c_int, POINTER(c_ushort)]
FPDFText_GetText.restype = c_int

# ./fpdf_text.h: 415
FPDFText_CountRects = _libs['pdfium']['FPDFText_CountRects']
FPDFText_CountRects.argtypes = [FPDF_TEXTPAGE, c_int, c_int]
FPDFText_CountRects.restype = c_int

# ./fpdf_text.h: 441
FPDFText_GetRect = _libs['pdfium']['FPDFText_GetRect']
FPDFText_GetRect.argtypes = [FPDF_TEXTPAGE, c_int, POINTER(c_double), POINTER(c_double), POINTER(c_double), POINTER(c_double)]
FPDFText_GetRect.restype = FPDF_BOOL

# ./fpdf_text.h: 472
FPDFText_GetBoundedText = _libs['pdfium']['FPDFText_GetBoundedText']
FPDFText_GetBoundedText.argtypes = [FPDF_TEXTPAGE, c_double, c_double, c_double, c_double, POINTER(c_ushort), c_int]
FPDFText_GetBoundedText.restype = c_int

# ./fpdf_text.h: 502
FPDFText_FindStart = _libs['pdfium']['FPDFText_FindStart']
FPDFText_FindStart.argtypes = [FPDF_TEXTPAGE, FPDF_WIDESTRING, c_ulong, c_int]
FPDFText_FindStart.restype = FPDF_SCHHANDLE

# ./fpdf_text.h: 515
FPDFText_FindNext = _libs['pdfium']['FPDFText_FindNext']
FPDFText_FindNext.argtypes = [FPDF_SCHHANDLE]
FPDFText_FindNext.restype = FPDF_BOOL

# ./fpdf_text.h: 525
FPDFText_FindPrev = _libs['pdfium']['FPDFText_FindPrev']
FPDFText_FindPrev.argtypes = [FPDF_SCHHANDLE]
FPDFText_FindPrev.restype = FPDF_BOOL

# ./fpdf_text.h: 535
FPDFText_GetSchResultIndex = _libs['pdfium']['FPDFText_GetSchResultIndex']
FPDFText_GetSchResultIndex.argtypes = [FPDF_SCHHANDLE]
FPDFText_GetSchResultIndex.restype = c_int

# ./fpdf_text.h: 545
FPDFText_GetSchCount = _libs['pdfium']['FPDFText_GetSchCount']
FPDFText_GetSchCount.argtypes = [FPDF_SCHHANDLE]
FPDFText_GetSchCount.restype = c_int

# ./fpdf_text.h: 555
FPDFText_FindClose = _libs['pdfium']['FPDFText_FindClose']
FPDFText_FindClose.argtypes = [FPDF_SCHHANDLE]
FPDFText_FindClose.restype = None

# ./fpdf_text.h: 577
FPDFLink_LoadWebLinks = _libs['pdfium']['FPDFLink_LoadWebLinks']
FPDFLink_LoadWebLinks.argtypes = [FPDF_TEXTPAGE]
FPDFLink_LoadWebLinks.restype = FPDF_PAGELINK

# ./fpdf_text.h: 586
FPDFLink_CountWebLinks = _libs['pdfium']['FPDFLink_CountWebLinks']
FPDFLink_CountWebLinks.argtypes = [FPDF_PAGELINK]
FPDFLink_CountWebLinks.restype = c_int

# ./fpdf_text.h: 607
FPDFLink_GetURL = _libs['pdfium']['FPDFLink_GetURL']
FPDFLink_GetURL.argtypes = [FPDF_PAGELINK, c_int, POINTER(c_ushort), c_int]
FPDFLink_GetURL.restype = c_int

# ./fpdf_text.h: 621
FPDFLink_CountRects = _libs['pdfium']['FPDFLink_CountRects']
FPDFLink_CountRects.argtypes = [FPDF_PAGELINK, c_int]
FPDFLink_CountRects.restype = c_int

# ./fpdf_text.h: 644
FPDFLink_GetRect = _libs['pdfium']['FPDFLink_GetRect']
FPDFLink_GetRect.argtypes = [FPDF_PAGELINK, c_int, c_int, POINTER(c_double), POINTER(c_double), POINTER(c_double), POINTER(c_double)]
FPDFLink_GetRect.restype = FPDF_BOOL

# ./fpdf_text.h: 667
FPDFLink_GetTextRange = _libs['pdfium']['FPDFLink_GetTextRange']
FPDFLink_GetTextRange.argtypes = [FPDF_PAGELINK, c_int, POINTER(c_int), POINTER(c_int)]
FPDFLink_GetTextRange.restype = FPDF_BOOL

# ./fpdf_text.h: 679
FPDFLink_CloseWebLinks = _libs['pdfium']['FPDFLink_CloseWebLinks']
FPDFLink_CloseWebLinks.argtypes = [FPDF_PAGELINK]
FPDFLink_CloseWebLinks.restype = None

# ./fpdf_thumbnail.h: 28
FPDFPage_GetDecodedThumbnailData = _libs['pdfium']['FPDFPage_GetDecodedThumbnailData']
FPDFPage_GetDecodedThumbnailData.argtypes = [FPDF_PAGE, POINTER(None), c_ulong]
FPDFPage_GetDecodedThumbnailData.restype = c_ulong

# ./fpdf_thumbnail.h: 43
FPDFPage_GetRawThumbnailData = _libs['pdfium']['FPDFPage_GetRawThumbnailData']
FPDFPage_GetRawThumbnailData.argtypes = [FPDF_PAGE, POINTER(None), c_ulong]
FPDFPage_GetRawThumbnailData.restype = c_ulong

# ./fpdf_thumbnail.h: 53
FPDFPage_GetThumbnailAsBitmap = _libs['pdfium']['FPDFPage_GetThumbnailAsBitmap']
FPDFPage_GetThumbnailAsBitmap.argtypes = [FPDF_PAGE]
FPDFPage_GetThumbnailAsBitmap.restype = FPDF_BITMAP

# ./fpdf_transformpage.h: 24
FPDFPage_SetMediaBox = _libs['pdfium']['FPDFPage_SetMediaBox']
FPDFPage_SetMediaBox.argtypes = [FPDF_PAGE, c_float, c_float, c_float, c_float]
FPDFPage_SetMediaBox.restype = None

# ./fpdf_transformpage.h: 37
FPDFPage_SetCropBox = _libs['pdfium']['FPDFPage_SetCropBox']
FPDFPage_SetCropBox.argtypes = [FPDF_PAGE, c_float, c_float, c_float, c_float]
FPDFPage_SetCropBox.restype = None

# ./fpdf_transformpage.h: 50
FPDFPage_SetBleedBox = _libs['pdfium']['FPDFPage_SetBleedBox']
FPDFPage_SetBleedBox.argtypes = [FPDF_PAGE, c_float, c_float, c_float, c_float]
FPDFPage_SetBleedBox.restype = None

# ./fpdf_transformpage.h: 63
FPDFPage_SetTrimBox = _libs['pdfium']['FPDFPage_SetTrimBox']
FPDFPage_SetTrimBox.argtypes = [FPDF_PAGE, c_float, c_float, c_float, c_float]
FPDFPage_SetTrimBox.restype = None

# ./fpdf_transformpage.h: 76
FPDFPage_SetArtBox = _libs['pdfium']['FPDFPage_SetArtBox']
FPDFPage_SetArtBox.argtypes = [FPDF_PAGE, c_float, c_float, c_float, c_float]
FPDFPage_SetArtBox.restype = None

# ./fpdf_transformpage.h: 92
FPDFPage_GetMediaBox = _libs['pdfium']['FPDFPage_GetMediaBox']
FPDFPage_GetMediaBox.argtypes = [FPDF_PAGE, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPage_GetMediaBox.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 108
FPDFPage_GetCropBox = _libs['pdfium']['FPDFPage_GetCropBox']
FPDFPage_GetCropBox.argtypes = [FPDF_PAGE, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPage_GetCropBox.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 124
FPDFPage_GetBleedBox = _libs['pdfium']['FPDFPage_GetBleedBox']
FPDFPage_GetBleedBox.argtypes = [FPDF_PAGE, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPage_GetBleedBox.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 140
FPDFPage_GetTrimBox = _libs['pdfium']['FPDFPage_GetTrimBox']
FPDFPage_GetTrimBox.argtypes = [FPDF_PAGE, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPage_GetTrimBox.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 156
FPDFPage_GetArtBox = _libs['pdfium']['FPDFPage_GetArtBox']
FPDFPage_GetArtBox.argtypes = [FPDF_PAGE, POINTER(c_float), POINTER(c_float), POINTER(c_float), POINTER(c_float)]
FPDFPage_GetArtBox.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 176
FPDFPage_TransFormWithClip = _libs['pdfium']['FPDFPage_TransFormWithClip']
FPDFPage_TransFormWithClip.argtypes = [FPDF_PAGE, POINTER(FS_MATRIX), POINTER(FS_RECTF)]
FPDFPage_TransFormWithClip.restype = FPDF_BOOL

# ./fpdf_transformpage.h: 191
FPDFPageObj_TransformClipPath = _libs['pdfium']['FPDFPageObj_TransformClipPath']
FPDFPageObj_TransformClipPath.argtypes = [FPDF_PAGEOBJECT, c_double, c_double, c_double, c_double, c_double, c_double]
FPDFPageObj_TransformClipPath.restype = None

# ./fpdf_transformpage.h: 209
FPDFPageObj_GetClipPath = _libs['pdfium']['FPDFPageObj_GetClipPath']
FPDFPageObj_GetClipPath.argtypes = [FPDF_PAGEOBJECT]
FPDFPageObj_GetClipPath.restype = FPDF_CLIPPATH

# ./fpdf_transformpage.h: 217
FPDFClipPath_CountPaths = _libs['pdfium']['FPDFClipPath_CountPaths']
FPDFClipPath_CountPaths.argtypes = [FPDF_CLIPPATH]
FPDFClipPath_CountPaths.restype = c_int

# ./fpdf_transformpage.h: 227
FPDFClipPath_CountPathSegments = _libs['pdfium']['FPDFClipPath_CountPathSegments']
FPDFClipPath_CountPathSegments.argtypes = [FPDF_CLIPPATH, c_int]
FPDFClipPath_CountPathSegments.restype = c_int

# ./fpdf_transformpage.h: 240
FPDFClipPath_GetPathSegment = _libs['pdfium']['FPDFClipPath_GetPathSegment']
FPDFClipPath_GetPathSegment.argtypes = [FPDF_CLIPPATH, c_int, c_int]
FPDFClipPath_GetPathSegment.restype = FPDF_PATHSEGMENT

# ./fpdf_transformpage.h: 253
FPDF_CreateClipPath = _libs['pdfium']['FPDF_CreateClipPath']
FPDF_CreateClipPath.argtypes = [c_float, c_float, c_float, c_float]
FPDF_CreateClipPath.restype = FPDF_CLIPPATH

# ./fpdf_transformpage.h: 261
FPDF_DestroyClipPath = _libs['pdfium']['FPDF_DestroyClipPath']
FPDF_DestroyClipPath.argtypes = [FPDF_CLIPPATH]
FPDF_DestroyClipPath.restype = None

# ./fpdf_transformpage.h: 271
FPDFPage_InsertClipPath = _libs['pdfium']['FPDFPage_InsertClipPath']
FPDFPage_InsertClipPath.argtypes = [FPDF_PAGE, FPDF_CLIPPATH]
FPDFPage_InsertClipPath.restype = None

# ./fpdfview.h: 36
FPDF_OBJECT_UNKNOWN = 0

# ./fpdfview.h: 37
FPDF_OBJECT_BOOLEAN = 1

# ./fpdfview.h: 38
FPDF_OBJECT_NUMBER = 2

# ./fpdfview.h: 39
FPDF_OBJECT_STRING = 3

# ./fpdfview.h: 40
FPDF_OBJECT_NAME = 4

# ./fpdfview.h: 41
FPDF_OBJECT_ARRAY = 5

# ./fpdfview.h: 42
FPDF_OBJECT_DICTIONARY = 6

# ./fpdfview.h: 43
FPDF_OBJECT_STREAM = 7

# ./fpdfview.h: 44
FPDF_OBJECT_NULLOBJ = 8

# ./fpdfview.h: 45
FPDF_OBJECT_REFERENCE = 9

# ./fpdfview.h: 327
FPDF_POLICY_MACHINETIME_ACCESS = 0

# ./fpdfview.h: 583
FPDF_ERR_SUCCESS = 0

# ./fpdfview.h: 584
FPDF_ERR_UNKNOWN = 1

# ./fpdfview.h: 585
FPDF_ERR_FILE = 2

# ./fpdfview.h: 586
FPDF_ERR_FORMAT = 3

# ./fpdfview.h: 587
FPDF_ERR_PASSWORD = 4

# ./fpdfview.h: 588
FPDF_ERR_SECURITY = 5

# ./fpdfview.h: 589
FPDF_ERR_PAGE = 6

# ./fpdfview.h: 790
FPDF_ANNOT = 0x01

# ./fpdfview.h: 793
FPDF_LCD_TEXT = 0x02

# ./fpdfview.h: 795
FPDF_NO_NATIVETEXT = 0x04

# ./fpdfview.h: 797
FPDF_GRAYSCALE = 0x08

# ./fpdfview.h: 799
FPDF_DEBUG_INFO = 0x80

# ./fpdfview.h: 801
FPDF_NO_CATCH = 0x100

# ./fpdfview.h: 803
FPDF_RENDER_LIMITEDIMAGECACHE = 0x200

# ./fpdfview.h: 805
FPDF_RENDER_FORCEHALFTONE = 0x400

# ./fpdfview.h: 807
FPDF_PRINTING = 0x800

# ./fpdfview.h: 810
FPDF_RENDER_NO_SMOOTHTEXT = 0x1000

# ./fpdfview.h: 812
FPDF_RENDER_NO_SMOOTHIMAGE = 0x2000

# ./fpdfview.h: 814
FPDF_RENDER_NO_SMOOTHPATH = 0x4000

# ./fpdfview.h: 817
FPDF_REVERSE_BYTE_ORDER = 0x10

# ./fpdfview.h: 821
FPDF_CONVERT_FILL_TO_STROKE = 0x20

# ./fpdfview.h: 1083
FPDFBitmap_Unknown = 0

# ./fpdfview.h: 1085
FPDFBitmap_Gray = 1

# ./fpdfview.h: 1087
FPDFBitmap_BGR = 2

# ./fpdfview.h: 1089
FPDFBitmap_BGRx = 3

# ./fpdfview.h: 1091
FPDFBitmap_BGRA = 4

# ./fpdf_formfill.h: 16
FORMTYPE_NONE = 0

# ./fpdf_formfill.h: 17
FORMTYPE_ACRO_FORM = 1

# ./fpdf_formfill.h: 18
FORMTYPE_XFA_FULL = 2

# ./fpdf_formfill.h: 19
FORMTYPE_XFA_FOREGROUND = 3

# ./fpdf_formfill.h: 21
FORMTYPE_COUNT = 4

# ./fpdf_formfill.h: 23
JSPLATFORM_ALERT_BUTTON_OK = 0

# ./fpdf_formfill.h: 24
JSPLATFORM_ALERT_BUTTON_OKCANCEL = 1

# ./fpdf_formfill.h: 25
JSPLATFORM_ALERT_BUTTON_YESNO = 2

# ./fpdf_formfill.h: 26
JSPLATFORM_ALERT_BUTTON_YESNOCANCEL = 3

# ./fpdf_formfill.h: 27
JSPLATFORM_ALERT_BUTTON_DEFAULT = JSPLATFORM_ALERT_BUTTON_OK

# ./fpdf_formfill.h: 29
JSPLATFORM_ALERT_ICON_ERROR = 0

# ./fpdf_formfill.h: 30
JSPLATFORM_ALERT_ICON_WARNING = 1

# ./fpdf_formfill.h: 31
JSPLATFORM_ALERT_ICON_QUESTION = 2

# ./fpdf_formfill.h: 32
JSPLATFORM_ALERT_ICON_STATUS = 3

# ./fpdf_formfill.h: 33
JSPLATFORM_ALERT_ICON_ASTERISK = 4

# ./fpdf_formfill.h: 34
JSPLATFORM_ALERT_ICON_DEFAULT = JSPLATFORM_ALERT_ICON_ERROR

# ./fpdf_formfill.h: 36
JSPLATFORM_ALERT_RETURN_OK = 1

# ./fpdf_formfill.h: 37
JSPLATFORM_ALERT_RETURN_CANCEL = 2

# ./fpdf_formfill.h: 38
JSPLATFORM_ALERT_RETURN_NO = 3

# ./fpdf_formfill.h: 39
JSPLATFORM_ALERT_RETURN_YES = 4

# ./fpdf_formfill.h: 41
JSPLATFORM_BEEP_ERROR = 0

# ./fpdf_formfill.h: 42
JSPLATFORM_BEEP_WARNING = 1

# ./fpdf_formfill.h: 43
JSPLATFORM_BEEP_QUESTION = 2

# ./fpdf_formfill.h: 44
JSPLATFORM_BEEP_STATUS = 3

# ./fpdf_formfill.h: 45
JSPLATFORM_BEEP_DEFAULT = 4

# ./fpdf_formfill.h: 303
FXCT_ARROW = 0

# ./fpdf_formfill.h: 304
FXCT_NESW = 1

# ./fpdf_formfill.h: 305
FXCT_NWSE = 2

# ./fpdf_formfill.h: 306
FXCT_VBEAM = 3

# ./fpdf_formfill.h: 307
FXCT_HBEAM = 4

# ./fpdf_formfill.h: 308
FXCT_HAND = 5

# ./fpdf_formfill.h: 1132
FPDFDOC_AACTION_WC = 0x10

# ./fpdf_formfill.h: 1133
FPDFDOC_AACTION_WS = 0x11

# ./fpdf_formfill.h: 1134
FPDFDOC_AACTION_DS = 0x12

# ./fpdf_formfill.h: 1135
FPDFDOC_AACTION_WP = 0x13

# ./fpdf_formfill.h: 1136
FPDFDOC_AACTION_DP = 0x14

# ./fpdf_formfill.h: 1157
FPDFPAGE_AACTION_OPEN = 0

# ./fpdf_formfill.h: 1158
FPDFPAGE_AACTION_CLOSE = 1

# ./fpdf_formfill.h: 1577
FPDF_FORMFIELD_UNKNOWN = 0

# ./fpdf_formfill.h: 1578
FPDF_FORMFIELD_PUSHBUTTON = 1

# ./fpdf_formfill.h: 1579
FPDF_FORMFIELD_CHECKBOX = 2

# ./fpdf_formfill.h: 1580
FPDF_FORMFIELD_RADIOBUTTON = 3

# ./fpdf_formfill.h: 1581
FPDF_FORMFIELD_COMBOBOX = 4

# ./fpdf_formfill.h: 1582
FPDF_FORMFIELD_LISTBOX = 5

# ./fpdf_formfill.h: 1583
FPDF_FORMFIELD_TEXTFIELD = 6

# ./fpdf_formfill.h: 1584
FPDF_FORMFIELD_SIGNATURE = 7

# ./fpdf_formfill.h: 1599
FPDF_FORMFIELD_COUNT = 8

# ./fpdf_annot.h: 20
FPDF_ANNOT_UNKNOWN = 0

# ./fpdf_annot.h: 21
FPDF_ANNOT_TEXT = 1

# ./fpdf_annot.h: 22
FPDF_ANNOT_LINK = 2

# ./fpdf_annot.h: 23
FPDF_ANNOT_FREETEXT = 3

# ./fpdf_annot.h: 24
FPDF_ANNOT_LINE = 4

# ./fpdf_annot.h: 25
FPDF_ANNOT_SQUARE = 5

# ./fpdf_annot.h: 26
FPDF_ANNOT_CIRCLE = 6

# ./fpdf_annot.h: 27
FPDF_ANNOT_POLYGON = 7

# ./fpdf_annot.h: 28
FPDF_ANNOT_POLYLINE = 8

# ./fpdf_annot.h: 29
FPDF_ANNOT_HIGHLIGHT = 9

# ./fpdf_annot.h: 30
FPDF_ANNOT_UNDERLINE = 10

# ./fpdf_annot.h: 31
FPDF_ANNOT_SQUIGGLY = 11

# ./fpdf_annot.h: 32
FPDF_ANNOT_STRIKEOUT = 12

# ./fpdf_annot.h: 33
FPDF_ANNOT_STAMP = 13

# ./fpdf_annot.h: 34
FPDF_ANNOT_CARET = 14

# ./fpdf_annot.h: 35
FPDF_ANNOT_INK = 15

# ./fpdf_annot.h: 36
FPDF_ANNOT_POPUP = 16

# ./fpdf_annot.h: 37
FPDF_ANNOT_FILEATTACHMENT = 17

# ./fpdf_annot.h: 38
FPDF_ANNOT_SOUND = 18

# ./fpdf_annot.h: 39
FPDF_ANNOT_MOVIE = 19

# ./fpdf_annot.h: 40
FPDF_ANNOT_WIDGET = 20

# ./fpdf_annot.h: 41
FPDF_ANNOT_SCREEN = 21

# ./fpdf_annot.h: 42
FPDF_ANNOT_PRINTERMARK = 22

# ./fpdf_annot.h: 43
FPDF_ANNOT_TRAPNET = 23

# ./fpdf_annot.h: 44
FPDF_ANNOT_WATERMARK = 24

# ./fpdf_annot.h: 45
FPDF_ANNOT_THREED = 25

# ./fpdf_annot.h: 46
FPDF_ANNOT_RICHMEDIA = 26

# ./fpdf_annot.h: 47
FPDF_ANNOT_XFAWIDGET = 27

# ./fpdf_annot.h: 48
FPDF_ANNOT_REDACT = 28

# ./fpdf_annot.h: 51
FPDF_ANNOT_FLAG_NONE = 0

# ./fpdf_annot.h: 52
FPDF_ANNOT_FLAG_INVISIBLE = (1 << 0)

# ./fpdf_annot.h: 53
FPDF_ANNOT_FLAG_HIDDEN = (1 << 1)

# ./fpdf_annot.h: 54
FPDF_ANNOT_FLAG_PRINT = (1 << 2)

# ./fpdf_annot.h: 55
FPDF_ANNOT_FLAG_NOZOOM = (1 << 3)

# ./fpdf_annot.h: 56
FPDF_ANNOT_FLAG_NOROTATE = (1 << 4)

# ./fpdf_annot.h: 57
FPDF_ANNOT_FLAG_NOVIEW = (1 << 5)

# ./fpdf_annot.h: 58
FPDF_ANNOT_FLAG_READONLY = (1 << 6)

# ./fpdf_annot.h: 59
FPDF_ANNOT_FLAG_LOCKED = (1 << 7)

# ./fpdf_annot.h: 60
FPDF_ANNOT_FLAG_TOGGLENOVIEW = (1 << 8)

# ./fpdf_annot.h: 62
FPDF_ANNOT_APPEARANCEMODE_NORMAL = 0

# ./fpdf_annot.h: 63
FPDF_ANNOT_APPEARANCEMODE_ROLLOVER = 1

# ./fpdf_annot.h: 64
FPDF_ANNOT_APPEARANCEMODE_DOWN = 2

# ./fpdf_annot.h: 65
FPDF_ANNOT_APPEARANCEMODE_COUNT = 3

# ./fpdf_annot.h: 69
FPDF_FORMFLAG_NONE = 0

# ./fpdf_annot.h: 70
FPDF_FORMFLAG_READONLY = (1 << 0)

# ./fpdf_annot.h: 71
FPDF_FORMFLAG_REQUIRED = (1 << 1)

# ./fpdf_annot.h: 72
FPDF_FORMFLAG_NOEXPORT = (1 << 2)

# ./fpdf_annot.h: 76
FPDF_FORMFLAG_TEXT_MULTILINE = (1 << 12)

# ./fpdf_annot.h: 77
FPDF_FORMFLAG_TEXT_PASSWORD = (1 << 13)

# ./fpdf_annot.h: 81
FPDF_FORMFLAG_CHOICE_COMBO = (1 << 17)

# ./fpdf_annot.h: 82
FPDF_FORMFLAG_CHOICE_EDIT = (1 << 18)

# ./fpdf_annot.h: 83
FPDF_FORMFLAG_CHOICE_MULTI_SELECT = (1 << 21)

# ./fpdf_annot.h: 90
FPDF_ANNOT_AACTION_KEY_STROKE = 12

# ./fpdf_annot.h: 91
FPDF_ANNOT_AACTION_FORMAT = 13

# ./fpdf_annot.h: 92
FPDF_ANNOT_AACTION_VALIDATE = 14

# ./fpdf_annot.h: 93
FPDF_ANNOT_AACTION_CALCULATE = 15

# ./fpdf_dataavail.h: 15
PDF_LINEARIZATION_UNKNOWN = (-1)

# ./fpdf_dataavail.h: 16
PDF_NOT_LINEARIZED = 0

# ./fpdf_dataavail.h: 17
PDF_LINEARIZED = 1

# ./fpdf_dataavail.h: 19
PDF_DATA_ERROR = (-1)

# ./fpdf_dataavail.h: 20
PDF_DATA_NOTAVAIL = 0

# ./fpdf_dataavail.h: 21
PDF_DATA_AVAIL = 1

# ./fpdf_dataavail.h: 23
PDF_FORM_ERROR = (-1)

# ./fpdf_dataavail.h: 24
PDF_FORM_NOTAVAIL = 0

# ./fpdf_dataavail.h: 25
PDF_FORM_AVAIL = 1

# ./fpdf_dataavail.h: 26
PDF_FORM_NOTEXIST = 2

# ./fpdf_doc.h: 18
PDFACTION_UNSUPPORTED = 0

# ./fpdf_doc.h: 20
PDFACTION_GOTO = 1

# ./fpdf_doc.h: 22
PDFACTION_REMOTEGOTO = 2

# ./fpdf_doc.h: 24
PDFACTION_URI = 3

# ./fpdf_doc.h: 26
PDFACTION_LAUNCH = 4

# ./fpdf_doc.h: 28
PDFACTION_EMBEDDEDGOTO = 5

# ./fpdf_doc.h: 31
PDFDEST_VIEW_UNKNOWN_MODE = 0

# ./fpdf_doc.h: 32
PDFDEST_VIEW_XYZ = 1

# ./fpdf_doc.h: 33
PDFDEST_VIEW_FIT = 2

# ./fpdf_doc.h: 34
PDFDEST_VIEW_FITH = 3

# ./fpdf_doc.h: 35
PDFDEST_VIEW_FITV = 4

# ./fpdf_doc.h: 36
PDFDEST_VIEW_FITR = 5

# ./fpdf_doc.h: 37
PDFDEST_VIEW_FITB = 6

# ./fpdf_doc.h: 38
PDFDEST_VIEW_FITBH = 7

# ./fpdf_doc.h: 39
PDFDEST_VIEW_FITBV = 8

# ./fpdf_edit.h: 15
def FPDF_ARGB(a, r, g, b):
    return uint32_t(((((uint32_t(b).value & 0xff) | ((uint32_t(g).value & 0xff) << 8)) | ((uint32_t(r).value & 0xff) << 16)) | ((uint32_t(a).value & 0xff) << 24))).value

# ./fpdf_edit.h: 18
def FPDF_GetBValue(argb):
    return uint8_t(argb).value

# ./fpdf_edit.h: 19
def FPDF_GetGValue(argb):
    return uint8_t((uint16_t(argb).value >> 8)).value

# ./fpdf_edit.h: 20
def FPDF_GetRValue(argb):
    return uint8_t((argb >> 16)).value

# ./fpdf_edit.h: 21
def FPDF_GetAValue(argb):
    return uint8_t((argb >> 24)).value

# ./fpdf_edit.h: 24
FPDF_COLORSPACE_UNKNOWN = 0

# ./fpdf_edit.h: 25
FPDF_COLORSPACE_DEVICEGRAY = 1

# ./fpdf_edit.h: 26
FPDF_COLORSPACE_DEVICERGB = 2

# ./fpdf_edit.h: 27
FPDF_COLORSPACE_DEVICECMYK = 3

# ./fpdf_edit.h: 28
FPDF_COLORSPACE_CALGRAY = 4

# ./fpdf_edit.h: 29
FPDF_COLORSPACE_CALRGB = 5

# ./fpdf_edit.h: 30
FPDF_COLORSPACE_LAB = 6

# ./fpdf_edit.h: 31
FPDF_COLORSPACE_ICCBASED = 7

# ./fpdf_edit.h: 32
FPDF_COLORSPACE_SEPARATION = 8

# ./fpdf_edit.h: 33
FPDF_COLORSPACE_DEVICEN = 9

# ./fpdf_edit.h: 34
FPDF_COLORSPACE_INDEXED = 10

# ./fpdf_edit.h: 35
FPDF_COLORSPACE_PATTERN = 11

# ./fpdf_edit.h: 38
FPDF_PAGEOBJ_UNKNOWN = 0

# ./fpdf_edit.h: 39
FPDF_PAGEOBJ_TEXT = 1

# ./fpdf_edit.h: 40
FPDF_PAGEOBJ_PATH = 2

# ./fpdf_edit.h: 41
FPDF_PAGEOBJ_IMAGE = 3

# ./fpdf_edit.h: 42
FPDF_PAGEOBJ_SHADING = 4

# ./fpdf_edit.h: 43
FPDF_PAGEOBJ_FORM = 5

# ./fpdf_edit.h: 46
FPDF_SEGMENT_UNKNOWN = (-1)

# ./fpdf_edit.h: 47
FPDF_SEGMENT_LINETO = 0

# ./fpdf_edit.h: 48
FPDF_SEGMENT_BEZIERTO = 1

# ./fpdf_edit.h: 49
FPDF_SEGMENT_MOVETO = 2

# ./fpdf_edit.h: 51
FPDF_FILLMODE_NONE = 0

# ./fpdf_edit.h: 52
FPDF_FILLMODE_ALTERNATE = 1

# ./fpdf_edit.h: 53
FPDF_FILLMODE_WINDING = 2

# ./fpdf_edit.h: 55
FPDF_FONT_TYPE1 = 1

# ./fpdf_edit.h: 56
FPDF_FONT_TRUETYPE = 2

# ./fpdf_edit.h: 58
FPDF_LINECAP_BUTT = 0

# ./fpdf_edit.h: 59
FPDF_LINECAP_ROUND = 1

# ./fpdf_edit.h: 60
FPDF_LINECAP_PROJECTING_SQUARE = 2

# ./fpdf_edit.h: 62
FPDF_LINEJOIN_MITER = 0

# ./fpdf_edit.h: 63
FPDF_LINEJOIN_ROUND = 1

# ./fpdf_edit.h: 64
FPDF_LINEJOIN_BEVEL = 2

# ./fpdf_edit.h: 67
FPDF_PRINTMODE_EMF = 0

# ./fpdf_edit.h: 68
FPDF_PRINTMODE_TEXTONLY = 1

# ./fpdf_edit.h: 69
FPDF_PRINTMODE_POSTSCRIPT2 = 2

# ./fpdf_edit.h: 70
FPDF_PRINTMODE_POSTSCRIPT3 = 3

# ./fpdf_edit.h: 71
FPDF_PRINTMODE_POSTSCRIPT2_PASSTHROUGH = 4

# ./fpdf_edit.h: 72
FPDF_PRINTMODE_POSTSCRIPT3_PASSTHROUGH = 5

# ./fpdf_edit.h: 73
FPDF_PRINTMODE_EMF_IMAGE_MASKS = 6

# ./fpdf_edit.h: 74
FPDF_PRINTMODE_POSTSCRIPT3_TYPE42 = 7

# ./fpdf_edit.h: 75
FPDF_PRINTMODE_POSTSCRIPT3_TYPE42_PASSTHROUGH = 8

# ./fpdf_ext.h: 20
FPDF_UNSP_DOC_XFAFORM = 1

# ./fpdf_ext.h: 22
FPDF_UNSP_DOC_PORTABLECOLLECTION = 2

# ./fpdf_ext.h: 24
FPDF_UNSP_DOC_ATTACHMENT = 3

# ./fpdf_ext.h: 26
FPDF_UNSP_DOC_SECURITY = 4

# ./fpdf_ext.h: 28
FPDF_UNSP_DOC_SHAREDREVIEW = 5

# ./fpdf_ext.h: 30
FPDF_UNSP_DOC_SHAREDFORM_ACROBAT = 6

# ./fpdf_ext.h: 32
FPDF_UNSP_DOC_SHAREDFORM_FILESYSTEM = 7

# ./fpdf_ext.h: 34
FPDF_UNSP_DOC_SHAREDFORM_EMAIL = 8

# ./fpdf_ext.h: 36
FPDF_UNSP_ANNOT_3DANNOT = 11

# ./fpdf_ext.h: 38
FPDF_UNSP_ANNOT_MOVIE = 12

# ./fpdf_ext.h: 40
FPDF_UNSP_ANNOT_SOUND = 13

# ./fpdf_ext.h: 42
FPDF_UNSP_ANNOT_SCREEN_MEDIA = 14

# ./fpdf_ext.h: 44
FPDF_UNSP_ANNOT_SCREEN_RICHMEDIA = 15

# ./fpdf_ext.h: 46
FPDF_UNSP_ANNOT_ATTACHMENT = 16

# ./fpdf_ext.h: 48
FPDF_UNSP_ANNOT_SIG = 17

# ./fpdf_ext.h: 92
PAGEMODE_UNKNOWN = (-1)

# ./fpdf_ext.h: 94
PAGEMODE_USENONE = 0

# ./fpdf_ext.h: 96
PAGEMODE_USEOUTLINES = 1

# ./fpdf_ext.h: 98
PAGEMODE_USETHUMBS = 2

# ./fpdf_ext.h: 100
PAGEMODE_FULLSCREEN = 3

# ./fpdf_ext.h: 102
PAGEMODE_USEOC = 4

# ./fpdf_ext.h: 104
PAGEMODE_USEATTACHMENTS = 5

# ./fpdf_flatten.h: 14
FLATTEN_FAIL = 0

# ./fpdf_flatten.h: 16
FLATTEN_SUCCESS = 1

# ./fpdf_flatten.h: 18
FLATTEN_NOTHINGTODO = 2

# ./fpdf_flatten.h: 21
FLAT_NORMALDISPLAY = 0

# ./fpdf_flatten.h: 23
FLAT_PRINT = 1

# ./fpdf_progressive.h: 15
FPDF_RENDER_READY = 0

# ./fpdf_progressive.h: 16
FPDF_RENDER_TOBECONTINUED = 1

# ./fpdf_progressive.h: 17
FPDF_RENDER_DONE = 2

# ./fpdf_progressive.h: 18
FPDF_RENDER_FAILED = 3

# ./fpdf_save.h: 45
FPDF_INCREMENTAL = 1

# ./fpdf_save.h: 46
FPDF_NO_INCREMENTAL = 2

# ./fpdf_save.h: 47
FPDF_REMOVE_SECURITY = 3

# ./fpdf_sysfontinfo.h: 17
FXFONT_ANSI_CHARSET = 0

# ./fpdf_sysfontinfo.h: 18
FXFONT_DEFAULT_CHARSET = 1

# ./fpdf_sysfontinfo.h: 19
FXFONT_SYMBOL_CHARSET = 2

# ./fpdf_sysfontinfo.h: 20
FXFONT_SHIFTJIS_CHARSET = 128

# ./fpdf_sysfontinfo.h: 21
FXFONT_HANGEUL_CHARSET = 129

# ./fpdf_sysfontinfo.h: 22
FXFONT_GB2312_CHARSET = 134

# ./fpdf_sysfontinfo.h: 23
FXFONT_CHINESEBIG5_CHARSET = 136

# ./fpdf_sysfontinfo.h: 24
FXFONT_GREEK_CHARSET = 161

# ./fpdf_sysfontinfo.h: 25
FXFONT_VIETNAMESE_CHARSET = 163

# ./fpdf_sysfontinfo.h: 26
FXFONT_HEBREW_CHARSET = 177

# ./fpdf_sysfontinfo.h: 27
FXFONT_ARABIC_CHARSET = 178

# ./fpdf_sysfontinfo.h: 28
FXFONT_CYRILLIC_CHARSET = 204

# ./fpdf_sysfontinfo.h: 29
FXFONT_THAI_CHARSET = 222

# ./fpdf_sysfontinfo.h: 30
FXFONT_EASTERNEUROPEAN_CHARSET = 238

# ./fpdf_sysfontinfo.h: 33
FXFONT_FF_FIXEDPITCH = (1 << 0)

# ./fpdf_sysfontinfo.h: 34
FXFONT_FF_ROMAN = (1 << 4)

# ./fpdf_sysfontinfo.h: 35
FXFONT_FF_SCRIPT = (4 << 4)

# ./fpdf_sysfontinfo.h: 38
FXFONT_FW_NORMAL = 400

# ./fpdf_sysfontinfo.h: 39
FXFONT_FW_BOLD = 700

# ./fpdf_text.h: 483
FPDF_MATCHCASE = 0x00000001

# ./fpdf_text.h: 485
FPDF_MATCHWHOLEWORD = 0x00000002

# ./fpdf_text.h: 487
FPDF_CONSECUTIVE = 0x00000004

# ./fpdf_edit.h: 93
FPDF_IMAGEOBJ_METADATA = struct_FPDF_IMAGEOBJ_METADATA

# -- End header members --
