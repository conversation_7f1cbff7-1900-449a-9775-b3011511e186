#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Emoji or Emojis are logograms, ideograms and smileys used in electronic messages and web pages.
The emoji's primary function is to fill in emotional cues otherwise missing from typed conversation.
Some examples of emojis are 😃, 🧘🏻‍♂️, 🌍, 🍞, 🚗, 📞, 🎉, ♥️, 🍆, and 🏁. Emojis exist in various genres,
including facial expressions, common objects, places and types of weather, and animals.
They are much like emoticons, but emojis are pictures rather than typographic approximations;
the term "emoji" in the strict sense refers to such pictures which can be represented as encoded characters,
but it is sometimes applied to messaging stickers by extension.
"""
import enum
import typing
from decimal import Decimal
import pathlib

from borb.pdf.canvas.geometry.rectangle import Rectangle
from borb.pdf.canvas.layout.image.image import Image
from borb.pdf.page.page import Page


class Emoji(Image):
    """
    Emoji or Emojis are logograms, ideograms and smileys used in electronic messages and web pages.
    The emoji's primary function is to fill in emotional cues otherwise missing from typed conversation.
    Some examples of emojis are 😃, 🧘🏻‍♂️, 🌍, 🍞, 🚗, 📞, 🎉, ♥️, 🍆, and 🏁. Emojis exist in various genres,
    including facial expressions, common objects, places and types of weather, and animals.
    They are much like emoticons, but emojis are pictures rather than typographic approximations;
    the term "emoji" in the strict sense refers to such pictures which can be represented as encoded characters,
    but it is sometimes applied to messaging stickers by extension.
    """

    #
    # CONSTRUCTOR
    #

    def __init__(self, path_to_resource: pathlib.Path):
        # Emoji explicitly does not support other LayoutElement properties
        # since emoji are considered to be at the character level, and borb
        # does not support LayoutElement properties on characters either.
        super(Emoji, self).__init__(path_to_resource)
        self.set_font_size(Decimal(12))
        self._margin_top = Decimal(0)
        self._margin_bottom = Decimal(0)
        self._fixed_leading: typing.Optional[Decimal] = None
        self._multiplied_leading: typing.Optional[Decimal] = Decimal(1.2)

    #
    # PRIVATE
    #

    def _get_content_box(self, available_space: Rectangle) -> Rectangle:
        # determine line_height
        assert self._font_size is not None
        assert self._multiplied_leading is not None or self._fixed_leading is not None
        line_height: Decimal = self._font_size
        if self._multiplied_leading is not None:
            line_height *= self._multiplied_leading
        if self._fixed_leading is not None:
            line_height += self._fixed_leading

        # return
        return Rectangle(
            available_space.get_x(),
            available_space.get_y() + available_space.get_height() - line_height,
            line_height,
            line_height,
        )

    def _paint_content_box(self, page: Page, available_space: Rectangle) -> None:
        super(Emoji, self)._paint_content_box(page, available_space)

    #
    # PUBLIC
    #

    def set_fixed_leading(self, fixed_leading: Decimal) -> "Emoji":
        """
        This function sets the fixed_leading of this Emoji
        :param fixed_leading:       the fixed_leading that will be used
        :return:                    self
        """
        self._multiplied_leading = None
        self._fixed_leading = fixed_leading
        return self

    def set_font_size(self, font_size: Decimal) -> "Emoji":
        """
        This function sets the font_size of this Emoji.
        This function returns self.
        :param font_size:   the font size
        :return:            self
        """
        assert font_size >= Decimal(0)
        self._font_size = font_size
        self._width = self._font_size
        self._height = self._font_size
        return self

    def set_multiplied_leading(self, multiplied_leading: Decimal) -> "Emoji":
        """
        This function sets the multiplied_leading of this Emoji
        :param multiplied_leading:  the multiplied_leading that will be used
        :return:                    self
        """
        self._fixed_leading = None
        self._multiplied_leading = multiplied_leading
        return self


class Emojis(enum.Enum):
    """
    This enum.Enum holds all available Emoji.
    """

    # fmt: off
    A = Emoji(pathlib.Path(__file__).parent / "resources/a.png")
    AB = Emoji(pathlib.Path(__file__).parent / "resources/ab.png")
    ABC = Emoji(pathlib.Path(__file__).parent / "resources/abc.png")
    ABCD = Emoji(pathlib.Path(__file__).parent / "resources/abcd.png")
    AERIAL_TRAMWAY = Emoji(pathlib.Path(__file__).parent / "resources/aerial_tramway.png")
    AIRPLANE = Emoji(pathlib.Path(__file__).parent / "resources/airplane.png")
    ALARM_CLOCK = Emoji(pathlib.Path(__file__).parent / "resources/alarm_clock.png")
    ALIEN = Emoji(pathlib.Path(__file__).parent / "resources/alien.png")
    AMBULANCE = Emoji(pathlib.Path(__file__).parent / "resources/ambulance.png")
    ANCHOR = Emoji(pathlib.Path(__file__).parent / "resources/anchor.png")
    ANGEL = Emoji(pathlib.Path(__file__).parent / "resources/angel.png")
    ANGER = Emoji(pathlib.Path(__file__).parent / "resources/anger.png")
    ANGRY = Emoji(pathlib.Path(__file__).parent / "resources/angry.png")
    ANGUISHED = Emoji(pathlib.Path(__file__).parent / "resources/anguished.png")
    ANT = Emoji(pathlib.Path(__file__).parent / "resources/ant.png")
    APPLE = Emoji(pathlib.Path(__file__).parent / "resources/apple.png")
    AQUARIUS = Emoji(pathlib.Path(__file__).parent / "resources/aquarius.png")
    ARIES = Emoji(pathlib.Path(__file__).parent / "resources/aries.png")
    ARROWS_CLOCKWISE = Emoji(pathlib.Path(__file__).parent / "resources/arrows_clockwise.png")
    ARROWS_COUNTERCLOCKWISE = Emoji(pathlib.Path(__file__).parent / "resources/arrows_counterclockwise.png")
    ARROWS_RIGHT_TWISTED = Emoji(pathlib.Path(__file__).parent / "resources/arrows_right_twisted.png")
    ARROW_BACKWARD = Emoji(pathlib.Path(__file__).parent / "resources/arrow_backward.png")
    ARROW_DOUBLE_DOWN = Emoji(pathlib.Path(__file__).parent / "resources/arrow_double_down.png")
    ARROW_DOUBLE_UP = Emoji(pathlib.Path(__file__).parent / "resources/arrow_double_up.png")
    ARROW_DOWN = Emoji(pathlib.Path(__file__).parent / "resources/arrow_down.png")
    ARROW_DOWN_HOOK = Emoji(pathlib.Path(__file__).parent / "resources/arrow_down_hook.png")
    ARROW_DOWN_SMALL = Emoji(pathlib.Path(__file__).parent / "resources/arrow_down_small.png")
    ARROW_FORWARD = Emoji(pathlib.Path(__file__).parent / "resources/arrow_forward.png")
    ARROW_LEFT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_left.png")
    ARROW_LEFT_HOOK = Emoji(pathlib.Path(__file__).parent / "resources/arrow_left_hook.png")
    ARROW_LEFT_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_left_right.png")
    ARROW_LOWER_LEFT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_lower_left.png")
    ARROW_LOWER_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_lower_right.png")
    ARROW_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_right.png")
    ARROW_RIGHT_HOOK = Emoji(pathlib.Path(__file__).parent / "resources/arrow_right_hook.png")
    ARROW_UP = Emoji(pathlib.Path(__file__).parent / "resources/arrow_up.png")
    ARROW_UPPER_LEFT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_upper_left.png")
    ARROW_UPPER_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/arrow_upper_right.png")
    ARROW_UP_DOWN = Emoji(pathlib.Path(__file__).parent / "resources/arrow_up_down.png")
    ARROW_UP_HOOK = Emoji(pathlib.Path(__file__).parent / "resources/arrow_up_hook.png")
    ARROW_UP_SMALL = Emoji(pathlib.Path(__file__).parent / "resources/arrow_up_small.png")
    ART = Emoji(pathlib.Path(__file__).parent / "resources/art.png")
    ARTICULATED_LORRY = Emoji(pathlib.Path(__file__).parent / "resources/articulated_lorry.png")
    ASTONISHED = Emoji(pathlib.Path(__file__).parent / "resources/astonished.png")
    ATM = Emoji(pathlib.Path(__file__).parent / "resources/atm.png")
    B = Emoji(pathlib.Path(__file__).parent / "resources/b.png")
    BABY = Emoji(pathlib.Path(__file__).parent / "resources/baby.png")
    BABY_BOTTLE = Emoji(pathlib.Path(__file__).parent / "resources/baby_bottle.png")
    BABY_CHICK = Emoji(pathlib.Path(__file__).parent / "resources/baby_chick.png")
    BABY_SYMBOL = Emoji(pathlib.Path(__file__).parent / "resources/baby_symbol.png")
    BAGAGE_CLAIM = Emoji(pathlib.Path(__file__).parent / "resources/bagage_claim.png")
    BALLOON = Emoji(pathlib.Path(__file__).parent / "resources/balloon.png")
    BALLOT_BOX_WITH_CHECK = Emoji(pathlib.Path(__file__).parent / "resources/ballot_box_with_check.png")
    BAMBOO = Emoji(pathlib.Path(__file__).parent / "resources/bamboo.png")
    BANANA = Emoji(pathlib.Path(__file__).parent / "resources/banana.png")
    BANG_BANG = Emoji(pathlib.Path(__file__).parent / "resources/bang_bang.png")
    BANK = Emoji(pathlib.Path(__file__).parent / "resources/bank.png")
    BARBER = Emoji(pathlib.Path(__file__).parent / "resources/barber.png")
    BAR_CHART = Emoji(pathlib.Path(__file__).parent / "resources/bar_chart.png")
    BASEBALL = Emoji(pathlib.Path(__file__).parent / "resources/baseball.png")
    BASKETBALL = Emoji(pathlib.Path(__file__).parent / "resources/basketball.png")
    BATH = Emoji(pathlib.Path(__file__).parent / "resources/bath.png")
    BATHTUB = Emoji(pathlib.Path(__file__).parent / "resources/bathtub.png")
    BATTERY = Emoji(pathlib.Path(__file__).parent / "resources/battery.png")
    BEAR = Emoji(pathlib.Path(__file__).parent / "resources/bear.png")
    BEER = Emoji(pathlib.Path(__file__).parent / "resources/beer.png")
    BEERS = Emoji(pathlib.Path(__file__).parent / "resources/beers.png")
    BEETLE = Emoji(pathlib.Path(__file__).parent / "resources/beetle.png")
    BEGINNER = Emoji(pathlib.Path(__file__).parent / "resources/beginner.png")
    BELL = Emoji(pathlib.Path(__file__).parent / "resources/bell.png")
    BENTO = Emoji(pathlib.Path(__file__).parent / "resources/bento.png")
    BICYCLIST = Emoji(pathlib.Path(__file__).parent / "resources/bicyclist.png")
    BIKE = Emoji(pathlib.Path(__file__).parent / "resources/bike.png")
    BIKINI = Emoji(pathlib.Path(__file__).parent / "resources/bikini.png")
    BIRD = Emoji(pathlib.Path(__file__).parent / "resources/bird.png")
    BIRTHDAY = Emoji(pathlib.Path(__file__).parent / "resources/birthday.png")
    BLACK_CIRCLE = Emoji(pathlib.Path(__file__).parent / "resources/black_circle.png")
    BLACK_JOKER = Emoji(pathlib.Path(__file__).parent / "resources/black_joker.png")
    BLACK_NIB = Emoji(pathlib.Path(__file__).parent / "resources/black_nib.png")
    BLACK_SQUARE_BUTTON = Emoji(pathlib.Path(__file__).parent / "resources/black_square_button.png")
    BLOSSOM = Emoji(pathlib.Path(__file__).parent / "resources/blossom.png")
    BLOWFISH = Emoji(pathlib.Path(__file__).parent / "resources/blowfish.png")
    BLUE_BOOK = Emoji(pathlib.Path(__file__).parent / "resources/blue_book.png")
    BLUE_CAR = Emoji(pathlib.Path(__file__).parent / "resources/blue_car.png")
    BLUE_CIRCLE = Emoji(pathlib.Path(__file__).parent / "resources/blue_circle.png")
    BLUE_DIAMOND = Emoji(pathlib.Path(__file__).parent / "resources/blue_diamond.png")
    BLUE_HEART = Emoji(pathlib.Path(__file__).parent / "resources/blue_heart.png")
    BLUSH = Emoji(pathlib.Path(__file__).parent / "resources/blush.png")
    BOAR = Emoji(pathlib.Path(__file__).parent / "resources/boar.png")
    BOAT = Emoji(pathlib.Path(__file__).parent / "resources/boat.png")
    BOMB = Emoji(pathlib.Path(__file__).parent / "resources/bomb.png")
    BOOK = Emoji(pathlib.Path(__file__).parent / "resources/book.png")
    BOOKMARK = Emoji(pathlib.Path(__file__).parent / "resources/bookmark.png")
    BOOKMARK_TABS = Emoji(pathlib.Path(__file__).parent / "resources/bookmark_tabs.png")
    BOOKS = Emoji(pathlib.Path(__file__).parent / "resources/books.png")
    BOOM = Emoji(pathlib.Path(__file__).parent / "resources/boom.png")
    BOOT = Emoji(pathlib.Path(__file__).parent / "resources/boot.png")
    BOUQUET = Emoji(pathlib.Path(__file__).parent / "resources/bouquet.png")
    BOW = Emoji(pathlib.Path(__file__).parent / "resources/bow.png")
    BOWLING = Emoji(pathlib.Path(__file__).parent / "resources/bowling.png")
    BOWTIE = Emoji(pathlib.Path(__file__).parent / "resources/bowtie.png")
    BOY = Emoji(pathlib.Path(__file__).parent / "resources/boy.png")
    BREAD = Emoji(pathlib.Path(__file__).parent / "resources/bread.png")
    BRIDE_WITH_VEIL = Emoji(pathlib.Path(__file__).parent / "resources/bride_with_veil.png")
    BRIDGE_AT_NIGHT = Emoji(pathlib.Path(__file__).parent / "resources/bridge_at_night.png")
    BRIEFCASE = Emoji(pathlib.Path(__file__).parent / "resources/briefcase.png")
    BROKEN_HEART = Emoji(pathlib.Path(__file__).parent / "resources/broken_heart.png")
    BUG = Emoji(pathlib.Path(__file__).parent / "resources/bug.png")
    BULB = Emoji(pathlib.Path(__file__).parent / "resources/bulb.png")
    BULLETTRAIN_FRONT = Emoji(pathlib.Path(__file__).parent / "resources/bullettrain_front.png")
    BULLETTRAIN_SIDE = Emoji(pathlib.Path(__file__).parent / "resources/bullettrain_side.png")
    BUS = Emoji(pathlib.Path(__file__).parent / "resources/bus.png")
    BUSSTOP = Emoji(pathlib.Path(__file__).parent / "resources/busstop.png")
    BUSTS_IN_SILHOUETTE = Emoji(pathlib.Path(__file__).parent / "resources/busts_in_silhouette.png")
    BUST_IN_SILHOUETTE = Emoji(pathlib.Path(__file__).parent / "resources/bust_in_silhouette.png")
    CACTUS = Emoji(pathlib.Path(__file__).parent / "resources/cactus.png")
    CAKE = Emoji(pathlib.Path(__file__).parent / "resources/cake.png")
    CALENDAR = Emoji(pathlib.Path(__file__).parent / "resources/calendar.png")
    CALLING = Emoji(pathlib.Path(__file__).parent / "resources/calling.png")
    CAMEL = Emoji(pathlib.Path(__file__).parent / "resources/camel.png")
    CAMERA = Emoji(pathlib.Path(__file__).parent / "resources/camera.png")
    CANCER = Emoji(pathlib.Path(__file__).parent / "resources/cancer.png")
    CANDY = Emoji(pathlib.Path(__file__).parent / "resources/candy.png")
    CAPITAL_ABCD = Emoji(pathlib.Path(__file__).parent / "resources/capital_abcd.png")
    CAPRICORN = Emoji(pathlib.Path(__file__).parent / "resources/capricorn.png")
    CAR = Emoji(pathlib.Path(__file__).parent / "resources/car.png")
    CARD_INDEX = Emoji(pathlib.Path(__file__).parent / "resources/card_index.png")
    CAROUSEL_HORSE = Emoji(pathlib.Path(__file__).parent / "resources/carousel_horse.png")
    CAT = Emoji(pathlib.Path(__file__).parent / "resources/cat.png")
    CAT_2 = Emoji(pathlib.Path(__file__).parent / "resources/cat_2.png")
    CD = Emoji(pathlib.Path(__file__).parent / "resources/cd.png")
    CHART = Emoji(pathlib.Path(__file__).parent / "resources/chart.png")
    CHART_WITH_DOWNWARDS_TREND = Emoji(pathlib.Path(__file__).parent / "resources/chart_with_downwards_trend.png")
    CHART_WITH_UPWARDS_TREND = Emoji(pathlib.Path(__file__).parent / "resources/chart_with_upwards_trend.png")
    CHECKERED_FLAG = Emoji(pathlib.Path(__file__).parent / "resources/checkered_flag.png")
    CHERRIES = Emoji(pathlib.Path(__file__).parent / "resources/cherries.png")
    CHERRY_BLOSSOM = Emoji(pathlib.Path(__file__).parent / "resources/cherry_blossom.png")
    CHESTNUT = Emoji(pathlib.Path(__file__).parent / "resources/chestnut.png")
    CHICKEN = Emoji(pathlib.Path(__file__).parent / "resources/chicken.png")
    CHILDREN_CROSSING = Emoji(pathlib.Path(__file__).parent / "resources/children_crossing.png")
    CHOCOLATE_BAR = Emoji(pathlib.Path(__file__).parent / "resources/chocolate_bar.png")
    CHRISTMAS_TREE = Emoji(pathlib.Path(__file__).parent / "resources/christmas_tree.png")
    CHURCH = Emoji(pathlib.Path(__file__).parent / "resources/church.png")
    CINEMA = Emoji(pathlib.Path(__file__).parent / "resources/cinema.png")
    CIRCUS_TENT = Emoji(pathlib.Path(__file__).parent / "resources/circus_tent.png")
    CITY_SUNRISE = Emoji(pathlib.Path(__file__).parent / "resources/city_sunrise.png")
    CITY_SUNSET = Emoji(pathlib.Path(__file__).parent / "resources/city_sunset.png")
    CL = Emoji(pathlib.Path(__file__).parent / "resources/cl.png")
    CLAP = Emoji(pathlib.Path(__file__).parent / "resources/clap.png")
    CLAPPER = Emoji(pathlib.Path(__file__).parent / "resources/clapper.png")
    CLIPBOARD = Emoji(pathlib.Path(__file__).parent / "resources/clipboard.png")
    CLOCK_1 = Emoji(pathlib.Path(__file__).parent / "resources/clock_1.png")
    CLOCK_10 = Emoji(pathlib.Path(__file__).parent / "resources/clock_10.png")
    CLOCK_10_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_10_30.png")
    CLOCK_11 = Emoji(pathlib.Path(__file__).parent / "resources/clock_11.png")
    CLOCK_11_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_11_30.png")
    CLOCK_12 = Emoji(pathlib.Path(__file__).parent / "resources/clock_12.png")
    CLOCK_12_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_12_30.png")
    CLOCK_1_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_1_30.png")
    CLOCK_2 = Emoji(pathlib.Path(__file__).parent / "resources/clock_2.png")
    CLOCK_2_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_2_30.png")
    CLOCK_3 = Emoji(pathlib.Path(__file__).parent / "resources/clock_3.png")
    CLOCK_3_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_3_30.png")
    CLOCK_4 = Emoji(pathlib.Path(__file__).parent / "resources/clock_4.png")
    CLOCK_4_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_4_30.png")
    CLOCK_5 = Emoji(pathlib.Path(__file__).parent / "resources/clock_5.png")
    CLOCK_5_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_5_30.png")
    CLOCK_6 = Emoji(pathlib.Path(__file__).parent / "resources/clock_6.png")
    CLOCK_6_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_6_30.png")
    CLOCK_7 = Emoji(pathlib.Path(__file__).parent / "resources/clock_7.png")
    CLOCK_7_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_7_30.png")
    CLOCK_8 = Emoji(pathlib.Path(__file__).parent / "resources/clock_8.png")
    CLOCK_8_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_8_30.png")
    CLOCK_9 = Emoji(pathlib.Path(__file__).parent / "resources/clock_9.png")
    CLOCK_9_30 = Emoji(pathlib.Path(__file__).parent / "resources/clock_9_30.png")
    CLOSED_BOOK = Emoji(pathlib.Path(__file__).parent / "resources/closed_book.png")
    CLOSED_LOCK_WITH_KEY = Emoji(pathlib.Path(__file__).parent / "resources/closed_lock_with_key.png")
    CLOSED_UMBRELLA = Emoji(pathlib.Path(__file__).parent / "resources/closed_umbrella.png")
    CLOUD = Emoji(pathlib.Path(__file__).parent / "resources/cloud.png")
    CLUBS = Emoji(pathlib.Path(__file__).parent / "resources/clubs.png")
    CN = Emoji(pathlib.Path(__file__).parent / "resources/cn.png")
    COCKTAIL = Emoji(pathlib.Path(__file__).parent / "resources/cocktail.png")
    COFFEE = Emoji(pathlib.Path(__file__).parent / "resources/coffee.png")
    COLLISION = Emoji(pathlib.Path(__file__).parent / "resources/collision.png")
    COMPUTER = Emoji(pathlib.Path(__file__).parent / "resources/computer.png")
    CONFETTI_BALL = Emoji(pathlib.Path(__file__).parent / "resources/confetti_ball.png")
    CONFOUNDED = Emoji(pathlib.Path(__file__).parent / "resources/confounded.png")
    CONFUSED = Emoji(pathlib.Path(__file__).parent / "resources/confused.png")
    CONSTRUCTION = Emoji(pathlib.Path(__file__).parent / "resources/construction.png")
    CONSTRUCTION_WORKER = Emoji(pathlib.Path(__file__).parent / "resources/construction_worker.png")
    CONVENIENCE_STORE = Emoji(pathlib.Path(__file__).parent / "resources/convenience_store.png")
    COOKIE = Emoji(pathlib.Path(__file__).parent / "resources/cookie.png")
    COOL = Emoji(pathlib.Path(__file__).parent / "resources/cool.png")
    COP = Emoji(pathlib.Path(__file__).parent / "resources/cop.png")
    COPYRIGHT = Emoji(pathlib.Path(__file__).parent / "resources/copyright.png")
    CORN = Emoji(pathlib.Path(__file__).parent / "resources/corn.png")
    COUPLE = Emoji(pathlib.Path(__file__).parent / "resources/couple.png")
    COUPLEKISS = Emoji(pathlib.Path(__file__).parent / "resources/couplekiss.png")
    COUPLE_WITH_HEART = Emoji(pathlib.Path(__file__).parent / "resources/couple_with_heart.png")
    COW = Emoji(pathlib.Path(__file__).parent / "resources/cow.png")
    COW_2 = Emoji(pathlib.Path(__file__).parent / "resources/cow_2.png")
    CREDIT_CARD = Emoji(pathlib.Path(__file__).parent / "resources/credit_card.png")
    CROCODILE = Emoji(pathlib.Path(__file__).parent / "resources/crocodile.png")
    CROSSED_FLAGS = Emoji(pathlib.Path(__file__).parent / "resources/crossed_flags.png")
    CROWN = Emoji(pathlib.Path(__file__).parent / "resources/crown.png")
    CRY = Emoji(pathlib.Path(__file__).parent / "resources/cry.png")
    CRYING_CAT_FACE = Emoji(pathlib.Path(__file__).parent / "resources/crying_cat_face.png")
    CRYSTAL_BALL = Emoji(pathlib.Path(__file__).parent / "resources/crystal_ball.png")
    CUPID = Emoji(pathlib.Path(__file__).parent / "resources/cupid.png")
    CURLY_LOOP = Emoji(pathlib.Path(__file__).parent / "resources/curly_loop.png")
    CURRENCY_EXCHANGE = Emoji(pathlib.Path(__file__).parent / "resources/currency_exchange.png")
    CURRY = Emoji(pathlib.Path(__file__).parent / "resources/curry.png")
    CUSTARD = Emoji(pathlib.Path(__file__).parent / "resources/custard.png")
    CUSTOMS = Emoji(pathlib.Path(__file__).parent / "resources/customs.png")
    CYCLONE = Emoji(pathlib.Path(__file__).parent / "resources/cyclone.png")
    DANCER = Emoji(pathlib.Path(__file__).parent / "resources/dancer.png")
    DANCERS = Emoji(pathlib.Path(__file__).parent / "resources/dancers.png")
    DANGO = Emoji(pathlib.Path(__file__).parent / "resources/dango.png")
    DART = Emoji(pathlib.Path(__file__).parent / "resources/dart.png")
    DASH = Emoji(pathlib.Path(__file__).parent / "resources/dash.png")
    DATE = Emoji(pathlib.Path(__file__).parent / "resources/date.png")
    DE = Emoji(pathlib.Path(__file__).parent / "resources/de.png")
    DECIDUOUS_TREE = Emoji(pathlib.Path(__file__).parent / "resources/deciduous_tree.png")
    DEPARTMENT_STORE = Emoji(pathlib.Path(__file__).parent / "resources/department_store.png")
    DIAMONDS = Emoji(pathlib.Path(__file__).parent / "resources/diamonds.png")
    DIAMOND_SHAPE_WITH_DOT_INSIDE = Emoji(pathlib.Path(__file__).parent / "resources/diamond_shape_with_dot_inside.png")
    DISAPPOINTED = Emoji(pathlib.Path(__file__).parent / "resources/disappointed.png")
    DISAPPOINTED_RELIEVED = Emoji(pathlib.Path(__file__).parent / "resources/disappointed_relieved.png")
    DIZZY = Emoji(pathlib.Path(__file__).parent / "resources/dizzy.png")
    DIZZY_FACE = Emoji(pathlib.Path(__file__).parent / "resources/dizzy_face.png")
    DOG = Emoji(pathlib.Path(__file__).parent / "resources/dog.png")
    DOG_2 = Emoji(pathlib.Path(__file__).parent / "resources/dog_2.png")
    DOLLAR = Emoji(pathlib.Path(__file__).parent / "resources/dollar.png")
    DOLLS = Emoji(pathlib.Path(__file__).parent / "resources/dolls.png")
    DOLPHIN = Emoji(pathlib.Path(__file__).parent / "resources/dolphin.png")
    DOOR = Emoji(pathlib.Path(__file__).parent / "resources/door.png")
    DOUGHNUT = Emoji(pathlib.Path(__file__).parent / "resources/doughnut.png")
    DO_NOT_LITTER = Emoji(pathlib.Path(__file__).parent / "resources/do_not_litter.png")
    DRAGON = Emoji(pathlib.Path(__file__).parent / "resources/dragon.png")
    DRAGON_FACE = Emoji(pathlib.Path(__file__).parent / "resources/dragon_face.png")
    DRESS = Emoji(pathlib.Path(__file__).parent / "resources/dress.png")
    DROMEDARY_CAMEL = Emoji(pathlib.Path(__file__).parent / "resources/dromedary_camel.png")
    DROPLET = Emoji(pathlib.Path(__file__).parent / "resources/droplet.png")
    DVD = Emoji(pathlib.Path(__file__).parent / "resources/dvd.png")
    EAR = Emoji(pathlib.Path(__file__).parent / "resources/ear.png")
    EARTH_AFRICA = Emoji(pathlib.Path(__file__).parent / "resources/earth_africa.png")
    EARTH_AMERICAS = Emoji(pathlib.Path(__file__).parent / "resources/earth_americas.png")
    EARTH_ASIA = Emoji(pathlib.Path(__file__).parent / "resources/earth_asia.png")
    EAR_OF_RICE = Emoji(pathlib.Path(__file__).parent / "resources/ear_of_rice.png")
    EGG = Emoji(pathlib.Path(__file__).parent / "resources/egg.png")
    EGGPLANT = Emoji(pathlib.Path(__file__).parent / "resources/eggplant.png")
    EIGHT = Emoji(pathlib.Path(__file__).parent / "resources/eight.png")
    EIGHT_POINTED_BLACK_STAR = Emoji(pathlib.Path(__file__).parent / "resources/eight_pointed_black_star.png")
    EIGHT_POINTED_BLUE_STAR = Emoji(pathlib.Path(__file__).parent / "resources/eight_pointed_blue_star.png")
    ELECTRIC_PLUG = Emoji(pathlib.Path(__file__).parent / "resources/electric_plug.png")
    ELEPHANT = Emoji(pathlib.Path(__file__).parent / "resources/elephant.png")
    EMAIL = Emoji(pathlib.Path(__file__).parent / "resources/email.png")
    END = Emoji(pathlib.Path(__file__).parent / "resources/end.png")
    ENVELOPE = Emoji(pathlib.Path(__file__).parent / "resources/envelope.png")
    ES = Emoji(pathlib.Path(__file__).parent / "resources/es.png")
    EURO = Emoji(pathlib.Path(__file__).parent / "resources/euro.png")
    EUROPEAN_CASTLE = Emoji(pathlib.Path(__file__).parent / "resources/european_castle.png")
    EUROPEAN_POST_OFFICE = Emoji(pathlib.Path(__file__).parent / "resources/european_post_office.png")
    EVERGREEN_TREE = Emoji(pathlib.Path(__file__).parent / "resources/evergreen_tree.png")
    EXCLAMATION = Emoji(pathlib.Path(__file__).parent / "resources/exclamation.png")
    EXPRESSIONLESS = Emoji(pathlib.Path(__file__).parent / "resources/expressionless.png")
    EYEGLASSES = Emoji(pathlib.Path(__file__).parent / "resources/eyeglasses.png")
    EYES = Emoji(pathlib.Path(__file__).parent / "resources/eyes.png")
    E_MAIL = Emoji(pathlib.Path(__file__).parent / "resources/e_mail.png")
    FACEPUNCH = Emoji(pathlib.Path(__file__).parent / "resources/facepunch.png")
    FACTORY = Emoji(pathlib.Path(__file__).parent / "resources/factory.png")
    FALLEN_LEAF = Emoji(pathlib.Path(__file__).parent / "resources/fallen_leaf.png")
    FAMILY = Emoji(pathlib.Path(__file__).parent / "resources/family.png")
    FAST_FORWARD = Emoji(pathlib.Path(__file__).parent / "resources/fast_forward.png")
    FAX = Emoji(pathlib.Path(__file__).parent / "resources/fax.png")
    FEARFUL = Emoji(pathlib.Path(__file__).parent / "resources/fearful.png")
    FEELSGOOD = Emoji(pathlib.Path(__file__).parent / "resources/feelsgood.png")
    FEET = Emoji(pathlib.Path(__file__).parent / "resources/feet.png")
    FERRIS_WHEEL = Emoji(pathlib.Path(__file__).parent / "resources/ferris_wheel.png")
    FILE_FOLDER = Emoji(pathlib.Path(__file__).parent / "resources/file_folder.png")
    FINNADIE = Emoji(pathlib.Path(__file__).parent / "resources/finnadie.png")
    FIRE = Emoji(pathlib.Path(__file__).parent / "resources/fire.png")
    FIREWORKS = Emoji(pathlib.Path(__file__).parent / "resources/fireworks.png")
    FIRE_ENGINE = Emoji(pathlib.Path(__file__).parent / "resources/fire_engine.png")
    FIRST_QUARTER_MOON = Emoji(pathlib.Path(__file__).parent / "resources/first_quarter_moon.png")
    FIRST_QUARTER_MOON_WITH_FACE = Emoji(pathlib.Path(__file__).parent / "resources/first_quarter_moon_with_face.png")
    FISH = Emoji(pathlib.Path(__file__).parent / "resources/fish.png")
    FISHING_POLE_AND_FISH = Emoji(pathlib.Path(__file__).parent / "resources/fishing_pole_and_fish.png")
    FISH_CAKE = Emoji(pathlib.Path(__file__).parent / "resources/fish_cake.png")
    FIST = Emoji(pathlib.Path(__file__).parent / "resources/fist.png")
    FIVE = Emoji(pathlib.Path(__file__).parent / "resources/five.png")
    FLAGS = Emoji(pathlib.Path(__file__).parent / "resources/flags.png")
    FLASHLIGHT = Emoji(pathlib.Path(__file__).parent / "resources/flashlight.png")
    FLOPPY_DISK = Emoji(pathlib.Path(__file__).parent / "resources/floppy_disk.png")
    FLOWER_PLAYING_CARDS = Emoji(pathlib.Path(__file__).parent / "resources/flower_playing_cards.png")
    FLUSHED = Emoji(pathlib.Path(__file__).parent / "resources/flushed.png")
    FOGGY = Emoji(pathlib.Path(__file__).parent / "resources/foggy.png")
    FOOTBALL = Emoji(pathlib.Path(__file__).parent / "resources/football.png")
    FOUNTAIN = Emoji(pathlib.Path(__file__).parent / "resources/fountain.png")
    FOUR = Emoji(pathlib.Path(__file__).parent / "resources/four.png")
    FOUR_LEAF_CLOVER = Emoji(pathlib.Path(__file__).parent / "resources/four_leaf_clover.png")
    FR = Emoji(pathlib.Path(__file__).parent / "resources/fr.png")
    FREE = Emoji(pathlib.Path(__file__).parent / "resources/free.png")
    FRIED_SHRIMP = Emoji(pathlib.Path(__file__).parent / "resources/fried_shrimp.png")
    FRIES = Emoji(pathlib.Path(__file__).parent / "resources/fries.png")
    FROG = Emoji(pathlib.Path(__file__).parent / "resources/frog.png")
    FROWNING = Emoji(pathlib.Path(__file__).parent / "resources/frowning.png")
    FU = Emoji(pathlib.Path(__file__).parent / "resources/fu.png")
    FUELPUMP = Emoji(pathlib.Path(__file__).parent / "resources/fuelpump.png")
    FULL_MOON = Emoji(pathlib.Path(__file__).parent / "resources/full_moon.png")
    FULL_MOON_WITH_FACE = Emoji(pathlib.Path(__file__).parent / "resources/full_moon_with_face.png")
    GAME_DIE = Emoji(pathlib.Path(__file__).parent / "resources/game_die.png")
    GB = Emoji(pathlib.Path(__file__).parent / "resources/gb.png")
    GEM = Emoji(pathlib.Path(__file__).parent / "resources/gem.png")
    GEMINI = Emoji(pathlib.Path(__file__).parent / "resources/gemini.png")
    GHOST = Emoji(pathlib.Path(__file__).parent / "resources/ghost.png")
    GIFT = Emoji(pathlib.Path(__file__).parent / "resources/gift.png")
    GIFT_HEART = Emoji(pathlib.Path(__file__).parent / "resources/gift_heart.png")
    GIRL = Emoji(pathlib.Path(__file__).parent / "resources/girl.png")
    GLOBE_WITH_MERIDIANS = Emoji(pathlib.Path(__file__).parent / "resources/globe_with_meridians.png")
    GOAT = Emoji(pathlib.Path(__file__).parent / "resources/goat.png")
    GOBERSERK = Emoji(pathlib.Path(__file__).parent / "resources/goberserk.png")
    GODMODE = Emoji(pathlib.Path(__file__).parent / "resources/godmode.png")
    GOLF = Emoji(pathlib.Path(__file__).parent / "resources/golf.png")
    GRAPES = Emoji(pathlib.Path(__file__).parent / "resources/grapes.png")
    GREEN_APPLE = Emoji(pathlib.Path(__file__).parent / "resources/green_apple.png")
    GREEN_BOOK = Emoji(pathlib.Path(__file__).parent / "resources/green_book.png")
    GREEN_HEART = Emoji(pathlib.Path(__file__).parent / "resources/green_heart.png")
    GREY_EXCLAMATION = Emoji(pathlib.Path(__file__).parent / "resources/grey_exclamation.png")
    GREY_QUESTION = Emoji(pathlib.Path(__file__).parent / "resources/grey_question.png")
    GRIMACING = Emoji(pathlib.Path(__file__).parent / "resources/grimacing.png")
    GRIN = Emoji(pathlib.Path(__file__).parent / "resources/grin.png")
    GRINNING = Emoji(pathlib.Path(__file__).parent / "resources/grinning.png")
    GUARDSMAN = Emoji(pathlib.Path(__file__).parent / "resources/guardsman.png")
    GUITAR = Emoji(pathlib.Path(__file__).parent / "resources/guitar.png")
    GUN = Emoji(pathlib.Path(__file__).parent / "resources/gun.png")
    HAIRCUT = Emoji(pathlib.Path(__file__).parent / "resources/haircut.png")
    HAMBURGER = Emoji(pathlib.Path(__file__).parent / "resources/hamburger.png")
    HAMMER = Emoji(pathlib.Path(__file__).parent / "resources/hammer.png")
    HAMSTER = Emoji(pathlib.Path(__file__).parent / "resources/hamster.png")
    HAND = Emoji(pathlib.Path(__file__).parent / "resources/hand.png")
    HANDBAG = Emoji(pathlib.Path(__file__).parent / "resources/handbag.png")
    HANKEY = Emoji(pathlib.Path(__file__).parent / "resources/hankey.png")
    HASH = Emoji(pathlib.Path(__file__).parent / "resources/hash.png")
    HATCHED_CHICK = Emoji(pathlib.Path(__file__).parent / "resources/hatched_chick.png")
    HATCHING_CHICK = Emoji(pathlib.Path(__file__).parent / "resources/hatching_chick.png")
    HEADPHONES = Emoji(pathlib.Path(__file__).parent / "resources/headphones.png")
    HEART = Emoji(pathlib.Path(__file__).parent / "resources/heart.png")
    HEARTBEAT = Emoji(pathlib.Path(__file__).parent / "resources/heartbeat.png")
    HEARTPULSE = Emoji(pathlib.Path(__file__).parent / "resources/heartpulse.png")
    HEARTS = Emoji(pathlib.Path(__file__).parent / "resources/hearts.png")
    HEART_DECORATION = Emoji(pathlib.Path(__file__).parent / "resources/heart_decoration.png")
    HEART_EYES = Emoji(pathlib.Path(__file__).parent / "resources/heart_eyes.png")
    HEART_EYES_CAT = Emoji(pathlib.Path(__file__).parent / "resources/heart_eyes_cat.png")
    HEAR_NO_EVIL = Emoji(pathlib.Path(__file__).parent / "resources/hear_no_evil.png")
    HEAVY_CHECK_MARK = Emoji(pathlib.Path(__file__).parent / "resources/heavy_check_mark.png")
    HEAVY_DIVISION_SIGN = Emoji(pathlib.Path(__file__).parent / "resources/heavy_division_sign.png")
    HEAVY_DOLLAR_SIGN = Emoji(pathlib.Path(__file__).parent / "resources/heavy_dollar_sign.png")
    HEAVY_EXCLAMATION_MARK = Emoji(pathlib.Path(__file__).parent / "resources/heavy_exclamation_mark.png")
    HEAVY_MINUS_SIGN = Emoji(pathlib.Path(__file__).parent / "resources/heavy_minus_sign.png")
    HEAVY_MULTIPLICATION = Emoji(pathlib.Path(__file__).parent / "resources/heavy_multiplication.png")
    HEAVY_PLUS_SIGN = Emoji(pathlib.Path(__file__).parent / "resources/heavy_plus_sign.png")
    HELICOPTER = Emoji(pathlib.Path(__file__).parent / "resources/helicopter.png")
    HERB = Emoji(pathlib.Path(__file__).parent / "resources/herb.png")
    HIBISCUS = Emoji(pathlib.Path(__file__).parent / "resources/hibiscus.png")
    HIGH_BRIGHTNESS = Emoji(pathlib.Path(__file__).parent / "resources/high_brightness.png")
    HIGH_HEEL = Emoji(pathlib.Path(__file__).parent / "resources/high_heel.png")
    HOCHO = Emoji(pathlib.Path(__file__).parent / "resources/hocho.png")
    HONEYBEE = Emoji(pathlib.Path(__file__).parent / "resources/honeybee.png")
    HONEY_POT = Emoji(pathlib.Path(__file__).parent / "resources/honey_pot.png")
    HORSE = Emoji(pathlib.Path(__file__).parent / "resources/horse.png")
    HORSE_RACING = Emoji(pathlib.Path(__file__).parent / "resources/horse_racing.png")
    HOSPITAL = Emoji(pathlib.Path(__file__).parent / "resources/hospital.png")
    HOTEL = Emoji(pathlib.Path(__file__).parent / "resources/hotel.png")
    HOTSPRINGS = Emoji(pathlib.Path(__file__).parent / "resources/hotsprings.png")
    HOURGLASS = Emoji(pathlib.Path(__file__).parent / "resources/hourglass.png")
    HOURGLASS_FLOWING_SAND = Emoji(pathlib.Path(__file__).parent / "resources/hourglass_flowing_sand.png")
    HOUSE = Emoji(pathlib.Path(__file__).parent / "resources/house.png")
    HOUSE_WITH_GARDEN = Emoji(pathlib.Path(__file__).parent / "resources/house_with_garden.png")
    HUNDRED = Emoji(pathlib.Path(__file__).parent / "resources/hundred.png")
    HURTREALBAD = Emoji(pathlib.Path(__file__).parent / "resources/hurtrealbad.png")
    HUSHED = Emoji(pathlib.Path(__file__).parent / "resources/hushed.png")
    ICECREAM = Emoji(pathlib.Path(__file__).parent / "resources/icecream.png")
    ICE_CREAM = Emoji(pathlib.Path(__file__).parent / "resources/ice_cream.png")
    ID = Emoji(pathlib.Path(__file__).parent / "resources/id.png")
    IMP = Emoji(pathlib.Path(__file__).parent / "resources/imp.png")
    INBOX_TRAY = Emoji(pathlib.Path(__file__).parent / "resources/inbox_tray.png")
    INCOMING_ENVELOPE = Emoji(pathlib.Path(__file__).parent / "resources/incoming_envelope.png")
    INFORMATION_DESK_PERSON = Emoji(pathlib.Path(__file__).parent / "resources/information_desk_person.png")
    INFORMATION_SOURCE = Emoji(pathlib.Path(__file__).parent / "resources/information_source.png")
    INNOCENT = Emoji(pathlib.Path(__file__).parent / "resources/innocent.png")
    INTERROBANG = Emoji(pathlib.Path(__file__).parent / "resources/interrobang.png")
    IPHONE = Emoji(pathlib.Path(__file__).parent / "resources/iphone.png")
    IT = Emoji(pathlib.Path(__file__).parent / "resources/it.png")
    IZAKAYA_LANTERN = Emoji(pathlib.Path(__file__).parent / "resources/izakaya_lantern.png")
    JACK_O_LANTERN = Emoji(pathlib.Path(__file__).parent / "resources/jack_o_lantern.png")
    JAPAN = Emoji(pathlib.Path(__file__).parent / "resources/japan.png")
    JAPANESE_CASTLE = Emoji(pathlib.Path(__file__).parent / "resources/japanese_castle.png")
    JAPANESE_GOBLIN = Emoji(pathlib.Path(__file__).parent / "resources/japanese_goblin.png")
    JAPANESE_OGRE = Emoji(pathlib.Path(__file__).parent / "resources/japanese_ogre.png")
    JEANS = Emoji(pathlib.Path(__file__).parent / "resources/jeans.png")
    JOY = Emoji(pathlib.Path(__file__).parent / "resources/joy.png")
    JOY_CAT = Emoji(pathlib.Path(__file__).parent / "resources/joy_cat.png")
    JP = Emoji(pathlib.Path(__file__).parent / "resources/jp.png")
    KEY = Emoji(pathlib.Path(__file__).parent / "resources/key.png")
    KEYCAP_TEN = Emoji(pathlib.Path(__file__).parent / "resources/keycap_ten.png")
    KIMONO = Emoji(pathlib.Path(__file__).parent / "resources/kimono.png")
    KISS = Emoji(pathlib.Path(__file__).parent / "resources/kiss.png")
    KISSING = Emoji(pathlib.Path(__file__).parent / "resources/kissing.png")
    KISSING_CAT = Emoji(pathlib.Path(__file__).parent / "resources/kissing_cat.png")
    KISSING_CLOSED_EYES = Emoji(pathlib.Path(__file__).parent / "resources/kissing_closed_eyes.png")
    KISSING_HEART = Emoji(pathlib.Path(__file__).parent / "resources/kissing_heart.png")
    KISSING_SMILING_EYES = Emoji(pathlib.Path(__file__).parent / "resources/kissing_smiling_eyes.png")
    KNIFE_AND_FORK = Emoji(pathlib.Path(__file__).parent / "resources/knife_and_fork.png")
    KOALA = Emoji(pathlib.Path(__file__).parent / "resources/koala.png")
    KOKO = Emoji(pathlib.Path(__file__).parent / "resources/koko.png")
    KR = Emoji(pathlib.Path(__file__).parent / "resources/kr.png")
    LAST_QUARTER_MOON = Emoji(pathlib.Path(__file__).parent / "resources/last_quarter_moon.png")
    LAST_QUARTER_MOON_WITH_FACE = Emoji(pathlib.Path(__file__).parent / "resources/last_quarter_moon_with_face.png")
    LAUGHING = Emoji(pathlib.Path(__file__).parent / "resources/laughing.png")
    LEAVES = Emoji(pathlib.Path(__file__).parent / "resources/leaves.png")
    LEDGER = Emoji(pathlib.Path(__file__).parent / "resources/ledger.png")
    LEFT_LUGGAGE = Emoji(pathlib.Path(__file__).parent / "resources/left_luggage.png")
    LEMON = Emoji(pathlib.Path(__file__).parent / "resources/lemon.png")
    LEO = Emoji(pathlib.Path(__file__).parent / "resources/leo.png")
    LEOPARD = Emoji(pathlib.Path(__file__).parent / "resources/leopard.png")
    LIBRA = Emoji(pathlib.Path(__file__).parent / "resources/libra.png")
    LIGHT_RAIL = Emoji(pathlib.Path(__file__).parent / "resources/light_rail.png")
    LINK = Emoji(pathlib.Path(__file__).parent / "resources/link.png")
    LIPS = Emoji(pathlib.Path(__file__).parent / "resources/lips.png")
    LIPSTICK = Emoji(pathlib.Path(__file__).parent / "resources/lipstick.png")
    LOCK = Emoji(pathlib.Path(__file__).parent / "resources/lock.png")
    LOCK_WITH_INK_PEN = Emoji(pathlib.Path(__file__).parent / "resources/lock_with_ink_pen.png")
    LOLLIPOP = Emoji(pathlib.Path(__file__).parent / "resources/lollipop.png")
    LOOP = Emoji(pathlib.Path(__file__).parent / "resources/loop.png")
    LOUDSPEAKER = Emoji(pathlib.Path(__file__).parent / "resources/loudspeaker.png")
    LOVE_HOTEL = Emoji(pathlib.Path(__file__).parent / "resources/love_hotel.png")
    LOVE_LETTER = Emoji(pathlib.Path(__file__).parent / "resources/love_letter.png")
    LOW_BRIGHTNESS = Emoji(pathlib.Path(__file__).parent / "resources/low_brightness.png")
    M = Emoji(pathlib.Path(__file__).parent / "resources/m.png")
    MAG = Emoji(pathlib.Path(__file__).parent / "resources/mag.png")
    MAGIC_8_BALL = Emoji(pathlib.Path(__file__).parent / "resources/magic_8_ball.png")
    MAG_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/mag_right.png")
    MAHJONG = Emoji(pathlib.Path(__file__).parent / "resources/mahjong.png")
    MAILBOX = Emoji(pathlib.Path(__file__).parent / "resources/mailbox.png")
    MAILBOX_CLOSED = Emoji(pathlib.Path(__file__).parent / "resources/mailbox_closed.png")
    MAILBOX_WITH_MAIL = Emoji(pathlib.Path(__file__).parent / "resources/mailbox_with_mail.png")
    MAILBOX_WITH_NO_MAIL = Emoji(pathlib.Path(__file__).parent / "resources/mailbox_with_no_mail.png")
    MAN = Emoji(pathlib.Path(__file__).parent / "resources/man.png")
    MANS_SHOE = Emoji(pathlib.Path(__file__).parent / "resources/mans_shoe.png")
    MAN_WITH_GUA_PI_MAO = Emoji(pathlib.Path(__file__).parent / "resources/man_with_gua_pi_mao.png")
    MAN_WITH_TURBAN = Emoji(pathlib.Path(__file__).parent / "resources/man_with_turban.png")
    MAPLE_LEAF = Emoji(pathlib.Path(__file__).parent / "resources/maple_leaf.png")
    MASK = Emoji(pathlib.Path(__file__).parent / "resources/mask.png")
    MASSAGE = Emoji(pathlib.Path(__file__).parent / "resources/massage.png")
    MEAT_ON_BONE = Emoji(pathlib.Path(__file__).parent / "resources/meat_on_bone.png")
    MEGA = Emoji(pathlib.Path(__file__).parent / "resources/mega.png")
    MELON = Emoji(pathlib.Path(__file__).parent / "resources/melon.png")
    MEMO = Emoji(pathlib.Path(__file__).parent / "resources/memo.png")
    MENS = Emoji(pathlib.Path(__file__).parent / "resources/mens.png")
    METAL = Emoji(pathlib.Path(__file__).parent / "resources/metal.png")
    MICROPHONE = Emoji(pathlib.Path(__file__).parent / "resources/microphone.png")
    MICROSCOPE = Emoji(pathlib.Path(__file__).parent / "resources/microscope.png")
    MILKY_WAY = Emoji(pathlib.Path(__file__).parent / "resources/milky_way.png")
    MINIBUS = Emoji(pathlib.Path(__file__).parent / "resources/minibus.png")
    MINIDISC = Emoji(pathlib.Path(__file__).parent / "resources/minidisc.png")
    MINUS_1 = Emoji(pathlib.Path(__file__).parent / "resources/minus_1.png")
    MOBILE_PHONE_OFF = Emoji(pathlib.Path(__file__).parent / "resources/mobile_phone_off.png")
    MONEYBAG = Emoji(pathlib.Path(__file__).parent / "resources/moneybag.png")
    MONEY_WITH_WINGS = Emoji(pathlib.Path(__file__).parent / "resources/money_with_wings.png")
    MONKEY = Emoji(pathlib.Path(__file__).parent / "resources/monkey.png")
    MONKEY_FACE = Emoji(pathlib.Path(__file__).parent / "resources/monkey_face.png")
    MONORAIL = Emoji(pathlib.Path(__file__).parent / "resources/monorail.png")
    MOON = Emoji(pathlib.Path(__file__).parent / "resources/moon.png")
    MORTAR_BOARD = Emoji(pathlib.Path(__file__).parent / "resources/mortar_board.png")
    MOUNTAIN_BICYCLIST = Emoji(pathlib.Path(__file__).parent / "resources/mountain_bicyclist.png")
    MOUNTAIN_CABLEWAY = Emoji(pathlib.Path(__file__).parent / "resources/mountain_cableway.png")
    MOUNTAIN_RAILWAY = Emoji(pathlib.Path(__file__).parent / "resources/mountain_railway.png")
    MOUNT_FUJI = Emoji(pathlib.Path(__file__).parent / "resources/mount_fuji.png")
    MOUSE = Emoji(pathlib.Path(__file__).parent / "resources/mouse.png")
    MOUSE_2 = Emoji(pathlib.Path(__file__).parent / "resources/mouse_2.png")
    MOVIE_CAMERA = Emoji(pathlib.Path(__file__).parent / "resources/movie_camera.png")
    MOYAI = Emoji(pathlib.Path(__file__).parent / "resources/moyai.png")
    MUSCLE = Emoji(pathlib.Path(__file__).parent / "resources/muscle.png")
    MUSHROOM = Emoji(pathlib.Path(__file__).parent / "resources/mushroom.png")
    MUSICAL_KEYBOARD = Emoji(pathlib.Path(__file__).parent / "resources/musical_keyboard.png")
    MUSICAL_NOTE = Emoji(pathlib.Path(__file__).parent / "resources/musical_note.png")
    MUSICAL_SCORE = Emoji(pathlib.Path(__file__).parent / "resources/musical_score.png")
    MUTE = Emoji(pathlib.Path(__file__).parent / "resources/mute.png")
    NAIL_CARE = Emoji(pathlib.Path(__file__).parent / "resources/nail_care.png")
    NAME_BADGE = Emoji(pathlib.Path(__file__).parent / "resources/name_badge.png")
    NECKBEARD = Emoji(pathlib.Path(__file__).parent / "resources/neckbeard.png")
    NECKTIE = Emoji(pathlib.Path(__file__).parent / "resources/necktie.png")
    NEGATIVE_SQUARED_CROSS_MARK = Emoji(pathlib.Path(__file__).parent / "resources/negative_squared_cross_mark.png")
    NEUTRAL_FACE = Emoji(pathlib.Path(__file__).parent / "resources/neutral_face.png")
    NEW = Emoji(pathlib.Path(__file__).parent / "resources/new.png")
    NEWSPAPER = Emoji(pathlib.Path(__file__).parent / "resources/newspaper.png")
    NEW_MOON = Emoji(pathlib.Path(__file__).parent / "resources/new_moon.png")
    NEW_MOON_WITH_FACE = Emoji(pathlib.Path(__file__).parent / "resources/new_moon_with_face.png")
    NG = Emoji(pathlib.Path(__file__).parent / "resources/ng.png")
    NINE = Emoji(pathlib.Path(__file__).parent / "resources/nine.png")
    NON_POTABLE_WATER = Emoji(pathlib.Path(__file__).parent / "resources/non_potable_water.png")
    NOSE = Emoji(pathlib.Path(__file__).parent / "resources/nose.png")
    NOTEBOOK = Emoji(pathlib.Path(__file__).parent / "resources/notebook.png")
    NOTEBOOK_WITH_DECORATIVE_COVER = Emoji(pathlib.Path(__file__).parent / "resources/notebook_with_decorative_cover.png")
    NOTES = Emoji(pathlib.Path(__file__).parent / "resources/notes.png")
    NO_BELL = Emoji(pathlib.Path(__file__).parent / "resources/no_bell.png")
    NO_BICYCLES = Emoji(pathlib.Path(__file__).parent / "resources/no_bicycles.png")
    NO_ENTRY = Emoji(pathlib.Path(__file__).parent / "resources/no_entry.png")
    NO_GOOD = Emoji(pathlib.Path(__file__).parent / "resources/no_good.png")
    NO_MOBILE_PHONES = Emoji(pathlib.Path(__file__).parent / "resources/no_mobile_phones.png")
    NO_MOUTH = Emoji(pathlib.Path(__file__).parent / "resources/no_mouth.png")
    NO_PEDESTRIANS = Emoji(pathlib.Path(__file__).parent / "resources/no_pedestrians.png")
    NO_SMOKING = Emoji(pathlib.Path(__file__).parent / "resources/no_smoking.png")
    NUT_AND_BOLT = Emoji(pathlib.Path(__file__).parent / "resources/nut_and_bolt.png")
    O = Emoji(pathlib.Path(__file__).parent / "resources/o.png")
    OCEAN = Emoji(pathlib.Path(__file__).parent / "resources/ocean.png")
    OCTOCAT = Emoji(pathlib.Path(__file__).parent / "resources/octocat.png")
    OCTOPUS = Emoji(pathlib.Path(__file__).parent / "resources/octopus.png")
    ODEN = Emoji(pathlib.Path(__file__).parent / "resources/oden.png")
    OFFICE = Emoji(pathlib.Path(__file__).parent / "resources/office.png")
    OK = Emoji(pathlib.Path(__file__).parent / "resources/ok.png")
    OK_HAND = Emoji(pathlib.Path(__file__).parent / "resources/ok_hand.png")
    OK_WOMAN = Emoji(pathlib.Path(__file__).parent / "resources/ok_woman.png")
    OLDER_MAN = Emoji(pathlib.Path(__file__).parent / "resources/older_man.png")
    OLDER_WOMAN = Emoji(pathlib.Path(__file__).parent / "resources/older_woman.png")
    ON = Emoji(pathlib.Path(__file__).parent / "resources/on.png")
    ONCOMING_AUTOMOBILE = Emoji(pathlib.Path(__file__).parent / "resources/oncoming_automobile.png")
    ONCOMING_BUS = Emoji(pathlib.Path(__file__).parent / "resources/oncoming_bus.png")
    ONCOMING_POLICE_CAR = Emoji(pathlib.Path(__file__).parent / "resources/oncoming_police_car.png")
    ONCOMING_TAXI = Emoji(pathlib.Path(__file__).parent / "resources/oncoming_taxi.png")
    ONE = Emoji(pathlib.Path(__file__).parent / "resources/one.png")
    ONE_TWO_THREE_FOUR = Emoji(pathlib.Path(__file__).parent / "resources/one_two_three_four.png")
    OPEN_FILE_FOLDER = Emoji(pathlib.Path(__file__).parent / "resources/open_file_folder.png")
    OPEN_HANDS = Emoji(pathlib.Path(__file__).parent / "resources/open_hands.png")
    OPEN_MOUTH = Emoji(pathlib.Path(__file__).parent / "resources/open_mouth.png")
    OPHIUCHUS = Emoji(pathlib.Path(__file__).parent / "resources/ophiuchus.png")
    ORANGE_BOOK = Emoji(pathlib.Path(__file__).parent / "resources/orange_book.png")
    ORANGE_DIAMOND = Emoji(pathlib.Path(__file__).parent / "resources/orange_diamond.png")
    OUTBOX_TRAY = Emoji(pathlib.Path(__file__).parent / "resources/outbox_tray.png")
    OX = Emoji(pathlib.Path(__file__).parent / "resources/ox.png")
    PAGER = Emoji(pathlib.Path(__file__).parent / "resources/pager.png")
    PAGE_FACING_UP = Emoji(pathlib.Path(__file__).parent / "resources/page_facing_up.png")
    PAGE_WITH_CURL = Emoji(pathlib.Path(__file__).parent / "resources/page_with_curl.png")
    PALM_TREE = Emoji(pathlib.Path(__file__).parent / "resources/palm_tree.png")
    PANDA_FACE = Emoji(pathlib.Path(__file__).parent / "resources/panda_face.png")
    PAPERCLIP = Emoji(pathlib.Path(__file__).parent / "resources/paperclip.png")
    PARKING = Emoji(pathlib.Path(__file__).parent / "resources/parking.png")
    PARTLY_SUNNY = Emoji(pathlib.Path(__file__).parent / "resources/partly_sunny.png")
    PART_ALTERNATION_MARK = Emoji(pathlib.Path(__file__).parent / "resources/part_alternation_mark.png")
    PASSPORT_CONTROL = Emoji(pathlib.Path(__file__).parent / "resources/passport_control.png")
    PAW_PRINTS = Emoji(pathlib.Path(__file__).parent / "resources/paw_prints.png")
    PEACH = Emoji(pathlib.Path(__file__).parent / "resources/peach.png")
    PEAR = Emoji(pathlib.Path(__file__).parent / "resources/pear.png")
    PENCIL = Emoji(pathlib.Path(__file__).parent / "resources/pencil.png")
    PENCIL_2 = Emoji(pathlib.Path(__file__).parent / "resources/pencil_2.png")
    PENGUIN = Emoji(pathlib.Path(__file__).parent / "resources/penguin.png")
    PENSIVE = Emoji(pathlib.Path(__file__).parent / "resources/pensive.png")
    PERFORMING_ARTS = Emoji(pathlib.Path(__file__).parent / "resources/performing_arts.png")
    PERSEVERE = Emoji(pathlib.Path(__file__).parent / "resources/persevere.png")
    PHONE = Emoji(pathlib.Path(__file__).parent / "resources/phone.png")
    PIG = Emoji(pathlib.Path(__file__).parent / "resources/pig.png")
    PIG_2 = Emoji(pathlib.Path(__file__).parent / "resources/pig_2.png")
    PIG_NOSE = Emoji(pathlib.Path(__file__).parent / "resources/pig_nose.png")
    PILL = Emoji(pathlib.Path(__file__).parent / "resources/pill.png")
    PINEAPPLE = Emoji(pathlib.Path(__file__).parent / "resources/pineapple.png")
    PISCES = Emoji(pathlib.Path(__file__).parent / "resources/pisces.png")
    PIZZA = Emoji(pathlib.Path(__file__).parent / "resources/pizza.png")
    PLUS_1 = Emoji(pathlib.Path(__file__).parent / "resources/plus_1.png")
    POINT_DOWN = Emoji(pathlib.Path(__file__).parent / "resources/point_down.png")
    POINT_LEFT = Emoji(pathlib.Path(__file__).parent / "resources/point_left.png")
    POINT_RIGHT = Emoji(pathlib.Path(__file__).parent / "resources/point_right.png")
    POINT_UP = Emoji(pathlib.Path(__file__).parent / "resources/point_up.png")
    POINT_UP_2 = Emoji(pathlib.Path(__file__).parent / "resources/point_up_2.png")
    POLICE_CAR = Emoji(pathlib.Path(__file__).parent / "resources/police_car.png")
    POODLE = Emoji(pathlib.Path(__file__).parent / "resources/poodle.png")
    POOP = Emoji(pathlib.Path(__file__).parent / "resources/poop.png")
    POSTAL_HORN = Emoji(pathlib.Path(__file__).parent / "resources/postal_horn.png")
    POSTBOX = Emoji(pathlib.Path(__file__).parent / "resources/postbox.png")
    POST_OFFICE = Emoji(pathlib.Path(__file__).parent / "resources/post_office.png")
    POTABLE_WATER = Emoji(pathlib.Path(__file__).parent / "resources/potable_water.png")
    POUCH = Emoji(pathlib.Path(__file__).parent / "resources/pouch.png")
    POULTRY_LEG = Emoji(pathlib.Path(__file__).parent / "resources/poultry_leg.png")
    POUND = Emoji(pathlib.Path(__file__).parent / "resources/pound.png")
    POUTING_CAT = Emoji(pathlib.Path(__file__).parent / "resources/pouting_cat.png")
    PRAY = Emoji(pathlib.Path(__file__).parent / "resources/pray.png")
    PRINCESS = Emoji(pathlib.Path(__file__).parent / "resources/princess.png")
    PUNCH = Emoji(pathlib.Path(__file__).parent / "resources/punch.png")
    PURPLE_HEART = Emoji(pathlib.Path(__file__).parent / "resources/purple_heart.png")
    PURSE = Emoji(pathlib.Path(__file__).parent / "resources/purse.png")
    PUSHPIN = Emoji(pathlib.Path(__file__).parent / "resources/pushpin.png")
    PUT_LITTER_IN_ITS_PLACE = Emoji(pathlib.Path(__file__).parent / "resources/put_litter_in_its_place.png")
    QUESTION = Emoji(pathlib.Path(__file__).parent / "resources/question.png")
    RABBIT = Emoji(pathlib.Path(__file__).parent / "resources/rabbit.png")
    RABBIT_2 = Emoji(pathlib.Path(__file__).parent / "resources/rabbit_2.png")
    RACEHORSE = Emoji(pathlib.Path(__file__).parent / "resources/racehorse.png")
    RADIO = Emoji(pathlib.Path(__file__).parent / "resources/radio.png")
    RADIO_BUTTON = Emoji(pathlib.Path(__file__).parent / "resources/radio_button.png")
    RAGE = Emoji(pathlib.Path(__file__).parent / "resources/rage.png")
    RAGE_1 = Emoji(pathlib.Path(__file__).parent / "resources/rage_1.png")
    RAGE_2 = Emoji(pathlib.Path(__file__).parent / "resources/rage_2.png")
    RAGE_3 = Emoji(pathlib.Path(__file__).parent / "resources/rage_3.png")
    RAGE_4 = Emoji(pathlib.Path(__file__).parent / "resources/rage_4.png")
    RAILWAY_CAR = Emoji(pathlib.Path(__file__).parent / "resources/railway_car.png")
    RAINBOW = Emoji(pathlib.Path(__file__).parent / "resources/rainbow.png")
    RAISED_HAND = Emoji(pathlib.Path(__file__).parent / "resources/raised_hand.png")
    RAISED_HANDS = Emoji(pathlib.Path(__file__).parent / "resources/raised_hands.png")
    RAISING_HAND = Emoji(pathlib.Path(__file__).parent / "resources/raising_hand.png")
    RAM = Emoji(pathlib.Path(__file__).parent / "resources/ram.png")
    RAMEN = Emoji(pathlib.Path(__file__).parent / "resources/ramen.png")
    RAT = Emoji(pathlib.Path(__file__).parent / "resources/rat.png")
    RECYCLE = Emoji(pathlib.Path(__file__).parent / "resources/recycle.png")
    RED_CAR = Emoji(pathlib.Path(__file__).parent / "resources/red_car.png")
    RED_CIRCLE = Emoji(pathlib.Path(__file__).parent / "resources/red_circle.png")
    REGISTERED = Emoji(pathlib.Path(__file__).parent / "resources/registered.png")
    RELAXED = Emoji(pathlib.Path(__file__).parent / "resources/relaxed.png")
    RELIEVED = Emoji(pathlib.Path(__file__).parent / "resources/relieved.png")
    REPEAT = Emoji(pathlib.Path(__file__).parent / "resources/repeat.png")
    REPEAT_ONCE = Emoji(pathlib.Path(__file__).parent / "resources/repeat_once.png")
    RESTROOM = Emoji(pathlib.Path(__file__).parent / "resources/restroom.png")
    REVOLVING_HEARTS = Emoji(pathlib.Path(__file__).parent / "resources/revolving_hearts.png")
    REWIND = Emoji(pathlib.Path(__file__).parent / "resources/rewind.png")
    RIBBON = Emoji(pathlib.Path(__file__).parent / "resources/ribbon.png")
    RICE = Emoji(pathlib.Path(__file__).parent / "resources/rice.png")
    RICE_BALL = Emoji(pathlib.Path(__file__).parent / "resources/rice_ball.png")
    RICE_CRACKER = Emoji(pathlib.Path(__file__).parent / "resources/rice_cracker.png")
    RICE_SCENE = Emoji(pathlib.Path(__file__).parent / "resources/rice_scene.png")
    RING = Emoji(pathlib.Path(__file__).parent / "resources/ring.png")
    ROCKET = Emoji(pathlib.Path(__file__).parent / "resources/rocket.png")
    ROLLER_COASTER = Emoji(pathlib.Path(__file__).parent / "resources/roller_coaster.png")
    ROOSTER = Emoji(pathlib.Path(__file__).parent / "resources/rooster.png")
    ROSE = Emoji(pathlib.Path(__file__).parent / "resources/rose.png")
    ROTATING_LIGHT = Emoji(pathlib.Path(__file__).parent / "resources/rotating_light.png")
    ROUND_PUSHPIN = Emoji(pathlib.Path(__file__).parent / "resources/round_pushpin.png")
    ROWBOAT = Emoji(pathlib.Path(__file__).parent / "resources/rowboat.png")
    RU = Emoji(pathlib.Path(__file__).parent / "resources/ru.png")
    RUGBY_FOOTBALL = Emoji(pathlib.Path(__file__).parent / "resources/rugby_football.png")
    RUNNER = Emoji(pathlib.Path(__file__).parent / "resources/runner.png")
    RUNNING = Emoji(pathlib.Path(__file__).parent / "resources/running.png")
    RUNNING_SHIRT_WITH_SASH = Emoji(pathlib.Path(__file__).parent / "resources/running_shirt_with_sash.png")
    SAGITTARIUS = Emoji(pathlib.Path(__file__).parent / "resources/sagittarius.png")
    SAILBOAT = Emoji(pathlib.Path(__file__).parent / "resources/sailboat.png")
    SAKE = Emoji(pathlib.Path(__file__).parent / "resources/sake.png")
    SANDAL = Emoji(pathlib.Path(__file__).parent / "resources/sandal.png")
    SANTA = Emoji(pathlib.Path(__file__).parent / "resources/santa.png")
    SATELITE = Emoji(pathlib.Path(__file__).parent / "resources/satelite.png")
    SATISFIED = Emoji(pathlib.Path(__file__).parent / "resources/satisfied.png")
    SAXOPHONE = Emoji(pathlib.Path(__file__).parent / "resources/saxophone.png")
    SCHOOL = Emoji(pathlib.Path(__file__).parent / "resources/school.png")
    SCHOOL_SATCHEL = Emoji(pathlib.Path(__file__).parent / "resources/school_satchel.png")
    SCISSORS = Emoji(pathlib.Path(__file__).parent / "resources/scissors.png")
    SCORPIUS = Emoji(pathlib.Path(__file__).parent / "resources/scorpius.png")
    SCREAM = Emoji(pathlib.Path(__file__).parent / "resources/scream.png")
    SCREAM_CAT = Emoji(pathlib.Path(__file__).parent / "resources/scream_cat.png")
    SCROLL = Emoji(pathlib.Path(__file__).parent / "resources/scroll.png")
    SEAT = Emoji(pathlib.Path(__file__).parent / "resources/seat.png")
    SEEDLING = Emoji(pathlib.Path(__file__).parent / "resources/seedling.png")
    SEE_NO_EVIL = Emoji(pathlib.Path(__file__).parent / "resources/see_no_evil.png")
    SEVEN = Emoji(pathlib.Path(__file__).parent / "resources/seven.png")
    SHAVED_ICE = Emoji(pathlib.Path(__file__).parent / "resources/shaved_ice.png")
    SHEEP = Emoji(pathlib.Path(__file__).parent / "resources/sheep.png")
    SHELL = Emoji(pathlib.Path(__file__).parent / "resources/shell.png")
    SHIP = Emoji(pathlib.Path(__file__).parent / "resources/ship.png")
    SHIPIT = Emoji(pathlib.Path(__file__).parent / "resources/shipit.png")
    SHIRT = Emoji(pathlib.Path(__file__).parent / "resources/shirt.png")
    SHIT = Emoji(pathlib.Path(__file__).parent / "resources/shit.png")
    SHOE = Emoji(pathlib.Path(__file__).parent / "resources/shoe.png")
    SHOWER = Emoji(pathlib.Path(__file__).parent / "resources/shower.png")
    SIGNAL_STRENGTH = Emoji(pathlib.Path(__file__).parent / "resources/signal_strength.png")
    SIX = Emoji(pathlib.Path(__file__).parent / "resources/six.png")
    SIX_POINTED_STAR = Emoji(pathlib.Path(__file__).parent / "resources/six_pointed_star.png")
    SKI = Emoji(pathlib.Path(__file__).parent / "resources/ski.png")
    SKULL = Emoji(pathlib.Path(__file__).parent / "resources/skull.png")
    SLEEPING = Emoji(pathlib.Path(__file__).parent / "resources/sleeping.png")
    SLEEPY = Emoji(pathlib.Path(__file__).parent / "resources/sleepy.png")
    SLOT_MACHINE = Emoji(pathlib.Path(__file__).parent / "resources/slot_machine.png")
    SMALL_BLUE_DIAMOND = Emoji(pathlib.Path(__file__).parent / "resources/small_blue_diamond.png")
    SMALL_ORANGE_DIAMOND = Emoji(pathlib.Path(__file__).parent / "resources/small_orange_diamond.png")
    SMALL_RED_TRIANGLE_DOWN = Emoji(pathlib.Path(__file__).parent / "resources/small_red_triangle_down.png")
    SMALL_RED_TRIANGLE_UP = Emoji(pathlib.Path(__file__).parent / "resources/small_red_triangle_up.png")
    SMILE = Emoji(pathlib.Path(__file__).parent / "resources/smile.png")
    SMILEY = Emoji(pathlib.Path(__file__).parent / "resources/smiley.png")
    SMILEY_CAT = Emoji(pathlib.Path(__file__).parent / "resources/smiley_cat.png")
    SMILE_CAT = Emoji(pathlib.Path(__file__).parent / "resources/smile_cat.png")
    SMILING_IMP = Emoji(pathlib.Path(__file__).parent / "resources/smiling_imp.png")
    SMIRK = Emoji(pathlib.Path(__file__).parent / "resources/smirk.png")
    SMIRK_CAT = Emoji(pathlib.Path(__file__).parent / "resources/smirk_cat.png")
    SMOKING = Emoji(pathlib.Path(__file__).parent / "resources/smoking.png")
    SNAIL = Emoji(pathlib.Path(__file__).parent / "resources/snail.png")
    SNAKE = Emoji(pathlib.Path(__file__).parent / "resources/snake.png")
    SNOWBOARDER = Emoji(pathlib.Path(__file__).parent / "resources/snowboarder.png")
    SNOWFLAKE = Emoji(pathlib.Path(__file__).parent / "resources/snowflake.png")
    SNOWMAN = Emoji(pathlib.Path(__file__).parent / "resources/snowman.png")
    SOB = Emoji(pathlib.Path(__file__).parent / "resources/sob.png")
    SOCCER = Emoji(pathlib.Path(__file__).parent / "resources/soccer.png")
    SOON = Emoji(pathlib.Path(__file__).parent / "resources/soon.png")
    SOS = Emoji(pathlib.Path(__file__).parent / "resources/sos.png")
    SOUND = Emoji(pathlib.Path(__file__).parent / "resources/sound.png")
    SPACE_INVADER = Emoji(pathlib.Path(__file__).parent / "resources/space_invader.png")
    SPADES = Emoji(pathlib.Path(__file__).parent / "resources/spades.png")
    SPAGHETTI = Emoji(pathlib.Path(__file__).parent / "resources/spaghetti.png")
    SPARKLER = Emoji(pathlib.Path(__file__).parent / "resources/sparkler.png")
    SPARKLES = Emoji(pathlib.Path(__file__).parent / "resources/sparkles.png")
    SPARKLING_HEART = Emoji(pathlib.Path(__file__).parent / "resources/sparkling_heart.png")
    SPEAKER = Emoji(pathlib.Path(__file__).parent / "resources/speaker.png")
    SPEAK_NO_EVIL = Emoji(pathlib.Path(__file__).parent / "resources/speak_no_evil.png")
    SPEECH_BALLOON = Emoji(pathlib.Path(__file__).parent / "resources/speech_balloon.png")
    SPEEDBOAT = Emoji(pathlib.Path(__file__).parent / "resources/speedboat.png")
    STAR = Emoji(pathlib.Path(__file__).parent / "resources/star.png")
    STARS = Emoji(pathlib.Path(__file__).parent / "resources/stars.png")
    STAR_2 = Emoji(pathlib.Path(__file__).parent / "resources/star_2.png")
    STATION = Emoji(pathlib.Path(__file__).parent / "resources/station.png")
    STATUE_OF_LIBERTY = Emoji(pathlib.Path(__file__).parent / "resources/statue_of_liberty.png")
    STEAM_LOCOMOTIVE = Emoji(pathlib.Path(__file__).parent / "resources/steam_locomotive.png")
    STEW = Emoji(pathlib.Path(__file__).parent / "resources/stew.png")
    STRAIGHT_RULER = Emoji(pathlib.Path(__file__).parent / "resources/straight_ruler.png")
    STRAWBERRY = Emoji(pathlib.Path(__file__).parent / "resources/strawberry.png")
    STUCK_OUT_TONGUE = Emoji(pathlib.Path(__file__).parent / "resources/stuck_out_tongue.png")
    STUCK_OUT_TONGUE_CLOSED_EYES = Emoji(pathlib.Path(__file__).parent / "resources/stuck_out_tongue_closed_eyes.png")
    STUCK_OUT_TONGUE_WINKING_EYE = Emoji(pathlib.Path(__file__).parent / "resources/stuck_out_tongue_winking_eye.png")
    SUNFLOWER = Emoji(pathlib.Path(__file__).parent / "resources/sunflower.png")
    SUNGLASSES = Emoji(pathlib.Path(__file__).parent / "resources/sunglasses.png")
    SUNNY = Emoji(pathlib.Path(__file__).parent / "resources/sunny.png")
    SUNRISE = Emoji(pathlib.Path(__file__).parent / "resources/sunrise.png")
    SUNRISE_OVER_MOUNTAINS = Emoji(pathlib.Path(__file__).parent / "resources/sunrise_over_mountains.png")
    SUN_WITH_FACE = Emoji(pathlib.Path(__file__).parent / "resources/sun_with_face.png")
    SURFER = Emoji(pathlib.Path(__file__).parent / "resources/surfer.png")
    SUSHI = Emoji(pathlib.Path(__file__).parent / "resources/sushi.png")
    SUSPECT = Emoji(pathlib.Path(__file__).parent / "resources/suspect.png")
    SUSPENSION_RAILWAY = Emoji(pathlib.Path(__file__).parent / "resources/suspension_railway.png")
    SWEAT = Emoji(pathlib.Path(__file__).parent / "resources/sweat.png")
    SWEAT_DROPS = Emoji(pathlib.Path(__file__).parent / "resources/sweat_drops.png")
    SWEAT_SMILE = Emoji(pathlib.Path(__file__).parent / "resources/sweat_smile.png")
    SWEET_POTATO = Emoji(pathlib.Path(__file__).parent / "resources/sweet_potato.png")
    SWIMMER = Emoji(pathlib.Path(__file__).parent / "resources/swimmer.png")
    SYMBOLS = Emoji(pathlib.Path(__file__).parent / "resources/symbols.png")
    SYRINGE = Emoji(pathlib.Path(__file__).parent / "resources/syringe.png")
    TADA = Emoji(pathlib.Path(__file__).parent / "resources/tada.png")
    TANABATA_TREE = Emoji(pathlib.Path(__file__).parent / "resources/tanabata_tree.png")
    TANGERINE = Emoji(pathlib.Path(__file__).parent / "resources/tangerine.png")
    TAURUS = Emoji(pathlib.Path(__file__).parent / "resources/taurus.png")
    TAXI = Emoji(pathlib.Path(__file__).parent / "resources/taxi.png")
    TEA = Emoji(pathlib.Path(__file__).parent / "resources/tea.png")
    TELEPHONE = Emoji(pathlib.Path(__file__).parent / "resources/telephone.png")
    TELEPHONE_RECEIVER = Emoji(pathlib.Path(__file__).parent / "resources/telephone_receiver.png")
    TELESCOPE = Emoji(pathlib.Path(__file__).parent / "resources/telescope.png")
    TENNIS = Emoji(pathlib.Path(__file__).parent / "resources/tennis.png")
    TENT = Emoji(pathlib.Path(__file__).parent / "resources/tent.png")
    THOUGHT_BALLOON = Emoji(pathlib.Path(__file__).parent / "resources/thought_balloon.png")
    THREE = Emoji(pathlib.Path(__file__).parent / "resources/three.png")
    THUMBSDOWN = Emoji(pathlib.Path(__file__).parent / "resources/thumbsdown.png")
    THUMBSUP = Emoji(pathlib.Path(__file__).parent / "resources/thumbsup.png")
    TICKET = Emoji(pathlib.Path(__file__).parent / "resources/ticket.png")
    TIGER = Emoji(pathlib.Path(__file__).parent / "resources/tiger.png")
    TIGER_2 = Emoji(pathlib.Path(__file__).parent / "resources/tiger_2.png")
    TIRED_FACE = Emoji(pathlib.Path(__file__).parent / "resources/tired_face.png")
    TM = Emoji(pathlib.Path(__file__).parent / "resources/tm.png")
    TOILET = Emoji(pathlib.Path(__file__).parent / "resources/toilet.png")
    TOKYO_TOWER = Emoji(pathlib.Path(__file__).parent / "resources/tokyo_tower.png")
    TOMATO = Emoji(pathlib.Path(__file__).parent / "resources/tomato.png")
    TONGUE = Emoji(pathlib.Path(__file__).parent / "resources/tongue.png")
    TOP = Emoji(pathlib.Path(__file__).parent / "resources/top.png")
    TOPHAT = Emoji(pathlib.Path(__file__).parent / "resources/tophat.png")
    TRACTOR = Emoji(pathlib.Path(__file__).parent / "resources/tractor.png")
    TRAFFIC_LIGHT = Emoji(pathlib.Path(__file__).parent / "resources/traffic_light.png")
    TRAIN = Emoji(pathlib.Path(__file__).parent / "resources/train.png")
    TRAIN_2 = Emoji(pathlib.Path(__file__).parent / "resources/train_2.png")
    TRAM = Emoji(pathlib.Path(__file__).parent / "resources/tram.png")
    TRIANGULAR_FLAG_ON_POST = Emoji(pathlib.Path(__file__).parent / "resources/triangular_flag_on_post.png")
    TRIANGULAR_RULER = Emoji(pathlib.Path(__file__).parent / "resources/triangular_ruler.png")
    TRIDENT = Emoji(pathlib.Path(__file__).parent / "resources/trident.png")
    TRIUMPH = Emoji(pathlib.Path(__file__).parent / "resources/triumph.png")
    TROLLEYBUS = Emoji(pathlib.Path(__file__).parent / "resources/trolleybus.png")
    TROLLFACE = Emoji(pathlib.Path(__file__).parent / "resources/trollface.png")
    TROPHY = Emoji(pathlib.Path(__file__).parent / "resources/trophy.png")
    TROPICAL_DRINK = Emoji(pathlib.Path(__file__).parent / "resources/tropical_drink.png")
    TROPICAL_FISH = Emoji(pathlib.Path(__file__).parent / "resources/tropical_fish.png")
    TRUCK = Emoji(pathlib.Path(__file__).parent / "resources/truck.png")
    TRUMPET = Emoji(pathlib.Path(__file__).parent / "resources/trumpet.png")
    TSHIRT = Emoji(pathlib.Path(__file__).parent / "resources/tshirt.png")
    TULIP = Emoji(pathlib.Path(__file__).parent / "resources/tulip.png")
    TURTLE = Emoji(pathlib.Path(__file__).parent / "resources/turtle.png")
    TV = Emoji(pathlib.Path(__file__).parent / "resources/tv.png")
    TWO = Emoji(pathlib.Path(__file__).parent / "resources/two.png")
    TWO_HEARTS = Emoji(pathlib.Path(__file__).parent / "resources/two_hearts.png")
    TWO_MEN_HOLDING_HANDS = Emoji(pathlib.Path(__file__).parent / "resources/two_men_holding_hands.png")
    TWO_WOMEN_HOLDING_HANDS = Emoji(pathlib.Path(__file__).parent / "resources/two_women_holding_hands.png")
    UK = Emoji(pathlib.Path(__file__).parent / "resources/uk.png")
    UMBRELLA = Emoji(pathlib.Path(__file__).parent / "resources/umbrella.png")
    UNAMUSED = Emoji(pathlib.Path(__file__).parent / "resources/unamused.png")
    UNDERAGE = Emoji(pathlib.Path(__file__).parent / "resources/underage.png")
    UNLOCK = Emoji(pathlib.Path(__file__).parent / "resources/unlock.png")
    UP = Emoji(pathlib.Path(__file__).parent / "resources/up.png")
    US = Emoji(pathlib.Path(__file__).parent / "resources/us.png")
    V = Emoji(pathlib.Path(__file__).parent / "resources/v.png")
    VERTICAL_TRAFFIC_LIGHT = Emoji(pathlib.Path(__file__).parent / "resources/vertical_traffic_light.png")
    VHS = Emoji(pathlib.Path(__file__).parent / "resources/vhs.png")
    VIBRATION_MODE = Emoji(pathlib.Path(__file__).parent / "resources/vibration_mode.png")
    VIDEO_CAMERA = Emoji(pathlib.Path(__file__).parent / "resources/video_camera.png")
    VIDEO_GAME = Emoji(pathlib.Path(__file__).parent / "resources/video_game.png")
    VIOLIN = Emoji(pathlib.Path(__file__).parent / "resources/violin.png")
    VIRGO = Emoji(pathlib.Path(__file__).parent / "resources/virgo.png")
    VOLCANO = Emoji(pathlib.Path(__file__).parent / "resources/volcano.png")
    VS = Emoji(pathlib.Path(__file__).parent / "resources/vs.png")
    WALKING = Emoji(pathlib.Path(__file__).parent / "resources/walking.png")
    WANING_CRESENT_MOON = Emoji(pathlib.Path(__file__).parent / "resources/waning_cresent_moon.png")
    WANING_GIBBOUS_MOON = Emoji(pathlib.Path(__file__).parent / "resources/waning_gibbous_moon.png")
    WARNING = Emoji(pathlib.Path(__file__).parent / "resources/warning.png")
    WATCH = Emoji(pathlib.Path(__file__).parent / "resources/watch.png")
    WATERMELON = Emoji(pathlib.Path(__file__).parent / "resources/watermelon.png")
    WATER_BUFFALO = Emoji(pathlib.Path(__file__).parent / "resources/water_buffalo.png")
    WAVE = Emoji(pathlib.Path(__file__).parent / "resources/wave.png")
    WAVY_DASH = Emoji(pathlib.Path(__file__).parent / "resources/wavy_dash.png")
    WAXING_CRESENT_MOON = Emoji(pathlib.Path(__file__).parent / "resources/waxing_cresent_moon.png")
    WAXING_GIBBOUS_MOON = Emoji(pathlib.Path(__file__).parent / "resources/waxing_gibbous_moon.png")
    WC = Emoji(pathlib.Path(__file__).parent / "resources/wc.png")
    WEARY = Emoji(pathlib.Path(__file__).parent / "resources/weary.png")
    WEDDING = Emoji(pathlib.Path(__file__).parent / "resources/wedding.png")
    WHALE = Emoji(pathlib.Path(__file__).parent / "resources/whale.png")
    WHALE_2 = Emoji(pathlib.Path(__file__).parent / "resources/whale_2.png")
    WHITE_CIRCLE = Emoji(pathlib.Path(__file__).parent / "resources/white_circle.png")
    WHITE_FLOWER = Emoji(pathlib.Path(__file__).parent / "resources/white_flower.png")
    WHITE_SQUARE_BUTTON = Emoji(pathlib.Path(__file__).parent / "resources/white_square_button.png")
    WIND_CHIME = Emoji(pathlib.Path(__file__).parent / "resources/wind_chime.png")
    WINE_GLASS = Emoji(pathlib.Path(__file__).parent / "resources/wine_glass.png")
    WINK = Emoji(pathlib.Path(__file__).parent / "resources/wink.png")
    WOLF = Emoji(pathlib.Path(__file__).parent / "resources/wolf.png")
    WOMAN = Emoji(pathlib.Path(__file__).parent / "resources/woman.png")
    WOMANS_CLOTHES = Emoji(pathlib.Path(__file__).parent / "resources/womans_clothes.png")
    WOMANS_HAT = Emoji(pathlib.Path(__file__).parent / "resources/womans_hat.png")
    WOMENS = Emoji(pathlib.Path(__file__).parent / "resources/womens.png")
    WORRIED = Emoji(pathlib.Path(__file__).parent / "resources/worried.png")
    WRENCH = Emoji(pathlib.Path(__file__).parent / "resources/wrench.png")
    X = Emoji(pathlib.Path(__file__).parent / "resources/x.png")
    YELLOW_HEART = Emoji(pathlib.Path(__file__).parent / "resources/yellow_heart.png")
    YEN = Emoji(pathlib.Path(__file__).parent / "resources/yen.png")
    YUM = Emoji(pathlib.Path(__file__).parent / "resources/yum.png")
    ZAP = Emoji(pathlib.Path(__file__).parent / "resources/zap.png")
    ZERO = Emoji(pathlib.Path(__file__).parent / "resources/zero.png")
    ZZZ = Emoji(pathlib.Path(__file__).parent / "resources/zzz.png")
    # fmt: on
