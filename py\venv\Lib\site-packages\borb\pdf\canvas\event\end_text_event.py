#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
    This implementation of Event is triggered when an ET instruction is being processed.
"""
from borb.pdf.canvas.event.event_listener import Event


class EndTextEvent(Event):
    """
    This implementation of Event is triggered when an ET instruction is being processed.
    """

    #
    # CONSTRUCTOR
    #

    #
    # PRIVATE
    #

    #
    # PUBLIC
    #

    pass
