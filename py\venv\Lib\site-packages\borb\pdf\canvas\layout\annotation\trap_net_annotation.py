#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
A trap network annotation (PDF 1.3) may be used to define the trapping characteristics for a page of a PDF
document.
"""
from borb.pdf.canvas.layout.annotation.annotation import Annotation


class TrapNetAnnotation(Annotation):
    """
    A trap network annotation (PDF 1.3) may be used to define the trapping characteristics for a page of a PDF
    document.
    """

    #
    # CONSTRUCTOR
    #

    def __init__(self):
        super(TrapNetAnnotation, self).__init__()
        # TODO

    #
    # PRIVATE
    #

    #
    # PUBLIC
    #
